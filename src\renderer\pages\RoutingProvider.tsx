import {
  createBrowserRouter,
  // Navigate,
  RouterProvider,
} from "react-router-dom";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import Login from "./Login";
import AppContainer from "./AppContainer";
import { routePaths } from "../utils/constant";
import { Amplify, Auth } from "aws-amplify";
import axios from "axios";
import List from "./User/List";
import Create from "./User/Create";
import Approve from "./Bnpl/ApproveReject/ApproveReject";
import LimitIncrease from "./Bnpl/LimitIncrease";
import ApproveReject from "./ResaleCertificate/ApproveReject";
import Layout from "./Layout";
import ReferenceData from "./ReferenceData";
import AchCreditOrder from "./AchCredit/AchCreditOrder";
import AchCreditPayment from "./AchCredit/AchCreditPayment/AchCreditPayment";
import UpdateQtySendInvoiceEmail from "./UpdateQtySendInvoiceEmail";
import CustomSetting from "./Deposit/CustomSetting";
import GlobalSetting from "./Deposit/GlobalSetting";
import Setting from "./Setting";
import CancelOrder from "./CancelOrder";
// import DyspatchTemplate from "./DyspatchTemplate";
import DyspatchTemplateEditor from "./DyspatchTemplateEditor";
import GenerateEmail from "./GenerateEmail";
import Pending from "./User/Pending/Pending";
import MakeApayment from "./Cass/MakeApayment";
import ReferenceSetting from "./ReferenceSetting/ReferenceSetting";
import CloseOrderAndReplenish from "./Bnpl/CloseOrderAndReplenish";
import RestartWebSocket from "./WebSocket";
import BroadcastNotifications from "./BroadcastNotifications";
import PendingCompanyList from "./User/PendingCompanyList";
import RemoveSalesTax from "./RemoveSalesTax/RemoveSalesTax";
import CloseOrder from "./AchCredit/CloseOrder/CloseOrder";
import ConvertAchToBnpl from "./ConvertAchToBnpl";
import SafeUploads from "./SafeUploads/SafeUploads";
import CohortList from "./User/Cohort";
import { SafeImgixImageKit } from "./SafeImgixImageKit/SafeImgixImageKit";
import RestartNodeMessageCron from "./NodeMessageCron";
import PhpMakeApayment from "./Cass/PhpMakeApayment";
import UpdateSecurity from "./UpdateSecurity";
import CassMappingTransaction from "./Cass/CassMappingTransaction";
import DiscountedUsers from "./User/DiscountedUsers/DiscountedUsers";
import NodeCreate from "./User/Create/NodeCreate";
import Tnc from "./Tnc/Tnc";
import CapgoUpdateURL from "./CapgoUpdateURL/CapgoUpdateURL";
import EmailAttachments from "./EmailAttachments";
import VideoUploads from "./VideoUploads";
import EdtiSection from "./EditSection/EdtiSection";
import PreapprovedEmails from "./User/PreApprovedEmails/PreApprovedEmails";
import HolidayList from "./HolidayList/HolidayList";
import Chats from "./Chats/Chats";
import CassTransactionSubmissionStatus from "./Cass/CassTransactionSubmissionStatus/CassTransactionSubmissionStatus";
import BomList from "./Bom/BomList";
import LogReader from "./LogReader";
import FetchUserLogs from "./FetchLogs/FetchLogs";
import ExternalApisAccess from "./ExternalApisAccess";
import EditPricingBrackets from "./EditPricingBrackets/EditPricingBrackets";
import GameSettings from "./GameSettings";
import CreditReplenishment from "./CreditReplenishment";
import ReferenceDataUpload from "./ReferenceDataUpload";
import RevertReferenceProductData from "./RevertReferenceProductData";
import Screens from "./AdminVideoTooltip/Screens";
import { NotificationProvider } from "../contexts/NotificationContext";
import HotspotsPage from "./AdminVideoTooltip/Hotspots";
import MappingsPage from "./AdminVideoTooltip/Mappings";
import { createTheme, ThemeProvider } from "@mui/material";
import SheetCompare from "./SheetCompare/SheetCompare";
import ViewNexusData from "./ViewNexusData/ViewNexusData";
import BuyerPayments from "./BuyerPayments";

Amplify.configure({
  Auth: {
    region: import.meta.env.VITE_AWS_COGNITO_REGION,
    userPoolId: import.meta.env.VITE_AWS_COGNITO_USER_POOL_ID,
    userPoolWebClientId: import.meta.env.VITE_AWS_COGNITO_USER_POOL_WEB_CLIENT_ID,
    cookieStorage: {
      domain: import.meta.env.VITE_AWS_COGNITO_DOMAIN,
      path: import.meta.env.VITE_AWS_COGNITO_PATH,
      expires: Number(import.meta.env.VITE_AWS_COGNITO_EXPIRES),
      sameSite: import.meta.env.VITE_AWS_COGNITO_SAME_SITE,
      secure: Boolean(import.meta.env.VITE_AWS_COGNITO_SECURE),
    },
  },
  Analytics: { disabled: Boolean(import.meta.env.VITE_AWS_COGNITO_ANALYTICS_DISABLED) },
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

const router = createBrowserRouter([
  {
    path: "/",
    element: <AppContainer />,
    children: [
      {
        path: routePaths.login,
        element: <Login />,
      },
      {
        path: `${routePaths.layout}`,
        element: <Layout />,
        children: [
          {
            path: routePaths.user,
            children: [
              {
                path: routePaths.create,
                element: <Create />,
              },
              {
                path: routePaths.list,
                element: <List />,
              },
              {
                path: routePaths.preApproved,
                element: <PreapprovedEmails />,
              },
              {
                path: routePaths.pending,
                element: <Pending />,
              },
              {
                path: routePaths.pendingCompanyList,
                element: <PendingCompanyList />,
              },
              {
                path: routePaths.cohortList,
                element: <CohortList />,
              },
              {
                path: routePaths.discountedUsers,
                element: <DiscountedUsers />,
              }
            ],
          },
          {
            path: routePaths.bnpl,
            children: [
              {
                path: routePaths.bnplApproveReject,
                element: <Approve />,
              },
              {
                path: routePaths.bnplLimitIncrease,
                element: <LimitIncrease />,
              },
              {
                path: routePaths.bnplCloseOrderAndReplenish,
                element: <CloseOrderAndReplenish />,
              },
              {
                path: routePaths.creditReplenishment,
                element: <CreditReplenishment />,
                children: [],
              },
            ],
          },
          {
            path: routePaths.resaleCertificate,
            children: [
              {
                path: routePaths.resaleCertificateApproveReject,
                element: <ApproveReject />,
              },
            ],
          },
          {
            path: routePaths.referecneDataV1,
            element: <ReferenceData />,
            children: [],
          },
          {
            path: routePaths.referecneData,
            element: <ReferenceDataUpload />,
            children: [],
          },
          {
            path: routePaths.revertReferenceData,
            element: <RevertReferenceProductData />,
            children: [],
          },
          {
            path: routePaths.achCredit,
            children: [
              {
                path: routePaths.achCreditOrders,
                element: <AchCreditOrder />,
              },
              {
                path: routePaths.achCreditPayment,
                element: <AchCreditPayment />,
              },
              {
                path: routePaths.achCreditCloseOrder,
                element: <CloseOrder />,
              },
            ],
          },
          {
            path: routePaths.sendInvoiceEmail,
            element: <UpdateQtySendInvoiceEmail />,
            children: [],
          },
          {
            path: routePaths.generateEmail,
            element: <GenerateEmail />,
            children: [],
          },
          {
            path: routePaths.broadcastNotifications,
            element: <BroadcastNotifications />,
            children: [],
          },
          {
            path: routePaths.depositSettings,
            children: [
              {
                path: routePaths.globalSetting,
                element: <GlobalSetting />,
              },
              {
                path: routePaths.customSetting,
                element: <CustomSetting />,
              },
            ],
          },
          {
            path: routePaths.setting,
            children: [
              {
                path: routePaths.orderCancellation,
                element: <Setting />,
              },
              {
                path: routePaths.referenceSetting,
                element: <ReferenceSetting />,
              },
            ],
          },
          {
            path: routePaths.cancelOrder,
            element: <CancelOrder />,
          },
          {
            path: routePaths.dyspatchTemplate,
            // element: <DyspatchTemplate />,
            element: <DyspatchTemplateEditor />,
          },
          {
            path: routePaths.bryzosPay,
            children: [
              {
                path: routePaths.makeApayment,
                element: <MakeApayment />,
              },
              {
                path: routePaths.phpMakeApayment,
                element: <PhpMakeApayment />,
              },
              {
                path: routePaths.cassMappingTransactionWithPo,
                element: <CassMappingTransaction />,
              },
              {
                path: routePaths.cassTransactionSubmissionStatus,
                element: <CassTransactionSubmissionStatus />,
              },
              {
                path: routePaths.buyerPayments,
                element: <BuyerPayments />,
              },
            ],
          },
          {
            path: routePaths.restartWebSocket,
            element: <RestartWebSocket />,
            children: [],
          },
          {
            path: routePaths.restartNodeMessageCron,
            element: <RestartNodeMessageCron />,
            children: [],
          },
          {
            path: routePaths.salesTax,
            children: [
              {
                path: routePaths.removeSalesTax,
                element: <RemoveSalesTax />,
                children: [],
              },
              {
                path: routePaths.viewNexusThreshold,
                element: <ViewNexusData />,
                children: [],
              },
            ],
          },
          
          {
            path: routePaths.convertAchToBnpl,
            element: <ConvertAchToBnpl />,
            children: [],
          },
          {
            path: routePaths.safeUploads,
            element: <SafeUploads />,
            children: [],
          },
          {
            path: routePaths.safeImgixImageKit,
            element: <SafeImgixImageKit />,
            children: [],
          },
          {
            path: routePaths.updateSecurityToken,
            element: <UpdateSecurity />,
            children: [],
          },
          {
            path: routePaths.tnc,
            element: <Tnc />,
            children: [],
          },
          {
            path: routePaths.capgoUpdate,
            element: <CapgoUpdateURL />
          },
          {
            path: routePaths.invoiceEmailAttachments,
            element: <EmailAttachments />,
            children: [],
          },
          {
            path: routePaths.videoLibrary,
            children: [
              {
                path: routePaths.videoUploads,
                element: <VideoUploads />,
                children: [],
              },
              {
                path: routePaths.videoTag,
                element: <EdtiSection />,
                children: [],
              },
            ],
          },
          {
            path: routePaths.holidayList,
            element: <HolidayList />,
            children: [],
          },
          {
            path: routePaths.bomList,
            element: <BomList />,
            children: [],
          },
          {
            path: routePaths.logReader,
            element: <LogReader />,
            children: [],
          },
          {
            path: routePaths.fetchUserLogs,
            element: <FetchUserLogs />,
            children: [],
          },
          {
            path: routePaths.externalApisAccess,
            element: <ExternalApisAccess />,
            children: [],
          },
          {
            path: routePaths.gameSettings,
            element: <GameSettings />,
            children: [],
          },
          {
            path: routePaths.adminVideoTooltipScreens,
            children: [{
              index: true,
              element: <Screens />,
            },{
              path: ':id/hotspots',
              element: <HotspotsPage />
            },{
              path: ':id/mappings',
              element: <MappingsPage />
            }]
          },
          {
            path: routePaths.chats,
            element: <Chats />,
            children: [],
          },
          {
            path: routePaths.compareSheets,
            element: <SheetCompare />,
          }
        ],
      },
    ],
  },
]);

const theme = createTheme({
  typography: {
    fontFamily: 'Noto Sans, sans-serif',
  },
  palette: {
    primary: {
      main: '#343a40'
    }
  },
  components: {
    MuiButtonBase: {
      defaultProps: {
        disableRipple: true,
        disableTouchRipple: true,
      }
    },
    MuiButton: {
      defaultProps: {
        disableElevation: true,
      },
      styleOverrides: {
        root: {
          textTransform: 'none',
        }
      }
    },
    MuiPaper: {
      defaultProps: {
        elevation: 0
      }
    },
    MuiMenu: {
      defaultProps: {
        slotProps: {
          paper: {
            elevation: 1
          }
        }
      }
    },
  }
})

const RoutingProvider = () => {
  return (
    <QueryClientProvider client={queryClient}>
     <ThemeProvider theme={theme}>
     <NotificationProvider>
     <RouterProvider router={router} />
     <ReactQueryDevtools initialIsOpen={false} position="bottom-right" />
     </NotificationProvider>
     </ThemeProvider>
    </QueryClientProvider>
  );
};

export default RoutingProvider;

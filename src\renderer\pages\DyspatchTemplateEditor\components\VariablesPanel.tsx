import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { useTemplateStore } from '../stores/templateStore';
import { useState } from 'react';
import styles from './VariablesPanel.module.scss';

export function VariablesPanel() {
  const { variables, collections, addVariable, updateVariable, removeVariable, updateCollection } = useTemplateStore();
  const [newVarName, setNewVarName] = useState('');
  const [activeTab, setActiveTab] = useState<'variables' | 'collections'>('variables');
  const [editingCollection, setEditingCollection] = useState<Record<string, string>>({});

  const handleAddVariable = () => {
    if (newVarName.trim()) {
      addVariable({
        name: newVarName.trim(),
        defaultValue: '',
        type: 'text',
      });
      setNewVarName('');
    }
  };

  const getCollectionText = (name: string, data: any[]) => {
    return editingCollection[name] !== undefined
      ? editingCollection[name]
      : JSON.stringify(data, null, 2);
  };

  const handleCollectionChange = (name: string, value: string) => {
    setEditingCollection(prev => ({ ...prev, [name]: value }));
  };

  const handleCollectionBlur = (name: string, jsonText: string) => {
    try {
      const data = JSON.parse(jsonText);
      if (Array.isArray(data)) {
        updateCollection(name, data);
        setEditingCollection(prev => {
          const newState = { ...prev };
          delete newState[name];
          return newState;
        });
      }
    } catch (error) {
      console.error('Invalid JSON:', error);
    }
  };

  return (
    <div className={styles.variablesPanel}>
      <div className={styles.panelTabs}>
        <button
          className={`${styles.tabBtn} ${activeTab === 'variables' ? styles.active : ''}`}
          onClick={() => setActiveTab('variables')}
        >
          Variables
        </button>
        <button
          className={`${styles.tabBtn} ${activeTab === 'collections' ? styles.active : ''}`}
          onClick={() => setActiveTab('collections')}
        >
          Collections
        </button>
      </div>

      <div className={styles.panelContent}>
        {activeTab === 'variables' && (
          <>
            <div className={styles.addVariable}>
              <input
                type="text"
                placeholder="Variable name"
                value={newVarName}
                onChange={(e) => setNewVarName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddVariable()}
              />
              <button onClick={handleAddVariable} disabled={!newVarName.trim()}>
                <AddIcon sx={{ fontSize: 16 }} />
              </button>
            </div>

            <div className={styles.variablesList}>
              {variables.map((variable) => (
                <div key={variable.name} className={styles.variableItem}>
                  <div className={styles.variableName}>{`{{${variable.name}}}`}</div>
                  <input
                    type="text"
                    className={styles.variableValue}
                    placeholder="Default value"
                    value={variable.defaultValue}
                    onChange={(e) =>
                      updateVariable(variable.name, { defaultValue: e.target.value })
                    }
                  />
                  <button
                    className={styles.deleteBtn}
                    onClick={() => removeVariable(variable.name)}
                  >
                    <DeleteIcon sx={{ fontSize: 14 }} />
                  </button>
                </div>
              ))}

              {variables.length === 0 && (
                <div className={styles.emptyVariables}>
                  <p>No variables detected</p>
                  <p className={styles.hint}>
                    Variables in {`{{ curly_braces }}`} will be automatically detected
                  </p>
                </div>
              )}
            </div>
          </>
        )}

        {activeTab === 'collections' && (
          <div className={styles.collectionsList}>
            {Object.entries(collections).map(([name, data]) => (
              <div key={name} className={styles.collectionItem}>
                <h4>Collection: {name}</h4>
                <textarea
                  className={styles.collectionJson}
                  placeholder={`JSON array for ${name} collection`}
                  value={getCollectionText(name, data)}
                  onChange={(e) => handleCollectionChange(name, e.target.value)}
                  onBlur={(e) => handleCollectionBlur(name, e.target.value)}
                  rows={8}
                  spellCheck={false}
                />
                <p className={styles.collectionHint}>
                  Used in: {`{% for item in ${name} %}`}
                </p>
              </div>
            ))}

            {Object.keys(collections).length === 0 && (
              <div className={styles.emptyCollections}>
                <p>No collections detected</p>
                <p className={styles.hint}>
                  Liquid loops like {`{% for item in items %}`} will be automatically detected
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

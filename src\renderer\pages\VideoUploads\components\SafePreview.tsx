import React, { useEffect, useState, useRef } from "react";
import styles from "../VideoUploads.module.scss";
import VideoPlayer from "../../../components/VideoPlayer";
import Modal from "@mui/material/Modal";
import { Box } from "@mui/material";
import downloadBryzos from "../../../../assests/images/download-bryzos-app.png";
import logo from "../../../../assests/images/logo.png";
import { ReactComponent as CloseIcon } from "../../../../assests/images/closePop.svg";
import { ReactComponent as PlayIcon } from "../../../../assests/images/VideoPlay-Icon.svg";
import { introVideoCaptionURL } from "../../../utils/constant";
import clsx from "clsx";
import { ReactComponent as ShareVideo } from "../../../../assests/images/SafeShareVideo.svg"
import DotPulseLoader from "../../../components/DotPulseLoader/DotPulseLoader";

function SafePreview({ queryData , extraSubtitle }: any) {
  const [currVideo, setCurrVideo] = useState("");
  const [title, setTitle] = useState("");
  const [safeVideo, setSafeVideo] = useState("");
  const [open, setOpen] = useState(false);
  const safevideoRef = useRef();
  const videoRef = useRef();
  const [tags, setTags] = useState("");
  const [videoData, setVideoData] = useState();
  const [currTag, setCurrTag] = useState(null);
  const [currIndex, setCurrIndex] = useState(0);
  const [disabledNextBtn,setDisableNextBtn] = useState()
  const [isPlayingPrev,setIsPlayingPrev] = useState(true)
  const [currThumbnail,setCurrThumbnail] = useState("")
  const [isExpanded, setIsExpanded] = useState(false);
  const characterLimit = 140; 
  const [isPageLoading , setIsPageLoading] = useState(true);
  const [safeThumbnail , setSafeThumbnail] = useState("");
  const [isSafeIntroVisible , setIsSafeIntroVisible] = useState(true);
  const [currSubtitle , setCurrSubtitle] = useState('');
  const [subtitlProperty, setSubtitlProperty] = useState({});
  const [subtitlePropertyforSafe, setSubtitlePropertyforSafe] = useState({});
  const [safeSubtitle , setSafeSubtitle] = useState("")
  const [targetedVideo,setTargetedVideo] = useState(null)
  const [imageLoading,setImageLoading] = useState(true)

  const handleToggle = () => {
      setIsExpanded(prev => !prev);
  };

  const getDisplayText = () => {
    if (currVideo || currThumbnail) {
        const text = queryData.videoData[currTag?.query_param][currIndex]?.description;
        if (isExpanded) return text;
          if (text?.length > characterLimit) {
            return text?.slice(0, characterLimit) + '...';
        }
        return text;
    }
  };

  useEffect(()=>{
    setImageLoading(true)
  },[currThumbnail])

  useEffect(() => {
    if (queryData?.videoData) {
      let isSafeIntroVisible = true;

      queryData.tag.forEach((currTag , index) => {
          if((currTag.query_param === "safe-intro") && !(currTag.show_on_safe)) {
               isSafeIntroVisible = false;
               setIsSafeIntroVisible(false);
          }
      })

      if (queryData?.videoData?.["safe-intro"]?.length && isSafeIntroVisible) {
        const filteredSafeVideos = queryData?.videoData?.["safe-intro"]?.filter(
          (video) => video?.show_on_ui === true || video?.show_on_ui === 1
        ); 
        if (filteredSafeVideos?.length) {
          setSafeVideo(filteredSafeVideos[0]["video_s3_url"]);
          setSafeThumbnail(filteredSafeVideos[0]["thumbnail_s3_url_map"]?.["intro_desktop"]);
          if(filteredSafeVideos[0]?.isNew) {
            setSubtitlePropertyforSafe({"captionBlobUrl":filteredSafeVideos[0].subtitle_s3_url})
          } else {
            setSubtitlePropertyforSafe({"captionUrl":filteredSafeVideos[0].subtitle_s3_url})
          }
        }
      } else {
        setSafeVideo("");
      }
      if (queryData?.tag) {
        const filteredSafeTagsList = queryData?.tag?.filter(
          (tag) => tag?.show_on_safe && tag?.query_param !== "safe-intro"
        );
        setTags(filteredSafeTagsList);
        setVideoData(queryData?.videoData);
      }
    }
  }, [queryData]);

  const playVideoWithRefresh = (videoUrl:string)=>{
    setCurrVideo();
    setTimeout(()=>{
      setCurrVideo(videoUrl);
    }, 0);
  }

  const handleClickOpen = () => {
    setIsPlayingPrev(false)
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setIsPlayingPrev(true)
    videoRef?.current?.pause();
    setCurrVideo("");
  };

  const playNextVideo = () => {
    const lengthOfCurrTagArray =
    queryData.videoData[currTag.query_param]?.length;
    const nextIndex = (currIndex + 1) % lengthOfCurrTagArray;
    setCurrIndex(nextIndex);
    setTargetedVideo(queryData.videoData[currTag?.query_param][nextIndex])
    if(queryData.videoData[currTag?.query_param][nextIndex].video_s3_url){
      playVideoWithRefresh(videoData[currTag?.query_param][nextIndex].video_s3_url);   
      setCurrSubtitle(videoData[currTag?.query_param][nextIndex].subtitle_s3_url);     

      if(videoData[currTag?.query_param][nextIndex]?.isNew) {
        setSubtitlProperty({"captionBlobUrl":videoData[currTag?.query_param][nextIndex].subtitle_s3_url})
      } else {
        setSubtitlProperty({"captionUrl":videoData[currTag?.query_param][nextIndex].subtitle_s3_url})
      }             
    }else{
     setCurrVideo("")
     setCurrThumbnail(queryData.videoData[currTag?.query_param][nextIndex].thumbnail_s3_url_map?.electron_player)
    }
    setTitle(queryData.videoData[currTag.query_param][nextIndex].title);
  };

  const playVideo = (tag: any, video: any, index: number) => {
    setTitle(video?.title);
    setCurrTag(tag);
    setCurrIndex(index);
    setTargetedVideo(video)
    if(video?.isNew) {
      setSubtitlProperty({"captionBlobUrl":video.subtitle_s3_url})
    } else {
      setSubtitlProperty({"captionUrl":video.subtitle_s3_url})
    }
    if (video.video_s3_url) {
      setCurrThumbnail("");
      setCurrVideo(video?.video_s3_url);
      setDisableNextBtn(!(videoData[tag?.query_param]?.length > 1));
      videoRef?.current?.load();
      videoRef?.current?.play();
    } else {
      setCurrThumbnail(video?.thumbnail_s3_url_map?.electron_player);
      setCurrVideo("");
    }
    handleClickOpen();
  };

  useEffect( () => {
    setTimeout( () => {
       setIsPageLoading(false);
    } , 3000);
  } , [])

  const onImageLoad = () => {
    setImageLoading(false)
  }

  return videoData ? (
    <div className={styles.safePreview}>
      <div className={styles.navbar}>
        <span>
          <a href="/">
            <img src={logo}></img>
          </a>
        </span>
        <span>
          <a href="/">
            <img src={downloadBryzos}></img>
          </a>
        </span>
      </div>
      <div className={styles.mainSection}>
        {queryData?.videoData?.["safe-intro"]?.length && isSafeIntroVisible ? <h1 className={styles.pageVideoTitle}>
          YOU RECEIVED A SAFE! HERE’S WHY...
        </h1> : <></>}


        {(safeVideo && safeThumbnail) ? (  !isPageLoading ?
        <div className={clsx(styles.safeBorder,'safeBorder')}>
            <VideoPlayer
            url={safeVideo}
            width={"100%"}
            height={"100%"}
            videoRef={safevideoRef}
            autoPlay={true}
            id={"safeVideo"}
            playOnMute = {true}
            isPlayingPrev={isPlayingPrev}
            fromSafeIntro={true}
            setIsPageLoading = {setIsPageLoading}
            {...subtitlePropertyforSafe}
          />
          </div> : 
             <div>
          <div className={styles.thumbnailDiv}>
                <img src={safeThumbnail} className={styles.safeThumbnaill}
                onClick={()=>{setIsPageLoading(false)}}
                ></img>
                 <PlayIcon className={styles.videoIconThumbnail} />
             </div>
             </div>
        ) : (
          <></>
        )}
        {
          (safeThumbnail && !safeVideo) && (
            <div className={styles.thumbnailDiv}>
            <img src={safeThumbnail} className={styles.safeThumbnaill}
            ></img>
         </div>
          )
        }
        <div className={styles.afterIntroContentSafe}>
          {tags?.map((tag, index) => {
            return (
              !!(videoData &&
              videoData[tag?.query_param]?.length && tag?.query_param !== 'safe-intro' && tag?.query_param === 'AFTER-INTRO') && (
                <div className={styles.afterScrollSafeContent} key={index}>
                  <>
                    <div className={styles.safeslider}>
                      {videoData[tag?.query_param]?.map((video, index) => {
                        return (
                          <div
                            className={styles.SafeContentCarousel}
                            key={index}
                          >
                            <div className={styles.imageSafeContainer}>
                              <img
                                src={video?.thumbnail_s3_url_map?.thumbnail_safe}
                                alt={"thumbnail"}
                                className={styles.ContentCarouselImage}
                                onClick={()=>{playVideo(tag,video,index)}}
                              />
                              {video?.video_s3_url && (
                                <PlayIcon className={styles.videoIcon} />
                              )}
                            </div>
                            <p>{video?.title}</p>
                          </div>
                        );
                      })}
                    </div>
                  </>
                </div>
              )
            );
          })}
        </div>

        <div className={styles.SliderContentSafe}>
          {tags?.map((tag, index) => {
            return (
              !!(videoData &&
              videoData[tag?.query_param]?.length && tag?.query_param !== 'AFTER-INTRO') && (
                <div className={styles.scrollSafeContent} key={index}>
                  <>
                    <div className={styles.safepreivewContent}>
                      <h1>{tag?.display_title.toUpperCase()}</h1>{" "}
                      <p>{tag?.display_subtitle}</p>
                    </div>
                    <div className={styles.safeslider}>
                      {videoData[tag?.query_param]?.map((video, index) => {
                        return (
                          <div
                            className={styles.SafeContentCarousel}
                            key={index}
                          >
                            <div className={styles.imageSafeContainer}>
                              <img
                                src={video?.thumbnail_s3_url_map?.thumbnail_safe}
                                alt={"thumbnail"}
                                className={styles.ContentCarouselImage}
                                onClick={()=>{playVideo(tag,video,index)}}
                              />
                              {video?.video_s3_url && (
                                <PlayIcon className={styles.videoIcon} />
                              )}
                            </div>
                            <p>{video?.title}</p>
                          </div>
                        );
                      })}
                    </div>
                  </>
                </div>
              )
            );
          })}
        </div>

      </div>
      <Modal
        keepMounted
        open={open}
        onClose={handleClose}
        aria-labelledby="keep-mounted-modal-title"
        aria-describedby="keep-mounted-modal-description"
        className={styles.safeContainer}
        classes={{
          root: "safePopupDialog",
        }}
      >
        <Box className={styles.safeModalContent}>
          <CloseIcon
            className={styles.closeSafeButton}
            onClick={handleClose}
          ></CloseIcon>
          {currVideo ? (
            <VideoPlayer
              videoRef={videoRef}
              url={currVideo}
              playNextVideo={playNextVideo}
              disabled={disabledNextBtn}
              autoPlay={true}
              {...subtitlProperty}
            />
          ) : (
            <div className={styles.imageThumbnailContainer}> 
              <DotPulseLoader showLoader={imageLoading} />
              <div className={styles.safethumbnailDiv}>
                <img src={currThumbnail} onLoad={onImageLoad}></img>
              </div>
            </div>
          )}
          <p>{title}</p>
          {((currVideo && targetedVideo) && targetedVideo?.share_video_url?.trim()?.length > 0 )
          && <span className={styles.safeshareVideoIcon} ><ShareVideo/> Share</span>}
          {((currVideo || currThumbnail) && currTag && targetedVideo) && (
            <div>
              <h3 className={styles.safeDescription}>
                {getDisplayText()}
                {targetedVideo?.description?.length > characterLimit && (
                  <span className={styles.showmorebtn} onClick={handleToggle}>
                    {isExpanded ? "Show Less" : "Show More"}
                  </span>
                )}
              </h3>
            </div>
          )}
        </Box>
      </Modal>
    </div>
  ) : (
    <></>
  );
}

export default SafePreview;

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from '@mui/material';

interface NavigationWarningDialogProps {
  open: boolean;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const NavigationWarningDialog: React.FC<NavigationWarningDialogProps> = ({
  open,
  message,
  onConfirm,
  onCancel,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      aria-labelledby="navigation-warning-dialog-title"
      aria-describedby="navigation-warning-dialog-description"
    >
      <DialogTitle   component="h4" id="navigation-warning-dialog-title">
        Unsaved Changes
      </DialogTitle>
      <DialogContent>
        <DialogContentText
        
          id="navigation-warning-dialog-description"
        >
          {message}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onCancel} color="primary">
          Cancel
        </Button>
        <Button
          onClick={onConfirm}
          color="primary"
          variant="contained"
          autoFocus
        >
          Leave
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NavigationWarningDialog;


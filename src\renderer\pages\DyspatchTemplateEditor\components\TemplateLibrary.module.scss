.templateLibrary {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-height: calc(100vh - 200px);
  overflow-y: auto;

  h3 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #333;
  }
}

.quickStart {
  margin-bottom: 40px;

  h3 {
    margin-bottom: 20px;
  }
}

.quickStartButtons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.quickStartBtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 16px;
  font-weight: 600;
  text-align: center;

  span {
    font-size: 14px;
    font-weight: 400;
    margin-top: 8px;
    opacity: 0.9;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
  }

  &.sourceMode {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);

    &:hover {
      box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }
  }
}

.existingTemplates {
  h3 {
    margin-bottom: 16px;
    font-size: 18px;
  }
}

.templateSearch {
  margin-bottom: 16px;
}

.searchInput {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: #f8f9fa;

  &:focus {
    outline: none;
    border-color: #007bff;
    background: white;
  }
}

.templateList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 8px;
  margin-bottom: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.templateItem {
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
  color: #495057;
  text-align: center;

  &:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateY(-1px);
  }
}

.libraryNote {
  font-size: 14px;
  color: #6c757d;
  text-align: left;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #007bff;

  code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
  }
}

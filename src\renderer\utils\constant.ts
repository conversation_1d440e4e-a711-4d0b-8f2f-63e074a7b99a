export const ErrorMessageResponses = {
  notAuthorizedException: [
    "NotAuthorizedException",
    "UnauthorizedException",
    "Token has expired or invalid user.",
    "This user is not an admin"
  ],
  notShowErrors: [
    "404 Not Found – Resource cannot be found",
    "Resource cannot be found",
    "Invalid email!",
    "Column mismatch"
  ],
  refreshOnError: ['Security Token Changed'],
  cassMapppedUnableToClosePo: "Unable to close PO"
};

export const uploadColumnError = "Column mismatch";

export const errorApiPath = {
  referenceProductUpload: "/reference-data/referenceProductUpload"
}

export const PRODUCT_TYPE_PIPE = 'Pipe';

export const routePaths = {
  layout: "",
  login: "login",
  user: "user",
  create: "create",
  list: "list",
  preApproved: "pre-approved-emails",
  pending: "pending",
  pendingCompanyList: "pending-company-list",
  bnpl: "bnpl",
  bnplApproveReject: "bnpl-approve-reject",
  bnplLimitIncrease: "bnpl-limit-increase",
  bnplCloseOrderAndReplenish: "close-order-and-replenish",
  resaleCertificate: "resale-certificate",
  resaleCertificateApproveReject: "resale-certificate-approve-reject",
  referecneData: "referenceData",
  referecneDataV1: "referenceData/v1",
  achCredit: "ach-credit",
  achCreditOrders: "orders",
  achCreditPayment: "payment",
  achCreditCloseOrder: "close-order",
  sendInvoiceEmail: "send-invoice-email",
  depositSettings: "deposit",
  globalSetting: "global-setting",
  customSetting: "custom-setting",
  setting: "setting",
  orderCancellation: "order-cancellation",
  referenceSetting: "reference-setting",
  cancelOrder: "cancel-order",
  dyspatchTemplate: "dyspatch-template",
  dyspatchTemplateEditor: "dyspatch-template-editor",
  generateEmail: "generate-email",
  bryzosPay: "bryzos-pay",
  makeApayment: "make-a-payment",
  restartWebSocket: "restart-web-socket",
  restartNodeMessageCron: "restart-node-message-cron",
  broadcastNotifications: "broadcast-notifications",
  removeSalesTax: "remove-sales-tax",
  convertAchToBnpl: "convert-ach-to-bnpl",
  safeUploads: "safe-uploads",
  safeImgixImageKit: "safe-imgix-image-kit",
  cohortList: "cohort-list",
  adHocPayment: "ad-hoc-payment",
  updateSecurityToken: "update-security-token",
  cassMappingTransactionWithPo: "cass-mapping-transaction-with-po",
  discountedUsers: "spread-users",
  capgoUpdate: "capgo-update",
  tnc: "tnc",
  invoiceEmailAttachments: "invoice-email-attachments",
  videoUploads: "video-uploads",
  videoTag: "video-tags",
  videoLibrary:"video-library",
  holidayList:'holiday-list',
  chats:'chats',
  cassTransactionSubmissionStatus:'cass-make-a-payment-status',
  logReader:'log-reader',
  fetchUserLogs:'fetch-users-log',
  externalApisAccess:'external-api-key-endpoint-permissions', 
  editPricingBrackets:'edit-pricing-brackets',
  revertReferenceData: 'revert-reference-data',
  adminVideoTooltipScreens: 'admin-video-tooltip-screens',
  viewNexusThreshold: 'view-nexus-threshold',
  salesTax:'sales-tax',
  buyerPayments: 'buyer-payments'
};

export const getAdminVideoTooltipScreenHotspotsRoute = (id: string) =>
  `/admin-video-tooltip-screens/${id}/hotspots`;

export const getAdminVideoTooltipScreenMappingsRoute = (id: string) =>
  `/admin-video-tooltip-screens/${id}/mappings`;

export const BUYER = "BUYER";
export const SELLER = "SELLER";

export const HEADER_DOWNLOAD_EXTRACTED_ATTACHMENT_LINK = "Download Extracted\nAttachment\n(Seller Invoice)";
export const HEADER_EXTRACTED_INVOICE_LINE_ITEMS = "Show Extracted Invoice Line Items"
export const HEADER_BUYER_GENERATED_INVOICE = "Download Generated Buyer Invoice";
export const HEADER_SEND_BUYER_INVOICE_EMAIL = "Send Buyer Invoice Email";

export const reactQueryKeys = {
  cognitoUser: "cognitoUser",
  getIndicator: "getIndicator",
  getAllUsers: "getAllUsers",
  getAllPendingOnBoardUsers: "getAllPendingOnBoardUsers",
  getAllCompany: "getAllCompany",
  getAllPendingCompanies: "getAllPendingCompanies",
  getAllBnpls: "getAllBnpls",
  getAllReferenceDataProduct: "getAllReferenceDataProduct",
  getAllProducts: "getAllProducts",
  getReferenceDataVersions: "getReferenceDataVersions",
  getAllIncreaseRequest: "getAllIncreaseRequest",
  getAllCertificates: "getAllCertificates",
  getAllAchCreditOrder: "getAllAchCreditOrder",
  getAllAchCreditPayment: "getAllAchCreditPayment",
  getOrderToUpdateQtySendInvoiceEmail: "getOrderToUpdateQtySendInvoiceEmail",
  getCustomDepositHistory: "getCustomDepositHistory",
  getCustomDepositSetting: "getCustomDepositSetting",
  getGlobalDepositHistory: "getGlobalDepositHistory",
  getGlobalDepositSetting: "getGlobalDepositSetting",
  getSettings: "getSettings",
  getCancelOrderData: "getCancelOrderData",
  getDyspatchTemplate: "getDyspatchTemplate",
  getCassTransactionData: "getCassTransactionData",
  getSellerPaymentSetup: "getSellerPaymentSetup",
  getAllBuyerOrders: "getAllBuyerOrders",
  getNotificationEvents: "getNotificationEvents",
  getPoNumberData: "getPoNumberData",
  getAdminReferenceData: "useGetAdminReferenceData",
  useGetSafeUploads: "useGetSafeUploads",
  useGetSafeImgixImageKit: "useGetSafeImgixImageKit",
  nodeGetCassTransactionData: "nodeGetCassTransactionData",
  getCassSellerSetup: "getCassSellerSetup",
  getCassData: "getCassData",
  useGetAchOrder: "useGetAchOrder",
  getSecurityData: "getSecurityData",
  getProbablePo: "getProbablePo",  
  getPos: "getPos",  
  getSalesTax: "getSalesTax",
  getCompanyResaleCertificate: "getCompanyResaleCertificate",
  getDiscountedUses: "getDiscountedUses",  
  getMappedCompanies: "getMappedCompanies",
  getInvoiceEmailAttachments: "getInvoiceEmailAttachments",
  getAllUploadedVideos: "getAllUploadedVideos",
  getAllVideoLibraryTags: "getAllVideoLibraryTags",
  getAllVideoLibraryInternalTags: "getAllVideoLibraryInternalTags",
  getAllHolidayCalendar: "getAllHolidayCalendar",
  getExternalApiKeys:"getExternalApiKeys",
  getInvoiceData: "getInvoiceData",
  getAllProductsV2: "getAllProductsV2",
  getReferenceDataProductVersionsV2: "getReferenceDataProductVersionsV2",
  getAdminVideoTooltipScreens: "getAdminVideoTooltipScreens",
  getSalesTaxNexusData: "getSalesTaxNexusData",
  getBuyerPayments: "getBuyerPayments"
};

// USER ROLE
export const ROLE_BRYZOS_ADMIN = "bryzos-admin";
export const BRYZOS_SEND_INVOICE_GROUP = "bryzos-send-invoice-group";
export const PAYMENT_METHOD_ACH_CREDIT = 'ach_credit';


export const referecneDataType = {
  widgetServiceReferenceData: "WIDGET_SERVICE_REFERENCE_DATA",
  homepageReferenceData: "HOMEPAGE_WIDGET_SERVICE_REFERENCE_DATA",
}

export const referenceDataVersionStatus = {
  active: 'active',
  previous: 'previous',
}

export const broadcastNotificationType = {
  priceChange: "NOTIFICATION_PRICE_CHANGES",
  productChange: "NOTIFICATION_PRODUCT_CHANGES",
  bothChange:  "NOTIFICATION_PRICE_PRODUCT_CHANGES",
}
export const sendToOptions = {
  all: "ALL",
  buyers: "BUYERS",
  sellers: "SELLERS",
  users: "USERS",
}

export const CUSTOM_NOTIFICATION = "CUSTOM_NOTIFICATION";
export const USE_IMGIX = "USE_IMGIX";
export const USE_IMAGEKIT = "USE_IMAGEKIT";

export const logoNameList = {
  bryzosLogo: 'bryzos',
  bryzosPayLogo: 'bryzosPay',
}

// cass constants
export const SUPPLIER = "Supplier";

export const CASS_ADHOC_SELLER_UNIQUE_IDENTIFIER_PREFIX = "CASS_ADHOC_SELLER_UNIQUE_IDENTIFIER_PREFIX";
export const CASS_ADHOC_SELLER_UNIQUE_IDENTIFIER_LENGTH = "CASS_ADHOC_SELLER_UNIQUE_IDENTIFIER_LENGTH";
export const CASS_SELLER_UNIQUE_IDENTIFIER_PREFIX = "CASS_SELLER_UNIQUE_IDENTIFIER_PREFIX";

export const inputRegex = /^[A-Za-z0-9\s]+$/;

export const makeApaymentErrorMessage = {
  rcdcPending: '*There is a pending RCDC for this PO.',
  orderIsNotClaimed: '*This order is not claimed.',
  notSellerSetup: "*Seller hasn't setup their destination account."
}

export const tooltipText = {
  cassMappingTransaction : "Funding amount and mapped PO's Buyer Total matched and hence is hidden"
}

export const videoUploadData = "Video uploads usually take a bit longer than other files. We'll let you know once it's done. Meanwhile, stay put and don't refresh the page."

export const partialForm = "Please complete the form"

export const invalidSequenceNumber = "The entered sequence number is already occupied. Please enter a new sequence number"

export const previewRadioButtonText = {
  desktop: "desktop",
  mobile: "mobile",
  safe: "safe",
};

export const videoTagsFields = {
  display_title : "display_title",
  name:"name",
  display_subtitle:"display_subtitle",
  show_on_app:"show_on_app",
  show_on_safe:"show_on_safe",
  add_at_top : "add_at_top",
  shuffle_sequence : "shuffle_sequence"
}

export const isRequired = "is required"

export const string = "string"

export const introVideoCaptionURL = '/subtitle/BryzosSafeIntroVideo.vtt';

export const errorText = {
  required: 'Required',
  fileIsRequired: 'A file is required',
  arrayMinimum: 'Array must contain at least one string',
}

export const defaultThumbnailFiles = {
  thumbnail_app: undefined,
  thumbnail_safe: undefined,
  electron_player: undefined,
  intro_desktop: undefined,
  intro_mobile: undefined,
  intro_tablet: undefined
}

export enum certStatusObj {
  pending = 'Pending',
  reject = 'Rejected',
  approve = 'Approved',
  expire = 'Expired',
  delete = 'Delete'
}

export const colorCode = {
  'red': '#ff5d47',
  'green': '#50C878',
  'grey': '#8595ad'
}
export const mimeTypeToExtension = {
  'application/pdf': '.pdf',
  'image/jpeg': '.jpg',
  'image/png': '.png',
  'text/plain': '.txt',
  'text/html': '.html',
  'application/zip': '.zip',
  'application/json': '.json',
  'audio/mpeg': '.mp3',
  'video/mp4': '.mp4',
  'application/x-rar-compressed': '.rar',
  'application/msword': '.doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'application/vnd.ms-excel': '.xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
};

export const confirmationPopupKeys= {
  confirmationContent:'Do you want to proceed?',
  confirmation :{
    yes : 'Yes',
    no :'No'
  }
}

export const preApprovedEmailConstant = {
  confirmationMessage: 'All data will be reset. Do you want to continue ?'
}


export interface TransactionsCount {
  all: number;
  success: number;
  failed: number;
  pending: number;
}

export const defaultTransactionsCountObj: TransactionsCount = {
  all: 0,
  success: 0,
  failed: 0,
  pending: 0
}

export const urlMasks = [
  {
    "key": "bryzos.com",
    "mask": "TL0hk"
  },
  {
    "key": "vercel.app",
    "mask": "70N1sX"
  },
  {
    "key": "bryzoswidget.com",
    "mask": "x8nZVM7I"
  },
  {
    "key": "bryzosservices.com",
    "mask": "G6Tmy"
  },
  {
    "key": "imgix.net",
    "mask": "uDLGFhKx"
  },
  {
    "key": "imagekit.io",
    "mask": "Ny7PH02R"
  },
  {
    "key": "cloudfront.net",
    "mask": "xXKaBdR"
  },
  {
    "key": "capgo.app",
    "mask": "a8CfxyV7a"
  },
  {
    "key": "amazonaws.com",
    "mask": "U12FEsJH"
  },
  {
    "key": "cassinfo.com",
    "mask": "Fr0mj1v3J"
  },
  {
    "key": "truevault.com",
    "mask": "hsQ6oqGZt"
  },
  {
    "key": "wss://ws-ap2.pusher.com",
    "mask": "h0ttI"
  },
  {
    "key": "|",
    "mask": "__PIPE__"
  }
]

export const API_KEY_ENVIRONMENTS= {
  sandbox:"sandbox",
  prod:"prod"
}

export const BNPL_STATUS = {
  enabled: "ENABLED",
  onHold: "ON HOLD",
  restricted: "RESTRICTED",
  rejected: "REJECTED",
  pending: "PENDING"
}



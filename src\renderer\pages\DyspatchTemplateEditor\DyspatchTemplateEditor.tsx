import React, { useEffect } from 'react';
import { DndContext } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { createDragDropManager } from 'dnd-core';
import { TopBar } from './components/TopBar';
import { BlockEditor } from './components/BlockEditor';
import { SourceEditor } from './components/SourceEditor';
import { PreviewPane } from './components/PreviewPane';
import { HtmlCodeView } from './components/HtmlCodeView';
import { TemplateLibrary } from './components/TemplateLibrary';
import { useTemplateStore } from './stores/templateStore';
import useGetTemplates from './hooks/useGetTemplates';
import type { TemplateItem } from './types';
import Loader from '../../components/common/Loader/Loader';
import styles from './DyspatchTemplateEditor.module.scss';

// Create a stable DragDropManager at module level so it survives
// React 18 StrictMode's unmount/remount cycle in development.
const manager = createDragDropManager(HTML5Backend);

const emptyTemplates: TemplateItem[] = [];

const DyspatchTemplateEditor: React.FC = () => {
  const { editorMode, viewMode, blocks, sourceHtml, resetTemplateState } = useTemplateStore();
  const { data: templates = emptyTemplates, isLoading, isFetching } = useGetTemplates();

  const hasContent = blocks.length > 0 || sourceHtml.length > 0;

  // Error handling removed - errors are handled by global axios interceptor in AppContainer.tsx
  // to prevent duplicate error popups

  useEffect(() => {
    return () => {
      resetTemplateState();
    };
  }, [resetTemplateState]);

  return (
    <DndContext.Provider value={{ dragDropManager: manager }}>
      <div className={styles.app}>
        <TopBar />
        <div className={styles.mainContent}>
          {!hasContent ? (
            <div className={styles.welcomeScreen}>
              <div className={styles.welcomeContent}>
                <h2>Welcome to Dyspatch Template Editor</h2>
                <p>Create professional email templates compatible with all major email clients</p>
                {isLoading || isFetching ? (
                  <div className={styles.loaderImg}>
                    <Loader />
                  </div>
                ) : (
                  <TemplateLibrary templates={templates} />
                )}
              </div>
            </div>
          ) : (
            <>
              {viewMode === 'edit' && (
                editorMode === 'block' ? <BlockEditor /> : <SourceEditor />
              )}
              {viewMode === 'preview' && <PreviewPane />}
              {viewMode === 'html' && <HtmlCodeView />}
            </>
          )}
        </div>
      </div>
    </DndContext.Provider>
  );
};

export default DyspatchTemplateEditor;

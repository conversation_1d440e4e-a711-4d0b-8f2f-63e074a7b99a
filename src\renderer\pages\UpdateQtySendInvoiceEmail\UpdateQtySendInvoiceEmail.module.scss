.searchBox{
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;

  input{
    padding: 0px 12px;
  }
}



.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    max-height: 600px;

    &.editLinestbl{
        max-height: 480px;
    }

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }

    table {
        width: 100%;
        white-space: nowrap;
        border-collapse: collapse;
        border-spacing: 0;

        thead {
            tr {

                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 4px 6px;
                    height: 35px;
                    position: sticky;
                    z-index: 9;
                    top: 0;
                    background: #676f7c;
                    color: #fff;
                    &:first-child{
                        position: sticky;
                        left: 0px;
                        background: #676f7c;
                        z-index: 10;
                    }

                    &:nth-child(3) {
                        width: 20px;
                    }

                    &:nth-child(4) {
                        width: 35%;
                    }
                }
            }
        }

        tbody {
            background-color: #fff;
            position: relative;

            tr {
                margin: 0;
                position: relative;

                &:nth-child(even) {
                    background-color: #f2f2f2;
                    td{
                          &:first-child{
                          background-color: #f2f2f2;
                      }
                    }

                }

                &.activeRow{
                        background-color: rgba(197, 210, 202,0.5);
                        td{
                            &:first-child{
                                background-color: rgba(197, 210, 202,9);
                            }
                        }
                }
               
                td {
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 8px 6px;
                    height: 42px;
                    position: relative;

                    &:first-child{
                        position: sticky;
                        left: 0px;
                        background-color: #fff;
                        z-index: 2;
                    }


                    &:nth-child(2), &:nth-child(5){
                        input{
                            width: 90px;
                        }
                    }

                    .approvalBtn {
                        display: inline-block;
                        text-align: center;
                        padding: 6px 11px;
                        border: 1px solid transparent;
                        border-radius: 0.25rem;
                        background-color: #343a40;
                        font-size: 15px;
                        color: #fff;
                        cursor: pointer;
                    }

                    .errorText{
                        font-size: 14px;
                        color: #ff0000;
                        margin: 0px;
                    }

                    button{
                        cursor: pointer;
                    }
                  
                }
            }
        }
    }
}

.delRowBtn{
    cursor: pointer;
}

.loaderCenter {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 100px;
}

.sendEmailBtn {
    display: inline-block;
    text-align: center;
    padding: 6px 17px;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    background-color: #343a40;
    border: none;
    font-size: 15px;
    color: #fff;
    cursor: pointer;
    margin-right: 12px;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.disabledBtn {
    opacity: 0.5;
    cursor: not-allowed;
}

.textCenter{
    text-align: center;
}

.inputError{
    border-color: #ff0000 !important;
}

.editLinePopup {
    .editLineContent {
        padding: 20px;
        max-width: 1500px;
        width: 100%;

        &.addLineContent{
            max-width: 900px;
            width: 100%;
        }

        .headerDialog {
            line-height: 1;
            margin-top: 0px;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 16px;
            .headerDetail{
                font-weight: 400;
            }
        }

        .crossBtn {
            background-color: transparent;
            padding: 0px;
            position: absolute;
            right: 12px;
            top: 10px;
            cursor: pointer;
            border: 0px;
            font-size: 20px;
        }

        .inputEditLine {
            width: 120px;
            border-radius: 4px;
            border: solid 1px #c4c8d1;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.57;
            letter-spacing: normal;
            text-align: left;
            color: #3b4665;
            height: 34px;
            padding: 0px 10px;
            &:disabled{
                background-color: #e3e3e3;
            }
        }
    }
}

.btnSection{
    margin-top: 12px;
    display: flex;
    justify-content: center;
}

.disabledRow {
    td{
        opacity: 0.5;
        position: relative;
          div, input {
            pointer-events: none; /* Prevent interaction with the row */
            }
        &:nth-child(1){
            opacity: unset;
            &::after {
                content: unset;
            }
        }
        &:nth-child(3){
            opacity: unset;
            &::after {
                content: unset;
            } 
        }
        &:after {
            content: " ";
            position: absolute;
            cursor: not-allowed;
            left: 0px;
            top: 0px;
            width: 100%;
            height: 100%;
        }
        &.chkEditLines{
            input{
                opacity: 0.3;
            }
        }
    }
}

.closedRow{
    td {
        position: relative;
        &.chkEditLines{
            input{
                opacity: 0.3;
            }
        }
    }

    td{
        &:nth-child(1){
            pointer-events: unset;
            opacity: unset;
            &:before {
                content: unset;
            }
        }
    }
}

.cancelledRow {
    td {
        position: relative;
        &.chkEditLines{
            input{
                opacity: 0.3;
            }
        }
    }

    td{
        &:before {
            content: " ";
            position: absolute;
            top: 50%;
            left: 0;
            border-bottom: 2px solid #ad0000;
            width: 100%;
            z-index: 99;
        }
        &:after{
            z-index: 999;
        }
        &:nth-child(1){
            pointer-events: unset;
            opacity: unset;
            &:before, &:after {
                content: unset;
            }
        }
    }
    
   
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding-right: 4px;
    border-radius: 0px 0px 4px 4px;
    margin-top: 1px;
}

.listAutoComletePanel.listAutoComletePanel {
    width: 100%;
    max-height: 260px;
    padding: 6px 4px 6px 10px;
    margin-top: 4px;

    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color:#a8b2bb;
        border-radius: 20px;

    }

    li {
        font-size: 14px;
        line-height: normal;
        text-align: left;
        color: #3b4665;
        box-shadow: none;
        padding: 6px 8px;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 4px;
        border-radius: 2px;
        min-height: 100%;
        &:hover {
            // background-color: #fff;
            color: #3b4665;
        }

        &[aria-selected="true"] {
            background-color: #fff !important;
            color: #000;
        }
    }
}

.orderContinuePopup {
    h2 {
        display: none;
    }
    .continuePopup {
        padding: 20px;
        text-align: center;
        width: 380px;
        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 280px;
        }
        .continuetext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);
        }
        .yesAndnoBtn {
            display: flex;
            gap: 10px;
            .okBtn {
                width: 100%;
                height: 45px;
                border-radius: 6px;
                text-decoration: none;
                gap: 8px;
                border: none;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                background-color: var(--primaryColor);
                color: #fff;
            }
        }
    }
}

.approveRejectPopup {
    h2 {
        display: none;
    }
    .successfullyUpdated {
        padding: 20px;
        text-align: center;
        width: 300px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 240px;
        }
        .successfullytext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);
            @media screen and (max-width: 768px) and (min-width: 320px) {
                font-size: 18px;
            }           
        }
        .okBtn {
            width: 100%;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
        }
    }
}

.blink {
    animation: blinker 1.5s linear infinite;
    color: red;
    font-size: 1.5em;
    /* Larger, responsive font size */
    margin-bottom: 20px;
}

@keyframes blinker {
    50% {
        opacity: 0;
    }
}
.orderTotalPricing{
    display: flex;
    flex-direction: row-reverse;
    margin-top: 12px;
   
    .ordersTotal {
        padding: 8px;
        min-width:230px;
        border-radius: 4px;

        &.ordersTotalBuyer{
            background-color: #e9f3ff;
            color: #2488fa;
            margin-left: 20px;
            height: max-content;

        }
        &.ordersTotalSeller{
            background-color: #ffefed;
            color: #ff5d47;
            height: max-content;
        }
       

        .ordersTotalRow{
            margin-bottom: 2px;
            .ordersTotalTitle {
                font-size: 16px;
                // color: #343a40;
                line-height: normal;
                text-align: left;
            }
        
            .ordersTotalValue {
                font-size: 16px;
                // color: #343a40;
                line-height: normal;
                text-align: right;
            }

            .ordersTotalDOllarSign{
                text-align: left;
                padding-left: 8px;
                min-width: 60px;
            }

        }
        .verticalTop{
            vertical-align: baseline;
        }
        .boldFont{
            td{
                font-weight: 500;
                font-size: 20px !important;
            }
        }
    }
    .addBtn{
        flex: 1;
        padding-top: 10px;
        padding-right: 10px;
    }
}

.cancelledTooltip{
    background: #ff5d47;
}
.closedTooltip{
    background-color: #f5bf4f;
}
.indicationTooltip {
    display: flex;
    color: #fff;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    cursor: pointer;
    &::before {
        flex: 0 0 17px;
        height: 17px;
        border-radius: 50%;
        content: "!";
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
  }

.errorStyle.errorStyle, .warningStyle.warningStyle {
    .tooltip.tooltip {
      font-size: 11px;
      line-height: 1.6;
      text-align: left;
      color: #fff;
      text-transform: capitalize;
      margin-bottom: 5px;
    }
}

.warningStyle.warningStyle {
    .tooltip.tooltip {
        background-color: #f5bf4f;
    }
}

.errorStyle.errorStyle {
    .tooltip.tooltip {
        background-color: #ff5d47;
    }
}

.loadingLinetxt{
    width: 100%;
    padding: 30px;
    text-align: center;
    font-size: 30px;
    color: #2488fa;
    text-transform: uppercase;
}


.sendEmailGrid{
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    span{
        width: 30px;
        font-size: 14px;
        text-align: left;
    }

    .sendEmailinput {
        width: 120px;
        border-radius: 4px;
        border: solid 1px #c4c8d1;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.57;
        letter-spacing: normal;
        text-align: left;
        color: #3b4665;
        height: 34px;
        padding: 0px 10px;
        flex: 1;
        &:read-only{
            background-color: #e3e3e3;
        }

        &:focus{
            outline: none;
        }
    }
}

.editLineExclamation {
    background: #0099ff;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    display: flex;
    height: 18px;
    width: 18px;
    align-items: center;
    justify-content: center;
    line-height: 1;
}


.btnSectionSendEmail{
    display: flex;
    align-items: center;
}
.sendEmailIconBtn{
    background-color: #fff;
    border-radius: 3px;
    padding: 0px;
    border: 0px;
    margin-right: 12px;
    cursor: pointer;
    &:last-child{
        margin-right: 0px;
    }
    svg{
        width: 26px;
        height: 26px;
        path{
            fill: #000;
        }
    }
}

.sendEmailPopupInputTooltip.sendEmailPopupInputTooltip {
    .tooltip.tooltip {
        color: #fff;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        margin-top: 6px;
    }
}
.Dropdownpaper{
    li{
        text-transform: uppercase;
    }
}
.dropdownUnitUppercase{
    text-transform: uppercase;
}

.canvasBlock {
  position: relative;
  padding: 10px;
  margin: 10px 0;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #e0e0e0;
  }

  &.selected {
    border-color: #007bff;
    background: #f0f8ff;
  }

  &.dragging {
    opacity: 0.5;
  }

  &.dropTarget {
    border-top: 3px solid #007bff;
  }

  &:hover .blockControls,
  &.selected .blockControls {
    display: flex;
  }
}

.blockControls {
  position: absolute;
  top: 5px;
  right: 5px;
  display: none;
  gap: 5px;
}

.dragHandle {
  padding: 4px;
  cursor: move;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.deleteBtn {
  padding: 4px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  color: #dc3545;
}

.blockContent {
  min-height: 30px;
}

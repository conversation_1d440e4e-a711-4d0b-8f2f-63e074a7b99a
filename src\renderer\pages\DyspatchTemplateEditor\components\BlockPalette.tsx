import { useDrag } from 'react-dnd';
import TitleIcon from '@mui/icons-material/Title';
import ImageIcon from '@mui/icons-material/Image';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import RemoveIcon from '@mui/icons-material/Remove';
import CodeIcon from '@mui/icons-material/Code';
import SubjectIcon from '@mui/icons-material/Subject';
import MouseIcon from '@mui/icons-material/Mouse';
import type { BlockType } from '../types';
import styles from './BlockPalette.module.scss';

interface DraggableBlockProps {
  type: BlockType;
  icon: React.ReactNode;
  label: string;
}

function DraggableBlock({ type, icon, label }: DraggableBlockProps) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'block',
    item: { type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }), [type]);

  return (
    <div
      ref={drag as any}
      className={`${styles.paletteItem} ${isDragging ? styles.dragging : ''}`}
      title={label}
    >
      {icon}
      <span>{label}</span>
    </div>
  );
}

export function BlockPalette() {
  const blocks: Array<{ type: BlockType; icon: React.ReactNode; label: string }> = [
    { type: 'heading1', icon: <TitleIcon sx={{ fontSize: 20 }} />, label: 'Heading 1' },
    { type: 'heading2', icon: <TitleIcon sx={{ fontSize: 18 }} />, label: 'Heading 2' },
    { type: 'heading3', icon: <TitleIcon sx={{ fontSize: 16 }} />, label: 'Heading 3' },
    { type: 'paragraph', icon: <SubjectIcon sx={{ fontSize: 20 }} />, label: 'Paragraph' },
    { type: 'button', icon: <MouseIcon sx={{ fontSize: 20 }} />, label: 'Button' },
    { type: 'image', icon: <ImageIcon sx={{ fontSize: 20 }} />, label: 'Image' },
    { type: 'spacer', icon: <CheckBoxOutlineBlankIcon sx={{ fontSize: 20 }} />, label: 'Spacer' },
    { type: 'divider', icon: <RemoveIcon sx={{ fontSize: 20 }} />, label: 'Divider' },
    { type: 'html', icon: <CodeIcon sx={{ fontSize: 20 }} />, label: 'HTML' },
  ];

  return (
    <div className={styles.blockPalette}>
      <h3>Blocks</h3>
      <div className={styles.paletteItems}>
        {blocks.map((block) => (
          <DraggableBlock key={block.type} {...block} />
        ))}
      </div>
    </div>
  );
}

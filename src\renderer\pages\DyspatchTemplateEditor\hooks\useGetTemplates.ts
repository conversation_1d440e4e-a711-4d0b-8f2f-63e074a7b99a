import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import type { TemplateItem } from "../types";

const useGetTemplates = () => {
  return useQuery<TemplateItem[]>(
    ["getEditorTemplates"],
    async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/dyspatch/get_templates`
        );
        
        // Check for error_message in response
        if (response.data?.data) {
          if (
            typeof response.data.data === "object" &&
            "error_message" in response.data.data
          ) {
            const errorMessage = response.data.data.error_message;
            throw new Error(errorMessage);
          }
        }
        
        if (response.data && response.data.data && Array.isArray(response.data.data)) {
          return response.data.data.map((item: any) => ({
            templateid: item.id || '',
            templateName: item.event || '',
            templateDisplayName: item.display_event || '',
            subject: item.subject || '',
            html_template: item.html_template || '',
          }));
        }
        
        return [];
      } catch (error: any) {
        // Extract error_message from error response if available
        if (error.response?.data?.data?.error_message) {
          throw new Error(error.response.data.data.error_message);
        }
        console.error("Failed to fetch templates:", error);
        throw new Error(error?.message || "Failed to fetch templates");
      }
    },
    {
      staleTime: 0,
      cacheTime: 0,
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      retry: false,
    }
  );
};

export default useGetTemplates;

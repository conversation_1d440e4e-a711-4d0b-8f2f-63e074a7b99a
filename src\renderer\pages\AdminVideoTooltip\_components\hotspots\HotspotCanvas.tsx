import React, { useState, useRef, useEffect } from 'react';
import { Stage, Layer, Image, Rect, Transformer } from 'react-konva';
import { Box } from '@mui/material';
import useImage from 'use-image';
import { useElementSize } from '@mantine/hooks';

const HotspotCanvas = ({ 
  screen, 
  hotspots, 
  onHotspotsChange, 
  selectedTool, 
  selectedHotspotId,
  onHotspotSelect,
  stageRef,

}) => {
  const [image] = useImage(screen?.image_url);
  const [isDrawing, setIsDrawing] = useState(false);
  const [newHotspot, setNewHotspot] = useState(null);
  const [stageScale, setStageScale] = useState(1);
  const [stagePos, setStagePos] = useState({ x: 0, y: 0 });
  const transformerRef = useRef();
  const layerRef = useRef();

  // get canvas width dynamically
  const { ref, width } = useElementSize();

  // Canvas dimensions
  const canvasWidth = width;
  const canvasHeight = 600;

  // Calculate image scaling to fit canvas while maintaining aspect ratio
  const getImageScale = () => {
    if (!image || !screen) return { scaleX: 1, scaleY: 1, width: 0, height: 0 };
    
    const imageAspect = screen.natural_width / screen.natural_height;
    const canvasAspect = canvasWidth / canvasHeight;
    
    let scaleX, scaleY, width, height;
    
    if (imageAspect > canvasAspect) {
      // Image is wider than canvas
      scaleX = scaleY = canvasWidth / screen.natural_width;
      width = canvasWidth;
      height = screen.natural_height * scaleX;
    } else {
      // Image is taller than canvas
      scaleX = scaleY = canvasHeight / screen.natural_height;
      width = screen.natural_width * scaleX;
      height = canvasHeight;
    }
    
    return { scaleX, scaleY, width, height };
  };

  const imageScale = getImageScale();

  // Convert normalized coordinates to canvas coordinates
  const denormalizeCoords = (normalizedCoords) => {
    return {
      x: normalizedCoords.x * imageScale.width,
      y: normalizedCoords.y * imageScale.height,
      width: normalizedCoords.width * imageScale.width,
      height: normalizedCoords.height * imageScale.height,
    };
  };

  // Convert canvas coordinates to normalized coordinates
  const normalizeCoords = (canvasCoords) => {
    return {
      x: canvasCoords.x / imageScale.width,
      y: canvasCoords.y / imageScale.height,
      width: canvasCoords.width / imageScale.width,
      height: canvasCoords.height / imageScale.height,
    };
  };

  // Handle mouse down for drawing
  const handleMouseDown = (e) => {
    if (selectedTool !== 'rectangle') return;
    
    const stage = e.target.getStage();
    if (!stage) return;
    const currentScale = stage.scaleX();
    const currentPos = { x: stage.x(), y: stage.y() };
    
    const pos = stage.getPointerPosition();
    if (!pos) return;
    
    const x = (pos.x - currentPos.x) / currentScale;
    const y = (pos.y - currentPos.y) / currentScale;
    
    // Check if click is within image bounds
    if (x < 0 || y < 0 || x > imageScale.width || y > imageScale.height) return;
    
    setIsDrawing(true);
    setNewHotspot({
      x,
      y,
      width: 0,
      height: 0,
    });
  };

  // Handle mouse move for drawing
  const handleMouseMove = (e) => {
    if (!isDrawing || selectedTool !== 'rectangle') return;
    
    const stage = e.target.getStage();
    if (!stage) return;
    
    const currentScale = stage.scaleX();
    const currentPos = { x: stage.x(), y: stage.y() };
    
    const pos = stage.getPointerPosition();
    if (!pos) return;
    
    const x = (pos.x - currentPos.x) / currentScale;
    const y = (pos.y - currentPos.y) / currentScale;
    
    setNewHotspot(prev => ({
      ...prev,
      width: Math.max(0, Math.min(imageScale.width - prev.x, x - prev.x)),
      height: Math.max(0, Math.min(imageScale.height - prev.y, y - prev.y)),
    }));
  };

  // Handle mouse up for drawing
  const handleMouseUp = () => {
    if (!isDrawing || !newHotspot) return;
    
    setIsDrawing(false);
    
    // Only create hotspot if it has meaningful size
    if (newHotspot.width > 10 && newHotspot.height > 10) {
      const normalizedCoords = normalizeCoords(newHotspot);
      const hotspotId = `hotspot_${Date.now()}`;
      
      const newHotspotData = {
        id: hotspotId,
        element_id: '', // Will be set by user
        shape_type: 'rect',
        coords_json: normalizedCoords,
        is_enabled: true,
        has_video_mapping: false,
      };
      
      onHotspotsChange([...hotspots, newHotspotData]);
      onHotspotSelect(hotspotId);
    }
    
    setNewHotspot(null);
  };

  // Handle hotspot click for selection
  const handleHotspotClick = (hotspotId) => {
    if (selectedTool === 'select') {
      onHotspotSelect(hotspotId);
    }
  };

  // Handle zoom
  const handleWheel = (e) => {
    e.evt.preventDefault();
    
    const scaleBy = 1.1;
    const stage = e.target.getStage();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    
    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };
    
    const newScale = e.evt.deltaY > 0 ? oldScale * scaleBy : oldScale / scaleBy;
    const clampedScale = Math.max(0.1, Math.min(3, newScale));
    
    setStageScale(clampedScale);
    setStagePos({
      x: pointer.x - mousePointTo.x * clampedScale,
      y: pointer.y - mousePointTo.y * clampedScale,
    });
  };

  const handleStageDragEnd = (e) => {
    const stage = e.target;
    setStagePos({ x: stage.x(), y: stage.y() });
  };

  const handleStageDragMove = (e) => {
    const stage = e.target;
    setStagePos({ x: stage.x(), y: stage.y() });
  };

  // Update transformer when selection changes
  useEffect(() => {
    if (selectedHotspotId && transformerRef.current) {
      const selectedNode = layerRef.current.findOne(`#${selectedHotspotId}`);
      if (selectedNode) {
        transformerRef.current.nodes([selectedNode]);
        transformerRef.current.getLayer().batchDraw();
      }
    } else if (transformerRef.current) {
      transformerRef.current.nodes([]);
      transformerRef.current.getLayer().batchDraw();
    }
  }, [selectedHotspotId]);

  return (
    <Box
      sx={{
        border: '1px solid #ccc',
        borderRadius: 1,
        overflow: 'hidden',
        backgroundColor: '#f5f5f5',
        cursor: selectedTool === 'rectangle' ? 'crosshair' : 'default',
      }}
      ref={ref}
    >
      <Stage
        ref={stageRef}
        width={canvasWidth}
        height={canvasHeight}
        scaleX={stageScale}
        scaleY={stageScale}
        x={stagePos.x}
        y={stagePos.y}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onWheel={handleWheel}
        draggable={selectedTool === 'pan'}
        onDragEnd={handleStageDragEnd}
        onDragMove={handleStageDragMove}
      >
        <Layer ref={layerRef}>
          {/* Background Image */}
          {image && (
            <Image
              image={image}
              width={imageScale.width}
              height={imageScale.height}
              listening={false}
            />
          )}
          
          {/* Existing Hotspots */}
          {hotspots.map((hotspot) => {
            const coords = denormalizeCoords(hotspot.coords_json);
            const isSelected = hotspot.id === selectedHotspotId;
            
            return (
              <Rect
                key={hotspot.id}
                id={hotspot.id}
                x={coords.x}
                y={coords.y}
                width={coords.width}
                height={coords.height}
                fill="rgba(65, 90, 119, 0.3)"
                stroke={isSelected ? "#ff6b35" : "#415a77"}
                strokeWidth={isSelected ? 3 : 2}
                dash={isSelected ? [5, 5] : []}
                onClick={() => handleHotspotClick(hotspot.id)}
                onTap={() => handleHotspotClick(hotspot.id)}
              />
            );
          })}
          
          {/* New Hotspot Being Drawn */}
          {newHotspot && (
            <Rect
              x={newHotspot.x}
              y={newHotspot.y}
              width={newHotspot.width}
              height={newHotspot.height}
              fill="rgba(255, 107, 53, 0.3)"
              stroke="#ff6b35"
              strokeWidth={2}
              dash={[5, 5]}
              listening={false}
            />
          )}
          
          {/* Transformer for selected hotspot */}
          {selectedTool === 'select' && (
            <Transformer
              ref={transformerRef}
              boundBoxFunc={(oldBox, newBox) => {
                // Constrain to image bounds
                const constrainedBox = {
                  ...newBox,
                  x: Math.max(0, Math.min(imageScale.width - newBox.width, newBox.x)),
                  y: Math.max(0, Math.min(imageScale.height - newBox.height, newBox.y)),
                  width: Math.max(10, Math.min(imageScale.width, newBox.width)),
                  height: Math.max(10, Math.min(imageScale.height, newBox.height)),
                };
                return constrainedBox;
              }}
              onTransformEnd={(e) => {
                const node = e.target;
                const hotspotId = node.id();
                const hotspotIndex = hotspots.findIndex(h => h.id === hotspotId);
                
                if (hotspotIndex !== -1) {
                  const newCoords = normalizeCoords({
                    x: node.x(),
                    y: node.y(),
                    width: node.width() * node.scaleX(),
                    height: node.height() * node.scaleY(),
                  });
                  
                  const updatedHotspots = [...hotspots];
                  updatedHotspots[hotspotIndex] = {
                    ...updatedHotspots[hotspotIndex],
                    coords_json: newCoords,
                  };
                  
                  onHotspotsChange(updatedHotspots);
                  
                  // Reset scale
                  node.scaleX(1);
                  node.scaleY(1);
                }
              }}
            />
          )}
        </Layer>
      </Stage>
    </Box>
  );
};

export default HotspotCanvas;

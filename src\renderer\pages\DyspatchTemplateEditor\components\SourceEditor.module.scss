.sourceEditor {
  display: flex;
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  min-height: 0;
}

.editorContainer {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: calc(100% - 520px);
  min-height: 0;

  // Disable ALL Monaco editor tooltips globally to prevent them from blocking interactions
  :global {
    // Hide only standalone tooltip/hover overlay elements (not button content)
    .monaco-editor > .monaco-hover,
    .monaco-editor .monaco-hover:not(.button):not(.button *),
    .monaco-editor .monaco-tooltip:not(.button):not(.button *),
    .monaco-editor .monaco-hover-content:not(.button):not(.button *) {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      pointer-events: none !important;
    }

    // Hide keyboard shortcut hints that appear as tooltips (but not if they're part of button structure)
    .monaco-editor .monaco-keybinding-key:not(.button):not(.button *),
    .monaco-editor .keybinding:not(.button):not(.button *) {
      display: none !important;
      visibility: hidden !important;
    }

    // Ensure all buttons remain visible and clickable
    .monaco-editor .button,
    .monaco-editor .find-widget .button,
    .monaco-editor .monaco-button {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
      pointer-events: auto !important;
      cursor: pointer !important;
    }

    // Only hide tooltip pseudo-elements that are overlays, not button styling
    .monaco-editor .button[title]::after,
    .monaco-editor .button[aria-label]::after {
      display: none !important;
      content: none !important;
    }
  }
}

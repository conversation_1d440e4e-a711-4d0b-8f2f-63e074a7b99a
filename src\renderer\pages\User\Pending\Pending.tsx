import { Select, MenuItem, Tooltip } from "@mui/material";
import { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useImmer } from "use-immer";
import Loader from "../../../components/common/Loader/Loader";
import { filterArrray } from "../../../utils/helper";
import styles from "./Pending.module.scss";
import MatPopup from "../../../components/common/MatPopup";
import { CreateUserFromSchema, CreateUserFromSchemaType } from "../../../models/createUser.model";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { cloneDeep } from "lodash";
import useGetAllPendingOnBoardUsers from "../../../hooks/useGetAllPendingOnBoardUsers";
import Create from "../Create";
import useCreateUser from "../../../hooks/useCreateUser";
import useCheckValidEmail from "../../../hooks/useCheckValidEmail";
import SucessErrorPopup from "../../../components/common/SucessErrorPopup";
import useHidePendingOnBoardUser from "../../../hooks/useHidePendingOnBoardUser";
import useOnboardUser from "../../../hooks/useOnboardUser";
import SearchBar from "../../../components/common/SearchBox/SearchBox";

const Pending = () => {
  const [filteredaUsers, setFilteredaUsers] = useImmer<any>([]);

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const [callInProgress, setCallInProgress] = useState(false);
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [showNewUserPopup, setShowNewUserPopup] = useImmer(false);
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [formData, setFormdata] = useImmer<CreateUserFromSchemaType | null>(
    null
  );
  const [userId, setuserId] = useImmer("");
  const [sucessErrorPopupMessage, setSucessErrorPopupMessage] = useImmer<any>("");
  const [hideUserData, setHideUserData] = useImmer<any>(null);
  const [hideUserReason, setHideUserReason] = useImmer("");

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredaUsers.length / itemsPerPage);

  const { data: allUsers, isLoading: allUsersLoading } = useGetAllPendingOnBoardUsers();
  const {
    mutate: createUser,
    isLoading: isCreateUserLoading,
    data: createUserData,
    error: createUserError,
  } = useCreateUser();

  const {
    mutate: onboardUser,
    isLoading: isOnboardUserLoading,
    data: onboardUserData,
    error: onboardUserError,
  } = useOnboardUser();

  const {
    mutate: checkValidEmail,
    isLoading: isCheckValidEmailLoading,
    data: checkValidEmailData,
    error: checkValidEmailError,
  } = useCheckValidEmail();

  const {
    mutate: hidePendingOnBoardUser,
    isLoading: isHidePendingOnBoardUserLoading,
    data: hidePendingOnBoardUserData,
    error: hidePendingOnBoardUserError,
  } = useHidePendingOnBoardUser();

  const {
    setError,
    reset,
    formState: { errors },
  } = useForm<CreateUserFromSchemaType>({
    resolver: yupResolver(CreateUserFromSchema),
  });

  useEffect(() => {
    if(allUsers?.length > 0 && inputSearchValue.length !== 0){
      search(inputSearchValue)
    }else if (allUsers) {
      setFilteredaUsers(allUsers);
    } else {
      setFilteredaUsers([]);
    }
  }, [allUsers, inputSearchValue]);

  useEffect(() => {
    setCurrentPage(0);
    setItemOffset(0);
  }, [itemsPerPage]);

  useEffect(() => {
    if (checkValidEmailError) {
      setShowConfirmationPopup(true);
    } else if (checkValidEmailData) {
      confirmationPopupYes();
    }
  }, [checkValidEmailData, checkValidEmailError]);

  useEffect(() => {
    if (createUserData && !isCreateUserLoading) {
      setApiResponseMessage(createUserData);
    }
  }, [createUserData, isCreateUserLoading]);

  useEffect(() => {
    if((onboardUserData && !isOnboardUserLoading)){
      setApiResponseMessage(onboardUserData);
    }
  }, [onboardUserData, isOnboardUserLoading]);

  useEffect(() => {
    if(createUserError || onboardUserError){
      let errorObj = createUserError ? JSON.parse((createUserError as Error).message) : JSON.parse((onboardUserError as Error).message);
      const pendingCompanyLink = `https://${import.meta.env.VITE_AWS_COGNITO_DOMAIN_COMPARE}/user/pending-company-list`;
      if(errorObj.error_redirect === pendingCompanyLink){
        const companyName = formData?.companyName
        const errorMessage = (
          <>
            <div>
              The company, &quot;{companyName}&quot; does not exist. <a className="companyClickBtn" href={pendingCompanyLink} target="_blank" rel="noopener noreferrer">Click here </a> to approve.
            </div>
          </>
        )
        setSucessErrorPopupMessage(errorMessage)
      }
    }
  }, [createUserError, onboardUserError])

  useEffect(() => {
    if (isHidePendingOnBoardUserLoading) {
      return;
    }

    if (hidePendingOnBoardUserData) {
      setApiResponseMessage(hidePendingOnBoardUserData);
    }
  }, [isHidePendingOnBoardUserLoading, hidePendingOnBoardUserData, hidePendingOnBoardUserError]);

  const handleNewUserData = (user: any) => {
    setShowNewUserPopup(false)
    if ("isExternalApiAdmin" in user) {
      delete user.isExternalApiAdmin;
    }
    setFormdata(cloneDeep(user));
      checkValidEmail({
        data: { email: user.emailAddress },
      });
  }

  const search = (searchValue: string) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(searchValue);
    if (searchValue) {
      const _filterArrray = filterArrray(allUsers, searchValue.trim(), [
        "onboarding_request_date",
        "first_name",
        "last_name",
        "email_id",
        "company_name",
        "client_company",
        "user_type",
      ]);
      if (_filterArrray?.length) {
        setFilteredaUsers(_filterArrray);
      } else {
        setFilteredaUsers([]);
      }
    } else {
      setFilteredaUsers(allUsers ? allUsers : []);
    }
  };

  const handlePageClick = (event: any) => {
    const newOffset = (event.selected * itemsPerPage) % filteredaUsers.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  const confirmationPopupYes = () => {
    if (formData) {
      reset();
      setCallInProgress(true);
      try {
        const passwordString = formData.password?.toString();
        const isCreateUser = passwordString?.length > 0;
        const password = isCreateUser ? passwordString : undefined;
        const data = {
          id: userId,
          first_name: formData.firstName,
          last_name: formData.lastName,
          email_id: formData.emailAddress,
          company_name: formData.companyName ?? null,
          client_company: formData.companyEntity ?? null,
          password,
          zip_code: formData.zipCode,
          user_type: formData.type,
          onboard_source: formData.onboardSource,
        };
        if(isCreateUser)
        createUser({data});
        else
        onboardUser({data});
        setCallInProgress(false);
      } catch (error: any) {
        setError("root", {
          type: "create-user-error",
          message: JSON.stringify(error),
        });
        setCallInProgress(false);
      }
    }
    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setShowNewUserPopup(false);
  };


  const handleCreateUser = (userData: any) => {
    setShowNewUserPopup(true);
    setFormdata(cloneDeep(userData))
    setuserId(userData.id)
    
  }

  const hideUser = () => {
    hidePendingOnBoardUser({ data: { id: hideUserData.id, hide_reason: hideUserReason } });
    cancelHideUser();
  }

  const cancelHideUser = () => {
    setHideUserData(null);
    setHideUserReason("");
  }

  return (
    <div className="contentMain">
      {
        callInProgress ||
        isCheckValidEmailLoading ||
        isCreateUserLoading ||
        isHidePendingOnBoardUserLoading ||
        allUsersLoading ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>
            <SearchBar
              value={inputSearchValue}
              placeholder={"Search"}
              onChange={(event)=>search(event.target.value)}
              onClear={()=> {setInputSearchValue('')}}
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>Created Date</th>
                  <th>First Name</th>
                  <th>Last Name</th>
                  <th>Email</th>
                  <th>Company Name</th>
                  <th>Company Entity/Location</th>
                  <th>Type</th>
                  <th></th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {filteredaUsers?.length ? (
                  filteredaUsers
                    .slice(itemOffset, endOffset)
                    .map((user: any, index: number) => (
                      <tr key={user.id}>
                        <td>{user.onboarding_request_date ? user.onboarding_request_date : 'N/A'}</td>
                        <td>{user.first_name}</td>
                        <td>{user.last_name}</td>
                        <td>{user.email_id}</td>
                        <td>{user.company_name}</td>
                        <td>{user.client_company}</td>
                        <td>{user.user_type}</td>
                        <td>
                          <button
                            className={styles.resetPassBtn}
                            onClick={() =>
                              handleCreateUser(user)
                            }
                          >
                            Create User
                          </button>
                        </td>
                        <td>
                          <button className={styles.resetPassBtn} onClick={() => setHideUserData(user)}>
                            Hide
                          </button>
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={9} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
        </div>
      )}

      <MatPopup className={styles.approveRejectPopup} open={!!hideUserData}>
        <div className={styles.hidePopup}>
          <button className={styles.xbtn} onClick={cancelHideUser}><span>x</span></button>
          <h1>Reason</h1>
          <input
          className={styles.hideinputText}
            type="text"
            onChange={(e) => setHideUserReason(e.target.value)}
            value={hideUserReason}
          />
          <button className={styles.btnHiden} onClick={hideUser} disabled={!hideUserReason?.trim()}>Ok</button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.emailNotvalidPopup}
        open={showConfirmationPopup}
      >
        <div className={styles.emailNotvalidbox}>
          <p className={styles.emailText}>
            Email is not valid <br />
            Do you want to continue ?
          </p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
      <MatPopup
        className={'orderContinuePopup pendingUserPopup'}
        open={showNewUserPopup}
      >
        <div className={styles.tblscrollPop}>
          <div className={styles.continuePopup1}>
            <p className={styles.continuetext}>Create New User</p>
            
            <Create isNewUserCreate = {formData} popupClose={confirmationPopupClose} handleNewUserData={handleNewUserData} setShowNewUserPopup={setShowNewUserPopup} hidePasscode={!formData?.show_password}/>
          </div>
        </div>
      </MatPopup>
      <SucessErrorPopup
        open={!!sucessErrorPopupMessage}
        messageText={sucessErrorPopupMessage}
        onPopupClose={() => {
          setSucessErrorPopupMessage("");
        }}
      />
    </div>
  );
};

export default Pending;

.ErrorDialog {
    .dialogContent {
        max-width: 350px;
        width: 100%;
        display: flex;
        align-items: center;
        padding: 30px 24px;
        object-fit: contain;
        border-radius: 10px;
        background-color: #fff;
        font-family: Noto Sans;
        text-align: center;

        .closeIcon {
            position: absolute;
            top: 10px;
            right: 12px;
            cursor: pointer;
            opacity: 0.5;

            &:hover,
            &:focus {
                opacity: unset;
            }

            svg {
                height: 20px;
                width: 20px;
                color: #000;

                path {
                    fill: #000
                }
            }
        }

        .successPopupTitle {
            font-family: Noto Sans;
            font-size: 18px;
            font-weight: normal;
            line-height: 1.6;
            text-align: center;
            color: #70ff00;
        }

        .content {
          text-align: center;
          font-size: 20px;
          margin-top: 0px;
          margin-bottom: 24px;
          color: var(--primaryColor);
        }

        .actionBtnSection{
            display: flex;
            .submitBtn {
                height: 45px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                padding: 10px 28px;
                border-radius: 4px;
                background-color: transparent;
                background-color: var(--primaryColor);
                font-family: Noto Sans;
                font-size: 14px;
                font-weight: normal;
                line-height: 1.4;
                text-align: center;
                color: #fff;
                transition: all 0.1s;
                border: 0px;
                margin-right: 12px;
                cursor: pointer;
                &:last-child{
                    margin-right: 0px;
                }
                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
            }
        }

    


    }

}
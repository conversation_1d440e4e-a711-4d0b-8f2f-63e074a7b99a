import { useMutation } from "@tanstack/react-query";
import axios from "axios";

export const useSaveHotspots = () => {
  return useMutation(async ({ screenId, hotspots }: { screenId: string; hotspots: any[] }) => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/screens/${screenId}/hotspots`, {
        hotspots
      });

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message ?? "");
    }
  });
};
import React from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  Chip,
  Alert,
} from '@mui/material';
import {
  RadioButtonUnchecked as UnassignedIcon,
  CheckCircle as AssignedIcon,
  Warning as WarningIcon,
  Celebration as CelebrationIcon,
} from '@mui/icons-material';

const UnassignedHotspots = ({ 
  hotspots, 
  selectedHotspotId, 
  onHotspotSelect,
  disabled = false 
}) => {
  // Categorize hotspots
  const unassignedHotspots = hotspots.filter(h => h.element_id && !h.has_video_mapping);
  const assignedHotspots = hotspots.filter(h => h.element_id && h.has_video_mapping);
  const needsIdHotspots = hotspots.filter(h => !h.element_id);

  const totalConfigurableHotspots = hotspots.filter(h => h.element_id).length;
  const completionPercentage = totalConfigurableHotspots > 0 
    ? Math.round((assignedHotspots.length / totalConfigurableHotspots) * 100) 
    : 0;

  return (
    <Paper sx={{ height: 'fit-content', maxHeight: 500, overflow: 'auto' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
        <Typography variant="h6" sx={{ color: '#0d1b2a', mb: 1 }}>
          Mapping Progress
        </Typography>
        
        {/* Progress Summary */}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
          <Chip
            label={`${assignedHotspots.length} mapped`}
            color="success"
            size="small"
            variant="outlined"
          />
          <Chip
            label={`${unassignedHotspots.length} pending`}
            color="warning"
            size="small"
            variant="outlined"
          />
          {needsIdHotspots.length > 0 && (
            <Chip
              label={`${needsIdHotspots.length} need ID`}
              color="error"
              size="small"
              variant="outlined"
            />
          )}
        </Box>
        
        {/* Progress Bar */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              flex: 1,
              height: 6,
              backgroundColor: '#e0e0e0',
              borderRadius: 3,
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                width: `${completionPercentage}%`,
                height: '100%',
                backgroundColor: completionPercentage === 100 ? '#4caf50' : '#2196f3',
                transition: 'width 0.3s ease',
              }}
            />
          </Box>
          <Typography variant="caption" sx={{ color: '#666', minWidth: 'fit-content' }}>
            {completionPercentage}%
          </Typography>
        </Box>
      </Box>

      {/* All Complete State */}
      {unassignedHotspots.length === 0 && needsIdHotspots.length === 0 && assignedHotspots.length > 0 && (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <CelebrationIcon sx={{ fontSize: 48, color: '#4caf50', mb: 2 }} />
          <Typography variant="h6" sx={{ color: '#4caf50', mb: 1 }}>
            All hotspots mapped!
          </Typography>
          <Typography variant="body2" sx={{ color: '#666' }}>
            Every hotspot on this screen has been assigned a video.
          </Typography>
        </Box>
      )}

      {/* Unassigned Hotspots */}
      {unassignedHotspots.length > 0 && (
        <>
          <Box sx={{ p: 2, backgroundColor: '#fff3e0', borderBottom: '1px solid #e0e0e0' }}>
            <Typography variant="subtitle2" sx={{ color: '#e65100', fontWeight: 'bold' }}>
              🎯 Ready for Video Assignment ({unassignedHotspots.length})
            </Typography>
            <Typography variant="caption" sx={{ color: '#bf360c' }}>
              Click to select and assign videos
            </Typography>
          </Box>
          
          <List sx={{ p: 0 }}>
            {unassignedHotspots.map((hotspot, index) => {
              const isSelected = hotspot.id === selectedHotspotId;
              
              return (
                <ListItem
                  key={hotspot.id}
                  disablePadding
                  sx={{
                    borderBottom: index < unassignedHotspots.length - 1 ? '1px solid #f0f0f0' : 'none',
                  }}
                >
                  <ListItemButton
                    selected={isSelected}
                    onClick={() => onHotspotSelect(hotspot.id)}
                    disabled={disabled}
                    sx={{
                      '&.Mui-selected': {
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                        borderLeft: '3px solid #ff9800',
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <UnassignedIcon 
                        sx={{ 
                          color: isSelected ? '#ff9800' : '#ccc',
                          fontSize: 20,
                        }} 
                      />
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={hotspot.element_id}
                      secondary={`Position: ${(hotspot.coords_json.x * 100).toFixed(0)}%, ${(hotspot.coords_json.y * 100).toFixed(0)}%`}
                      primaryTypographyProps={{
                        fontWeight: isSelected ? 'medium' : 'normal',
                        color: isSelected ? '#ff9800' : '#0d1b2a',
                      }}
                    />
                  </ListItemButton>
                </ListItem>
              );
            })}
          </List>
        </>
      )}

      {/* Assigned Hotspots (Collapsible) */}
      {assignedHotspots.length > 0 && (
        <>
          <Box sx={{ p: 2, backgroundColor: '#e8f5e8', borderBottom: '1px solid #e0e0e0' }}>
            <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
              ✅ Completed Mappings ({assignedHotspots.length})
            </Typography>
          </Box>
          
          <List sx={{ p: 0 }}>
            {assignedHotspots.map((hotspot, index) => {
              const isSelected = hotspot.id === selectedHotspotId;
              
              return (
                <ListItem
                  key={hotspot.id}
                  disablePadding
                  sx={{
                    borderBottom: '1px solid #f0f0f0',
                  }}
                >
                  <ListItemButton
                    selected={isSelected}
                    onClick={() => onHotspotSelect(hotspot.id)}
                    disabled={disabled}
                    sx={{
                      '&.Mui-selected': {
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        borderLeft: '3px solid #4caf50',
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <AssignedIcon 
                        sx={{ 
                          color: isSelected ? '#4caf50' : '#81c784',
                          fontSize: 20,
                        }} 
                      />
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={hotspot.element_id}
                      secondary="Video assigned"
                      primaryTypographyProps={{
                        fontWeight: isSelected ? 'medium' : 'normal',
                        color: isSelected ? '#4caf50' : '#0d1b2a',
                      }}
                    />
                  </ListItemButton>
                </ListItem>
              );
            })}
            
            {/* {assignedHotspots.length > 3 && (
              <ListItem>
                <ListItemText
                  primary={
                    <Typography variant="caption" sx={{ color: '#666', textAlign: 'center' }}>
                      +{assignedHotspots.length - 3} more completed
                    </Typography>
                  }
                />
              </ListItem>
            )} */}
          </List>
        </>
      )}

      {/* Needs ID Warning */}
      {needsIdHotspots.length > 0 && (
        <Alert 
          severity="info" 
          icon={<WarningIcon />}
          sx={{ m: 2, backgroundColor: 'rgba(33, 150, 243, 0.1)' }}
        >
          <Typography variant="body2">
            {needsIdHotspots.length} hotspot{needsIdHotspots.length !== 1 ? 's' : ''} need{needsIdHotspots.length === 1 ? 's' : ''} Element IDs before they can be mapped to videos.
          </Typography>
        </Alert>
      )}

      {/* Empty State */}
      {hotspots.length === 0 && (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <UnassignedIcon sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
          <Typography variant="h6" sx={{ color: '#666', mb: 1 }}>
            No hotspots found
          </Typography>
          <Typography variant="body2" sx={{ color: '#999' }}>
            Create some hotspots first to assign videos to them.
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default UnassignedHotspots;

import CloseIcon from '@mui/icons-material/Close';
import UploadIcon from '@mui/icons-material/Upload';
import DescriptionIcon from '@mui/icons-material/Description';
import { useState } from 'react';
import { useTemplateStore } from '../stores/templateStore';
import { extractSubjectFromHtml } from '../utils/emailGenerator';
import styles from './ImportModal.module.scss';

interface ImportModalProps {
  onClose: () => void;
}

export function ImportModal({ onClose }: ImportModalProps) {
  const { setSourceHtml, setSettings, setEditorMode, setViewMode, extractVariables, setIsDirty, setTemplateNameEditable, setTemplateId, setTemplateEvent } = useTemplateStore();
  const [activeTab, setActiveTab] = useState<'paste' | 'upload'>('paste');
  const [pastedHtml, setPastedHtml] = useState('');
  const [fileName, setFileName] = useState('');

  const handleImport = (html: string, templateName: string) => {
    // Set editor mode BEFORE setting HTML so block→source re-generation doesn't overwrite it
    setEditorMode('source');

    setSourceHtml(html);
    extractVariables(html);

    const subject = extractSubjectFromHtml(html);
    setSettings({
      templateName,
      subjectLine: subject || '',
    });
    setTemplateNameEditable(true);
    setTemplateId('');
    setTemplateEvent(''); // Clear event for imported templates

    setViewMode('edit');
    setIsDirty(true);
    onClose();
  };

  const handlePaste = () => {
    if (pastedHtml.trim()) {
      handleImport(pastedHtml, 'New Template');
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFileName(file.name);
      const reader = new FileReader();
      reader.onload = (event) => {
        const content = event.target?.result as string;
        const parsedTemplateName = file.name.replace(/\.[^.]+$/, '') || 'New Template';
        handleImport(content, parsedTemplateName);
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <button className={styles.closeBtn} onClick={onClose}>
            <CloseIcon sx={{ fontSize: 20 }} />
          </button>
          <h2>Import HTML Template</h2>
        </div>

        <div className={styles.importTabs}>
          <button
            className={`${styles.tab} ${activeTab === 'paste' ? styles.active : ''}`}
            onClick={() => setActiveTab('paste')}
          >
            <DescriptionIcon sx={{ fontSize: 16 }} />
            Paste HTML
          </button>
          <button
            className={`${styles.tab} ${activeTab === 'upload' ? styles.active : ''}`}
            onClick={() => setActiveTab('upload')}
          >
            <UploadIcon sx={{ fontSize: 16 }} />
            Upload File
          </button>
        </div>

        <div className={styles.modalContent}>
          {activeTab === 'paste' ? (
            <div className={styles.pasteSection}>
              <textarea
                className={styles.htmlInput}
                placeholder="Paste your HTML template here..."
                value={pastedHtml}
                onChange={(e) => setPastedHtml(e.target.value)}
                rows={15}
              />
              <div className={styles.modalActions}>
                <button className={styles.btnSecondary} onClick={onClose}>
                  Cancel
                </button>
                <button
                  className={styles.btnPrimary}
                  onClick={handlePaste}
                  disabled={!pastedHtml.trim()}
                >
                  Import
                </button>
              </div>
            </div>
          ) : (
            <div className={styles.uploadSection}>
              <label className={styles.fileUploadArea}>
                <input
                  type="file"
                  accept=".html,.htm,.liquid"
                  onChange={handleFileUpload}
                  hidden
                />
                <UploadIcon sx={{ fontSize: 48 }} />
                <p className={styles.uploadText}>
                  {fileName || 'Click to upload or drag and drop'}
                </p>
                <p className={styles.uploadHint}>HTML, HTM, or LIQUID files</p>
              </label>
              <div className={styles.modalActions}>
                <button className={styles.btnSecondary} onClick={onClose}>
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

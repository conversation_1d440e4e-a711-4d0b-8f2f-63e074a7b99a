.htmlCodeView {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
}

.codeToolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.compatibilityBadges {
  display: flex;
  gap: 8px;
}

.badge {
  padding: 4px 8px;
  background: #e7f5ff;
  color: #007bff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.codeActions {
  display: flex;
  gap: 10px;
}

.actionBtn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;

  &:hover {
    background: #f0f0f0;
  }
}

.codeContainer {
  flex: 1;
  overflow: auto;
  background: #f8f8f8;
}

.codeDisplay {
  padding: 20px;
  min-height: 100%;

  code {
    font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #333;
    white-space: pre-wrap;
    word-break: break-word;
  }
}

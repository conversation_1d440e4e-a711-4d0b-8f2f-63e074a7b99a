.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 35px;
    max-height: 700px;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }



    table {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 35px;
        border-collapse: collapse;
        border-spacing: 0;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }

        thead {
          z-index: 100;
          position: relative;
            tr {

                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 6px 12px;
                    color: #fff;
                    height: 35px;
                    position: sticky;
                    top: 0;
                    background: #676f7c;
                    color: #fff;

                }

                td {
                    line-height: 2.5;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;



                }
            }
        }

        tbody {
            background-color: #fff;
            tr {
                margin: 0;


                &:nth-child(even) {
                    background-color: #f2f2f2;
                }

                td {
                    color: var(--primaryColor);
                    font-size: 16px;
                    margin: 0;
                    padding: 6px 12px;
                    height: 42px;

                    .switch {
                        position: relative;
                        display: inline-block;
                        width: 30px;
                        height: 17px;
                      }
                      
                      .switch input { 
                        opacity: 0;
                        width: 0;
                        height: 0;
                      }
                      
                      .slider {
                        position: absolute;
                        cursor: pointer;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background-color: #ccc;
                        -webkit-transition: .2s;
                        transition: .2s;
                      }
                      
                      .slider:before {
                        position: absolute;
                        content: "";
                        height: 13px;
                        width: 13px;
                        left: 2px;
                        bottom: 2px;
                        background-color: white;
                        -webkit-transition: .2s;
                        transition: .2s;
                      }
                      
                      input:checked + .slider {
                        background-color: var(--primaryColor);
                      }
                      
                      input:focus + .slider {
                        box-shadow: 0 0 1px var(--primaryColor);
                      }
                      
                      input:checked + .slider:before {
                        -webkit-transform: translateX(13px);
                        -ms-transform: translateX(13px);
                        transform: translateX(13px);
                      }
                      
                      /* Rounded sliders */
                      .slider.round {
                        border-radius: 34px;
                      }
                      
                      .slider.round:before {
                        border-radius: 50%;
                      }

                }
            }
        }
    }
}

.oaderCenter {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 100px;

}

.searchBox {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;

    .showdropdwn {
        width: 82px;
        height: 38px;
        padding: 4px;
    }

    .searchInput {
        box-shadow: none;
        outline: none;
        height: 38px;
        padding: 6px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;

        position: absolute;
        right: 20px;
    }
}

.PaginationNumber {
    ul {
        list-style: none;
        display: flex;
        padding: 0px;


        li {
            position: relative;
            display: block;
            padding: 10px;
            margin-left: -1px;
            line-height: 1.25;
            color: var(--primaryColor);
            background-color: #fff;
            border: 1px solid #dee2e6;
            cursor: pointer;


            &:active {

                color: #fff;
                background-color: var(--primaryColor);
                border-color: var(--primaryColor);
            }

        }

    }


}

.loaderImg {

    text-align: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 200px;

}

.approveRejectPopup {

    h2 {
        display: none;
    }

    .successfullyUpdated {
        padding: 20px;
        text-align: center;
        width: 300px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 240px;
        }

        .successfullytext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);

            @media screen and (max-width: 768px) and (min-width: 320px) {
                font-size: 18px;
            }

        }

        .okBtn {
            width: 100%;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
        }
    }



}

.orderContinuePopup {
    h2 {
        display: none;
    }

    .continuePopup {
        padding: 20px;
        text-align: center;
        width: 300px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 240px;
        }

        .continuetext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);
        }

        .yesAndnoBtn {
            display: flex;
            gap: 10px;

            .okBtn {
                width: 100%;
                height: 45px;
                border-radius: 6px;
                text-decoration: none;
                gap: 8px;
                border: none;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                background-color: var(--primaryColor);
                color: #fff;
            }

        }
    }
    .continuePopup1 {
        padding: 20px;
        text-align: center;
        width: 370px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 290px;
        }
        .continuetext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);
        }
        .yesAndnoBtn {
            display: flex;
            gap: 10px;
            margin-top: 30px;

            .okBtn {
                width: 100%;
                height: 45px;
                border-radius: 6px;
                text-decoration: none;
                gap: 8px;
                border: none;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                background-color: var(--primaryColor);
                color: #fff;
                &:disabled{
                    opacity: 0.7;
                    cursor: not-allowed;
                }
            }

        }
    }
    .discountPopup {
      padding: 20px;
      text-align: center;
      width: 445px;

      @media screen and (max-width: 768px) and (min-width: 320px) {
          width: 100%;
      }
      .continuetext {
          text-align: center;
          font-size: 20px;
          margin-bottom: 24px;
          color: var(--primaryColor);
      }
      .yesAndnoBtn {
          display: flex;
          gap: 10px;
          margin-top: 30px;

          .okBtn {
              width: 100%;
              height: 45px;
              border-radius: 6px;
              text-decoration: none;
              gap: 8px;
              border: none;
              font-size: 16px;
              font-weight: 500;
              cursor: pointer;
              background-color: var(--primaryColor);
              color: #fff;
              &:disabled{
                  opacity: 0.7;
                  cursor: not-allowed;
              }
          }

      }
    }
}

.passCode {
    display: flex;
    gap: 15px;
    padding-left: 11px;
  
    @media screen and (max-width: 768px) and (min-width: 320px) {
      padding: 0px;
    }
  
    input {
      min-width: 41px;
      height: 34px;
      font-weight: 400;
      // line-height: 1.5;
      color: #495057;
      background-color: #fff;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 20px;
      border-radius: 7px;
  
      @media screen and (max-width: 768px) and (min-width: 320px) {
        width: 100%;
        min-width: 12%;
        margin-top: 10px;
        margin-bottom: 10px;
      }
  
      &::-webkit-inner-spin-button {
        display: none;
      }
    }
  }
  .resetPassBtn{
    height: 30px;
    border-radius: 4px;
    text-decoration: none;
    border: none;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    background-color: var(--primaryColor);
    color: #fff;
    padding: 0px 12px;
  }
  // discount popup 
  .discountDiv{
    display: flex;
    padding-bottom: 10px;
    .checkBoxDiv{
      padding-left: 10px;
    }
    span{
        width: 50%;
        text-align: left;
    }
    .discountRightDiv{
      width: 50%;
      text-align: left;
      display: flex;
      gap: 5px;
      fieldset {
        border: none;
      }
      .InputFieldcss {
        width: 100%;
        font-weight: normal;
        line-height: normal;
        color: #000;
        border-radius: 4px;
        font-size: 15px;
        outline: none;
        div{
          padding: 5px 0px 4px 15px;
          border: 1px solid rgba(0, 0, 0, 0.23);
          border-radius: 4px;
          box-sizing: inherit;
          height: 30px;
        }
      }
      input{
        width: 100%;
        height: 30px;
        padding: 4px 0px 4px 15px;
        font-size: 15px;
        border: 1px solid rgba(0, 0, 0, 0.23);
        border-radius: 4px;
        box-sizing: inherit;    
      }
      .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 17px;
      }
      
      .switch input { 
        opacity: 0;
        width: 0;
        height: 0;
      }
      
      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .2s;
        transition: .2s;
      }
      
      .slider:before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        -webkit-transition: .2s;
        transition: .2s;
      }
      
      input:checked + .slider {
        background-color: var(--primaryColor);
      }
      
      input:focus + .slider {
        box-shadow: 0 0 1px var(--primaryColor);
      }
      
      input:checked + .slider:before {
        -webkit-transform: translateX(13px);
        -ms-transform: translateX(13px);
        transform: translateX(13px);
      }
      
      /* Rounded sliders */
      .slider.round {
        border-radius: 34px;
      }
      
      .slider.round:before {
        border-radius: 50%;
      }
    }
  }

  .listUserEditPopup{
    padding:0px 20px;
    .continuetext{
      font-size: 20px;
      font-weight: 600;
    }
    @media (max-width:767px) {
      padding: 0px 12px;
    }  
    
  }

  .externalApiAdminStatus{
    text-align: center;
  }

   
  .inputFiledResetPass {
    display: flex;
    width: 100%;
    height: 40px;
    align-items: center;
    border-radius: 4px;
    border: 1px solid #ced4da;
    &:focus-within {
      border: 1px solid #000;
    }
    .InputFieldcss {
      border-radius: 4px;
      width: 320px;
      height: 100%;
      padding-left: 20px;
      font-weight: 400;
      line-height: 1.5;
      color: #495057;
      background-color: #fff;
      border: none;
    
      font-size: 15px;

      @media screen and (max-width: 768px) and (min-width: 320px) {
        width: 265px;

      }

    
      &:focus{
        outline: none;
      }
    }
    .pass {
      border-top-right-radius: 0px !important;
      border-bottom-right-radius: 0px !important;
    }

    .showHidePass {
      width: 40px;
      height: 38px;
      border:none;
      cursor: pointer;
      outline: none;
      padding: 0;
      margin: 0;
      border-radius: 4px;
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      border-left: 1px solid #ced4da;
    }

    @media screen and (max-width: 768px) and (min-width: 320px) {
      display: block;
      margin-bottom: 10px;

    }


    .emailText {
      width: 200px;
      font-size: 16px;
      font-weight: 600;

      @media screen and (max-width: 768px) and (min-width: 320px) {
        margin-bottom: 10px;

      }
    }
  }
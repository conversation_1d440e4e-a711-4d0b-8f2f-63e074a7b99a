.previewPane {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.previewToolbar {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.deviceToggle {
  display: flex;
  gap: 10px;
}

.deviceBtn {
  padding: 8px 16px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;

  &.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }
}

.previewContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 20px;
  overflow: auto;
}

/* ── Desktop iframe ── */
.previewIframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* ── Mobile phone wrapper ── */
.phoneWrapper {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
}

.deviceFrame {
  position: relative;
  width: 375px;
  height: 812px;
  border: 8px solid #1a1a1a;
  border-radius: 44px;
  background: #1a1a1a;
  box-shadow:
    0 0 0 2px #333,
    0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  flex-shrink: 0;
}

.deviceNotch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 160px;
  height: 28px;
  background: #1a1a1a;
  border-radius: 0 0 18px 18px;
  z-index: 2;
}

/* iframe inside the phone frame */
.deviceFrame .previewIframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 36px;
  box-shadow: none;
}

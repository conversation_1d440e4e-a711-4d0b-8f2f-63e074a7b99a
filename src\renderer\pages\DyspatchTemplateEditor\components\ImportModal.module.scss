.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modalHeader {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  background: white;
  min-height: 60px;

  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #000000 !important;
    margin: 0;
    padding: 0;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    line-height: 1.5;
    flex: 1;
  }
}

.closeBtn {
  padding: 4px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #666;
}

.importTabs {
  display: flex;
  padding: 0 20px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;

  &.active {
    color: #007bff;
    border-bottom-color: #007bff;
  }
}

.modalContent {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.pasteSection,
.uploadSection {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.htmlInput {
  flex: 1;
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-family: monospace;
  font-size: 13px;
  resize: none;
}

.fileUploadArea {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #007bff;
    background: #f0f8ff;
  }
}

.uploadText {
  margin-top: 16px;
  font-size: 16px;
  color: #333;
}

.uploadHint {
  margin-top: 8px;
  font-size: 14px;
  color: #999;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
}

.btnPrimary,
.btnSecondary {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btnPrimary {
  background: #007bff;
  color: white;

  &:hover {
    background: #0056b3;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
}

.btnSecondary {
  background: white;
  border: 1px solid #ddd;
  color: #333;

  &:hover {
    background: #f0f0f0;
  }
}

import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import InputField from "../../components/common/InputField";
import { useEffect } from "react";
import useGetSettings from "../../hooks/useGetSettings";
import usePostSettings from "../../hooks/usePostSettings";
import Loader from "../../components/common/Loader";
import styles from "./Setting.module.scss";
import { confirmationPopupKeys} from "../../utils/constant";
import useDialogStore from "../../components/common/DialogPopup/DialogStore";

const AutoCancellationDaysSchema = yup.object().shape({
  orderCancellationReminder: yup
    .string()
    .transform((value) => (typeof value === "string" ? value.trim() : value))
    .test(
      "is_valid_format",
      "If you want to enter multiple value enter numeric value with , separated",
      (value) => {
        if (value) {
          let result = true;
          value.split(",").some((element) => {
            if (!+element) {
              result = false;
              return true;
            }
          });
          return result;
        } else {
          return true;
        }
      }
    ),
  orderCancellationHours: yup
    .number()
    .transform((v) => (v ? v : null))
    .typeError("Please enter valid input")
    .default(null)
    .nullable(),
});

type AutoCancellationDaysSchemaType = yup.InferType<
  typeof AutoCancellationDaysSchema
>;

const Setting = () => {
  const {
    control,
    watch,
    reset,
    setError,
    clearErrors,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<AutoCancellationDaysSchemaType>({
    defaultValues: {},
    resolver: yupResolver(AutoCancellationDaysSchema),
  });
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  const { data: settingsData, isLoading: isSettingsDataLoading } =
    useGetSettings();
  const {
    mutate: saveAutoCancellationDays,
    isLoading: issaveAutoCancellationDaysLoading,
    data: autoCancellationDays,
  } = usePostSettings();

  useEffect(() => {
    if (settingsData) {
      let text = settingsData.order_cancellation_reminder;
      text = text ? text.substring(1, text.length - 1) : null;

      reset({
        orderCancellationReminder: text,
        orderCancellationHours: settingsData.order_cancellation_hours,
      });
    }
  }, [settingsData]);

  const autoCancellationDaysSubmitHandler = (
    data: AutoCancellationDaysSchemaType
  ) => {
    if (
      data &&
      (data.orderCancellationReminder ||
        (data.orderCancellationHours && +data.orderCancellationHours))
    ) {
      clearErrors();
    } else {
      setError("root", {
        type: "atleast-one",
        message: "Please fill atleast one field",
      });
    }
    saveAutoCancellationDays({
      data: {
        order_cancellation_reminder: data.orderCancellationReminder
          ? `[${data.orderCancellationReminder}]`
          : null,
        order_cancellation_hours: data.orderCancellationHours + "",
      },
    });
  };

  const showCancelOrderPopup = () => {
    showCommonDialog(null, confirmationPopupKeys.confirmationContent, null, resetDialogStore, 
      [{name: confirmationPopupKeys.confirmation.yes, action: ()=>{cancelOrderPopup()}},{name: confirmationPopupKeys.confirmation.no, action: resetDialogStore}])
   }

  const cancelOrderPopup = () => {
    handleSubmit(autoCancellationDaysSubmitHandler)();
    resetDialogStore()
  };


  return (
    <div className="contentMain">
      {isSettingsDataLoading || issaveAutoCancellationDaysLoading ? (
         <div className="loaderImg">
         <Loader />
       </div>
        
      ) : (
        <div className={styles.settingPage}>
            <div className={styles.orderReminderBox}>
              <p>Order Cancellation Reminder</p>
              <InputField
               className={styles.orderReminderInput}
                control={control}
                fieldName="orderCancellationReminder"
              /></div>

            <div className={styles.orderReminderBox}>
              <p>Order Cancellation Hours</p>
              <InputField
              className={styles.orderReminderInput}
                control={control}
                fieldName="orderCancellationHours"
                type="tel"
              />
            </div>
            <div>
              <button className={styles.submitBtn} onClick={showCancelOrderPopup}>Submit</button>
            </div>
        </div>
      )}
      <p>{errors.root?.message}</p>
    </div>
  );
};

export default Setting;

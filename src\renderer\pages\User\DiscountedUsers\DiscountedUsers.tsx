import { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { Select, MenuItem } from "@mui/material";
import { useImmer } from "use-immer";

import Loader from "../../../components/common/Loader";
import styles from "./DiscountedUsers.module.scss";
import { filterArrray } from "../../../utils/helper";
import useGetDiscountedUsers, { DiscountedUsers as users } from "../../../hooks/useGetDiscountedUsers";
import clsx from "clsx";
import SearchBar from "../../../components/common/SearchBox/SearchBox";

type Props = {};

const DiscountedUsers: React.FC<Props> = () => {
    const { data: discountedUsersData, isLoading: isDiscountedUsersDataLoading } = useGetDiscountedUsers();

    const [inputSearchValue, setInputSearchValue] = useState("");
    const [discountedUsers, setDiscountedUsers] = useImmer<users[]>([]);

    const [itemOffset, setItemOffset] = useState(0);
    const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(0);
    const endOffset = itemOffset + itemsPerPage;
    const pageCount = Math.ceil(discountedUsers.length / itemsPerPage);

    useEffect(() => {
        if (isDiscountedUsersDataLoading) {
            return;
        }

        if (discountedUsersData?.length) {
            const _discountedUserData = discountedUsersData.map((data)=>{
                data.discountPercentage = calculateSpreadInPercentage(data.discount_rate).toFixed(2);
                data.sellerSpreadPercentage = calculateSpreadInPercentage(data.seller_spread_rate).toFixed(2);
                return data;
            })
            setDiscountedUsers(_discountedUserData);
        } else {
            setDiscountedUsers([]);
        }
    }, [discountedUsersData, isDiscountedUsersDataLoading]);

    useEffect(()=>{
        search(inputSearchValue)
    },[inputSearchValue])


    const handlePageClick = (event: any) => {
        const newOffset = (event.selected * itemsPerPage) % discountedUsers.length;
        setCurrentPage(event.selected);
        setItemOffset(newOffset);
    };

    const search = (searchValue: string) => {
        setCurrentPage(0);
        setItemOffset(0);
        setInputSearchValue(searchValue);

        const _searchValue = searchValue.trim();
        if (_searchValue) {
            const _filterArrray = filterArrray(discountedUsersData, _searchValue.trim(), [
                "first_name",
                "last_name",
                "email_id",
                "discountPercentage",
                "discount_pricing_column",
                "is_discount_overriden",
                "sellerSpreadPercentage"
            ]);
            if (_filterArrray?.length) {
                setDiscountedUsers(_filterArrray);
            } else {
                setDiscountedUsers([]);
            }
        } else {
            setDiscountedUsers(discountedUsersData ? discountedUsersData : []);
        }
    };

    const calculateSpreadInPercentage = (spreadRate) => {
        const percentageSpread = (spreadRate - 1) * 100;
        return percentageSpread;
    }

    const addStyleForSpread = (spreadRate) => {
        let className = styles.spreadZero;
        if(spreadRate){
            if(spreadRate > 0){
                className = styles.spreadMarkup;
            }else if(spreadRate < 0) {
                className = styles.spreadMarkdown;
            }
        }
        return className;
    }

    return (
        <div>
            <div className={styles.searchBox}>
                <Select className="editLinesDropdown"
                    MenuProps={{
                        classes: {
                            paper: styles.Dropdownpaper,
                            list: styles.muiMenuList,
                        }
                    }}
                    value={itemsPerPage}
                    onChange={(event) => {
                        setItemsPerPage(+event.target.value);
                    }}
                >
                    {perPageEntriesOptions.map((item, index) => (
                        <MenuItem key={index} value={item}>
                            <span>{item}</span>
                        </MenuItem>
                    ))}
                </Select>
                <SearchBar
                    value={inputSearchValue}
                    placeholder={"Search"}
                    onChange={(event)=>search(event.target.value)}
                    onClear={()=> {setInputSearchValue('')}}
                />
            </div>
            <div className={styles.note}>
                *Buyer/Seller Spread - Red indicates MarkDown and Green indicates Markup
            </div>
             <div className={styles.tblscroll}>
            <table>
                <thead>
                    <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Email</th>
                        <th>Buyer Spread (%)</th>
                        <th>Base Pricing Column</th>
                        <th>Seller Spread (%)</th>
                        <th>Spread Overriden</th>
                    </tr>
                </thead>
                <tbody>
                    {isDiscountedUsersDataLoading ? (
                        <tr><td colSpan={11} className={styles.noDataFound}> <Loader /></td></tr>
                       
                    ) : (
                        <>
                            { discountedUsers?.length ? (
                                <>
                                    {discountedUsers.slice(itemOffset, endOffset).map((user) => (
                                        <tr key={user.email_id}>
                                            <td>{user.first_name}</td>
                                            <td>{user.last_name}</td>
                                            <td>{user.email_id}</td>
                                            <td className={clsx(addStyleForSpread(user.discountPercentage))}><span>{ user.discountPercentage === 'NaN' ? '' : Number(user.discountPercentage) ? Math.abs(Number(user.discountPercentage)).toFixed(2):  Math.abs(Number(user.discountPercentage))}</span> </td>
                                            <td>{user.discount_pricing_column}</td>
                                            <td className={clsx(addStyleForSpread(user.sellerSpreadPercentage))}><span>{ user.sellerSpreadPercentage === 'NaN' ? '' : Number(user.sellerSpreadPercentage) ? Math.abs(Number(user.sellerSpreadPercentage)).toFixed(2) : Math.abs(Number(user.sellerSpreadPercentage)) }</span> </td>
                                            <td>{user.is_discount_overriden /* === "1" ? "Yes" : "No" */}</td>
                                        </tr>
                                    ))}
                                </>
                            ) : (
                                <tr><td colSpan={11} className={styles.noDataFound}>No data found</td></tr>
                            )}
                        </>
                    )}
                </tbody>
            </table>
            </div>
            <div className={"PaginationNumber"}>
            <ReactPaginate
                breakLabel="..."
                nextLabel=">"
                onPageChange={handlePageClick}
                pageRangeDisplayed={5}
                pageCount={pageCount}
                previousLabel="<"
                renderOnZeroPageCount={(props) =>
                    props.pageCount > 0 ? undefined : null
                }
                forcePage={pageCount > 0 ? currentPage : -1}
            />
            </div>
        </div>
    );
};

export default DiscountedUsers;

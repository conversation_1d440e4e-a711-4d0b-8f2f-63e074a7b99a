
import { useCallback, useContext, useEffect, useRef, useState } from "react";
import { CommonCtx } from "../../AppContainer";
import styles from "../VideoUploads.module.scss";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { VideoUploadsFromSchema, VideoUploadsFromSchemaType } from "../../../models/videoUploads.model";
import { useDropzone } from "react-dropzone";
import clsx from "clsx";
import { ReactComponent as UploadImage } from '../../../../assests/images/icon-Upload-Files.svg';
import { ReactComponent as CloseArrow } from '../../../../assests/images/closePop.svg';
import TagsAutoComplete from "./TagsAutoComplete";
import { axios, uploadFileAndGetS3Url } from "@bryzos/giss-ui-library";
import Modal from "./Modal";
import VideoPlayer from "../../../components/VideoPlayer";
import MatPopup from "../../../components/common/MatPopup";
import MultipleFilesSelection from "./MultipleFilesSelection";
import { defaultThumbnailFiles } from "../../../utils/constant";
import useGetAllVideoLibraryTags from "../../../hooks/useGetAllVideoLibraryTags";
import useGetAllVideoLibraryInternalTags from "../../../hooks/useGetAllVideoLibraryInternalTags";
import useGenerateMultiPartUrls from "../../../hooks/useGetMultiPartUrl";
import useInitiateMultiPartUpload from "../../../hooks/useGetUploadId";
import usePostCompleteMultiPartUpload from "../../../hooks/usePostCompleteUpload";
import { v4 as uuidv4 } from 'uuid';

const AddVideo = ({confirmationPopupClose, saveVideo, setShowLoader, setUploadVideoLoaderText}: any) => {
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        getValues,
        formState: { isValid, errors},
    } = useForm<VideoUploadsFromSchemaType>({
        defaultValues: {
            title: '',
            description: '',
            caption: '',
            video_s3_url: null,
            thumbnail_s3_url: {},
            videoTags:[],
            videoInternalTags:[],
            thumbnailFiles: defaultThumbnailFiles,
            subtitle_s3_url: null,
            share_video_url:'',
            is_large_file : false,
        },
        resolver: yupResolver(VideoUploadsFromSchema),
        mode: "onSubmit",
    });
    
    const {
        data: videoLibraryTagsData,
    } = useGetAllVideoLibraryTags();
    
    const {
        data: videoLibraryInternalTagsData,
    } = useGetAllVideoLibraryInternalTags();

    const initiateMultiPartUpload = useInitiateMultiPartUpload();
    const generateMultiPartUrls = useGenerateMultiPartUrls();
    const postCompleteMultiPartUpload = usePostCompleteMultiPartUpload();

    const [acceptedFile, setAcceptedFile] = useState<any>("");
    const [file, setFile] = useState(null);
    const [isPlayingPopup, setPlayingPopup] = useState(false);
    const videoRef = useRef(null);
    const [snapShots, setSnapshots] = useState<any>([]);
    const [videoTags , setVideoTags] =  useState({});
    const [videoInternalTags , setVideoInternalTags] =  useState({});
    const [open, setOpen] = useState(false);
    const [disableCaptureThumbnail, setDisableCaptureThumbnail] = useState(true);
    const [isPlayingPrev , setIsPlayingPrev] = useState(true);
    const [openThumbnailSection, setOpenThumbnailSection] = useState(false);
    const [isFilledAllThumbnails, setIsFilledAllThumbnails] = useState<boolean>(false);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [openConfirmationPopup, setOpenConfirmationPopup] = useState<boolean>(false);
    const [captionFile , setCaptionFile] = useState(null);
    const captionRef = useRef(null);
    const [disableSubmitBtn,setDisableSubmitBtn] = useState(false)
    
    const newTitle = watch("title");

    const showPopupFormAnyComponent = useContext(CommonCtx);

    useEffect(() => {
        if(file){
            setValue('videoFile', file, { shouldValidate: true });
        }
    },[file])

    useEffect( () => {
        if(captionFile) {
            setValue('subtitleFile', captionFile , { shouldValidate: true });
        } else {
            setValue('subtitleFile', undefined , { shouldValidate: true });
        }
    }, [captionFile] )

    useEffect(() => {
        if(snapShots.length){
            const matches = snapShots.match(/^data:image\/([a-zA-Z+]+);base64,/);
            const fileExtension = matches?.[1] ?? 'png'; 
            const thumbnailFormatting = base64ToFile(snapShots, 'thumbnail.'+ fileExtension)
            setValue('thumbnailFile', thumbnailFormatting, { shouldValidate: true });
        }
    },[snapShots])


  useEffect(() => {
    if(watch('thumbnailFiles.thumbnail_app') && watch('thumbnailFiles.thumbnail_safe') && watch('thumbnailFiles.intro_desktop') && watch('thumbnailFiles.electron_player') && watch('thumbnailFiles.intro_mobile') && watch('thumbnailFiles.intro_tablet')){
        setIsFilledAllThumbnails(true);
    }else{
        setIsFilledAllThumbnails(false);
    }
  }, [watch('thumbnailFiles.thumbnail_app'), watch('thumbnailFiles.thumbnail_safe'), watch('thumbnailFiles.intro_desktop'), watch('thumbnailFiles.electron_player'), watch('thumbnailFiles.intro_mobile'), watch('thumbnailFiles.intro_tablet')])


    function uploadedVideoisVideo(item: { actual_filename: any; }) {
        // List of supported video file extensions
        const videoExtensions = ['.mp4', '.mov', '.wmv', '.flv'];
        // Extract the file extension from the filename and check if it's in the list
        const extension = item.actual_filename.split('.').pop().toLowerCase();
        return videoExtensions.includes(`.${extension}`);
    }
    const handleClickOpen = () => {        
        setIsPlayingPrev(false);
        setOpen(true);
    };

    const handleClose = () => {
        setIsPlayingPrev(true);
        setOpen(false);
    };


    const onDrop1 = useCallback((acceptedFiles: any) => {
        if (acceptedFiles.length) {
            const file = acceptedFiles[0];
            const ext = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();
            const allowedExtensions = ['.mp4', '.mov', '.wmv', '.flv', '.avi'];

            if (allowedExtensions.indexOf(ext) === -1) {
                setShowLoader(false)
                showPopupFormAnyComponent(`${file.name} is not supported.`, 'Ok');
                return;
            }

            setFile(file);

            if(file.size / Math.pow(10,9) >= 3.9) {
                setValue('is_large_file' , true , { shouldValidate: true });
            } else {
               setValue('is_large_file' , false , { shouldValidate: true });
            }

            const isVideoType = uploadedVideoisVideo({ actual_filename: file.name });
            if (isVideoType) {
                const videoFileUrl = window.URL.createObjectURL(file);
                setAcceptedFile(videoFileUrl);
            } else {
                const reader = new FileReader();
                reader.readAsDataURL(acceptedFiles[0]);
                reader.onload = (e) => {
                    if (e?.target)
                        setAcceptedFile(e.target.result);
                };
            }
        }
    }, []);


    const { getRootProps: getRootProps1, getInputProps: getInputProps1, open:open1 } = useDropzone({ onDrop: onDrop1, noClick: true });


    const clearSelectedVideo = () => {
        setAcceptedFile("")
        setFile(null)
        setValue('videoFile', undefined, { shouldValidate: true });
        setDisableCaptureThumbnail(true);
        setCaptionFile(null);
        setValue('is_large_file', false , { shouldValidate: true });
        setCaptionFile(null);
    }

    const clearSelectedThumbnail = () => {
        setOpenConfirmationPopup(true);
    }
    const base64ToFile = (base64: string, filename: string): File => {
        const arr = base64.split(',');
        const mime = arr[0].match(/:(.*?);/)?.[1] || '';
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        
        return new File([u8arr], filename, { type: mime });
      };
    

   const scaleFactor = 0.25;

   const capture = (video: any, scaleFactor: any) => {
       if (scaleFactor == null) {
           scaleFactor = 1;
       }
       const w = video.videoWidth * scaleFactor;
       const h = video.videoHeight * scaleFactor;
       const canvas = document.createElement('canvas');
       canvas.width = w;
       canvas.height = h;
       const ctx = canvas.getContext('2d');
       if(ctx) ctx.drawImage(video, 0, 0, w, h);
       return canvas;
   };

   const shoot = () => {
       const video = videoRef.current;
       const canvas = capture(video, scaleFactor);

       canvas.onclick = function() {
           window.open(this.toDataURL('image/jpg'));
       };
       const prevSnapshots = [...snapShots];
       const newSnapshots = [canvas, ...prevSnapshots];
       const snapshotConvertInBase64Format = newSnapshots.slice(0, 1)[0]?.toDataURL();
       const thumbnailFilesObj = {...watch('thumbnailFiles')}
       Object.keys(thumbnailFilesObj).forEach((thumbnailKey:string) => {
        setValue('thumbnailFiles.'+thumbnailKey, snapshotConvertInBase64Format,{ shouldValidate: true })
       });
       setSnapshots(snapshotConvertInBase64Format);
   };

    const uploadVideo = async (video: any) => {
        if (!video) {
            showPopupFormAnyComponent('Please select a video to upload.')
            return;
        }
        const orgVideoName = video.name;
        const fileNameIndex = orgVideoName.lastIndexOf('.');
        const fileName = 'video-' + uuidv4() + orgVideoName.slice(fileNameIndex)
        const uploadId = await initiateMultiPartUpload.mutateAsync(fileName)
        const chunkSize = 100 * 1024 * 1024;
        const totalChunks = Math.ceil(video.size / chunkSize);

        const partETags: any = [];

        const uploadChunk = async (start: number, end: number, partNumber: number) => {
            const chunk = video.slice(start, end);

            const formData = new FormData();
            formData.append('file', chunk);
            formData.append('partNumber', partNumber.toString());
            formData.append('totalParts', totalChunks.toString());

            try {
                const data = await generateMultiPartUrls.mutateAsync({
                    part_number: partNumber,
                    upload_id: uploadId,
                    file_name: fileName
                })

                const S3Uploadresponse = await axios.put(data, chunk);

                const eTag = S3Uploadresponse.headers.etag;
                partETags.push({ PartNumber: partNumber, ETag: eTag });

            } catch (err) {
                console.error('Error uploading part', partNumber, err);
                showPopupFormAnyComponent('Error uploading video part.')
            }
        };

        for (let i = 0; i < totalChunks; i++) {
            const start = i * chunkSize;
            const end = Math.min((i + 1) * chunkSize, video.size);
            await uploadChunk(start, end, i + 1);
        }

        const data = await postCompleteMultiPartUpload.mutateAsync({
            parts: partETags,
            upload_id: uploadId,
            file_name: fileName
        })

        return data;
    };

   const clickOnSubmitBtn = async(data: any) => {
    try{        
        setDisableSubmitBtn(true)
        confirmationPopupClose();
        setShowLoader(true);
        const videoFile  = watch("videoFile");
        const subtitleFile = watch("subtitleFile");

        if(videoFile){
            setUploadVideoLoaderText(true);
            const videoS3Url = await uploadVideo(videoFile);
            setValue('video_s3_url', videoS3Url, { shouldValidate: true })
        }
        if(data?.thumbnailFiles){
            setUploadVideoLoaderText(true); 
            const thumbnailS3UrlObj: any = {};
            for (const thumbnailFileKey of Object.keys(data.thumbnailFiles)) {
                const base64Thumbnail = data.thumbnailFiles[thumbnailFileKey];
                const matches = base64Thumbnail.match(/^data:image\/([a-zA-Z+]+);base64,/);
                const fileExtension = matches?.[1] ?? 'png'; 
                const thumbnailFormatting = base64ToFile(base64Thumbnail, 'thumbnail.'+ fileExtension);
                const thumbnailS3Url = await uploadFileAndGetS3Url(thumbnailFormatting, import.meta.env.VITE_S3_UPLOAD_VIDEO_THUMBNAIL_BUCKET_NAME, '/', import.meta.env.VITE_API_SERVICE+'/user/get_signed_url', 'thumbnail', import.meta.env.VITE_ENVIRONMENT )
                thumbnailS3UrlObj[thumbnailFileKey] = thumbnailS3Url;
            }
            setValue('thumbnail_s3_url', thumbnailS3UrlObj, { shouldValidate: true })
        }
        if(subtitleFile){
            setUploadVideoLoaderText(true); 
            const captionS3Url = await uploadFileAndGetS3Url(subtitleFile, import.meta.env.VITE_S3_UPLOAD_VIDEO_THUMBNAIL_BUCKET_NAME, '/', import.meta.env.VITE_API_SERVICE+'/user/get_signed_url', 'subtitle', import.meta.env.VITE_ENVIRONMENT )
            setValue('subtitle_s3_url', captionS3Url , { shouldValidate: true })
        }

        submitData(watch());
        setShowLoader(false)
        
    }catch(error: any) {
        console.log(error);
        showPopupFormAnyComponent(error?.message)
        setShowLoader(false)
        setUploadVideoLoaderText(false);
    }
    
   }

    const submitData = (data: {
        share_video_url: string; video_s3_url?: string | undefined; thumbnail_s3_url?: string | undefined; videoFile?: {} | undefined; thumbnailFile?: {} | undefined; caption: string; title: string; description: string; videoTags: string[]; is_large_file : Boolean; subtitle_s3_url : string | undefined; videoInternalTags: string[]
    }) => {
        const videoInternalTags = data.videoInternalTags.length ? data.videoInternalTags.join(',')  : null;
        const payload = {
            "data": {
                "title": data.title,
                "caption": data.caption,
                "description": data.description,
                "video_s3_url": data?.video_s3_url ?? null,
                "thumbnail_s3_url": data.thumbnail_s3_url,
                "tags": data.videoTags,
                "share_video_url":data.share_video_url,
                "is_large_file":data.is_large_file,
                "subtitle_s3_url" : data?.subtitle_s3_url ?? null,
                "internal_tags" : videoInternalTags,
            }
        }
        saveVideo(payload)
   }

   const handleOnPlay = () => {
       setDisableCaptureThumbnail(false);
   }
   
   const clearSelectedSubtitle = () => {
    setValue('subtitleFile', undefined, { shouldValidate: true });
    setCaptionFile(null);   
    if(captionRef.current) {
        captionRef.current.value = '';
    } 
   }

   const thumbnailPopupClose = () => {
    setOpenThumbnailSection(false);
    setIsPlayingPrev(true);
   }

   const thumbnailPopupOpen = (_isEdit: boolean) => {
    setOpenThumbnailSection(true);
    setIsPlayingPrev(false);
    setIsEdit(_isEdit)
   }

   const confirmationYes = () => {
    setSnapshots([])
    setValue('thumbnailFiles', defaultThumbnailFiles, { shouldValidate: true });
    confirmationNo()
   }

const confirmationNo = () => {
    setOpenConfirmationPopup(false);
}
   
   const captionBlobUrl = (captionFile:any) => {
          const subtitleBlob = new Blob([captionFile], { type: 'text/vtt' });
          const subtitleUrl = window.URL.createObjectURL(subtitleBlob);
          return subtitleUrl;
   }

   const handleSelectedSubtitle = (e : any) => {
    const vttFile = e.target.files[0];
    const ext = vttFile?.name.substring(vttFile?.name.lastIndexOf(".")).toLowerCase();
    const allowedExtensions = ['.vtt'];
  
    if (allowedExtensions.indexOf(ext) === -1) {
        setShowLoader(false)
        showPopupFormAnyComponent(`${vttFile?.name} is not supported.`, 'Ok');
         return;
    }

    setCaptionFile(vttFile);
   }    
   
    return(
        <div className={styles.continuePopup}>
        <div className={styles.overFlowForPop}>
            <div className={styles.col}>
                <div className={styles.inputField}>
                    <span className={styles.lblInput}>Title<span className={styles.uploadVideoNoteReq}>*</span></span>
                    <input type="text" className={styles.inputBox} {...register('title')} />
                </div>
                <div className={styles.inputField}>
                    <span className={styles.lblInput}>Description</span>
                    <input type="text" className={styles.inputBox} {...register('description')} />
                </div>
            </div>
            <div className={styles.col}>
                <div className={styles.inputField}>
                    <span className={styles.lblInput}>Caption</span>
                    <input type="text" className={styles.inputBox} {...register('caption')} />
                </div>
                <div className={styles.inputField}>
                    <span className={styles.lblInput}>Tags<span className={styles.uploadVideoNoteReq}>*</span></span>
                    <TagsAutoComplete setVideoTags={setVideoTags} videoTags={videoTags} setValue={setValue} valueName={'videoTags'} tagsData={videoLibraryTagsData}  />
                </div>
            </div>
            <div className={styles.col}>
                <div className={styles.inputField}>
                    <span className={styles.lblInput}>Share Video URL</span>
                    <input type="text" className={styles.inputBox} {...register('share_video_url')} />
                </div>
                <div className={styles.inputField}>
                    <span className={styles.lblInput}>Internal Tags</span>
                    <TagsAutoComplete setVideoTags={setVideoInternalTags} videoTags={videoInternalTags} setValue={setValue} valueName={'videoInternalTags'} tagsData={videoLibraryInternalTagsData} />
                </div>
            </div>
            <div className={styles.uploadVideoSection}>
            <div className={styles.uploadVideo}>
                <span>Video</span>
                {!acceptedFile ? (
                    <div className={styles.uploadBox} {...getRootProps1()}>
                        <UploadImage />
                        <p className={styles.uploadHeading}>Drag and Drop</p>
                        <p className={styles.uploadText1}>Drag and drop your files anywhere or</p>
                        <div className={styles.continuePopBtn}>
                          <button className={styles.continueBtn} onClick={open1}>Click here to browse</button>
                        </div>
                        <input {...getInputProps1()} multiple={false} />
                    </div>
                ) : (
                    <div className={styles.uploadBoxImage}>
                        <div className={clsx(styles.newCarousel, styles.popupVideoPreview)}>
                            <div className={styles.closeIcon} onClick={clearSelectedVideo}>
                                <CloseArrow />
                            </div>
                            <VideoPlayer
                                url={acceptedFile}
                                width={"320px"}
                                height={"240px"}
                                id='videoPopup'
                                videoRef={videoRef}
                                onPlay={handleOnPlay}
                                isPlayingPrev = {isPlayingPrev}
                                autoPlay = {false}
                                captionBlobUrl = {captionBlobUrl(captionFile)}
                            />
                        </div>
                    </div>
                )}
                <div className={styles.uploadVideoNote}>**Please upload videos in MP4 format. MOV files may not work on some devices.**</div>
                <div className={clsx(styles.uploadVideoNote, styles.largeVideoNotes)}>Videos above 3.9 GB will not be served.</div>
            </div>
            <div className={styles.uploadVideo}>
                <span>Thumbnail<span className={styles.uploadVideoNote}>*</span></span>
                {(!isFilledAllThumbnails) ? (
                    <>
                    <div className={styles.thumbnailMain}>
                        <div className={styles.captureThumbnail}>
                                <button onClick={()=>thumbnailPopupOpen(false)}>Choose Thumbnails </button>
                            </div>
                            <div className={styles.captureThumbnail}>
                                <span> OR</span>
                                <button onClick={shoot}  disabled={disableCaptureThumbnail}>Capture for Thumbnail</button>
                            </div>
                    </div>
                     
                        
                    </>
                ) : (
                    <>
                      <div className={clsx(styles.uploadBoxImage,styles.uploadBoxThumbImage)}>
                        <div className={styles.closeIcon} onClick={clearSelectedThumbnail}>
                            <CloseArrow />
                        </div>
                        <img src={watch('thumbnailFiles.electron_player')} width={'50%'} height={'50%'} alt="" />
                    </div>
                    <div className={styles.captureThumbnail}>
                     <button onClick={() => thumbnailPopupOpen(true)}>Edit Thumbnails </button>
                   </div>
                    </>
                  
                )}
            </div>
            </div>
           
        </div>
        <Modal
                        open={open}
                        handleClickOpen={handleClickOpen}
                        handleClose={handleClose}
                        extraVideo={acceptedFile}
                        extraTag={watch("videoTags")}
                        extraThumbnail={watch("thumbnailFiles")}
                        extraTitle={newTitle}
                        videoId = {null}
                        extraSequence = {null}
                        extraShow = {1}
                        extraDescription={watch("description")}
                        extraSubtitle = {captionBlobUrl(captionFile)}
                        share_video_url={watch("share_video_url")}
        ></Modal>   
                {acceptedFile ? 
                <div className={styles.addSubTitle}>
                    <div className={styles.captionTitle}>Closed Captioning <span>Please upload *.vtt file only.</span></div>
                    <div>
                        {captionFile ? <div className={styles.uploadFileMain}>
                        <div className={styles.captionFileName}>{captionFile.name}</div>
                        <div className={styles.closeIcon1} onClick={clearSelectedSubtitle}>
                            <CloseArrow />
                        </div>
                    </div> :
                        <div className={styles.uploadContainer}>
                            <label htmlFor="fileUpload" className={styles.uploadLabel}>
                            Upload File
                            </label>
                            <input type="file" id="fileUpload" className={styles.fileInput} ref={captionRef} onChange={handleSelectedSubtitle} />
                        </div>
                        }
                    </div>
                </div> : <></>}  

              {file ? <div className={clsx(styles.col,styles.largeVideoCheckbox)}>
                    <div className={styles.inputField}>
                        <span className={styles.lblInput}>Large Video</span>
                        <input
                              className= {styles.checkbox}
                              type="checkbox"
                              {...register('is_large_file')}
                              onChange={(e)=>{
                                register('is_large_file').onChange(e)
                              }}
                        />
                        <span className={styles.noteLargeVideo}>(Please check this if video is larger than 3.9 GB)</span>
                    </div>
                </div>  : <></>}
         <button
                    onClick={handleClickOpen}
                    className={clsx(styles.previewBtnUploadVideo)} disabled={!isValid}
                >
                    Preview
        </button>

        <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={handleSubmit(clickOnSubmitBtn)} disabled={!isValid || disableSubmitBtn} >
                Submit
            </button>
            <button
                className={styles.okBtn}
                onClick={() => {confirmationPopupClose()}}
            >
                Cancel
            </button>
        </div>
            <MatPopup
                className={styles.orderContinuePopup}
                open={openThumbnailSection}
                classes={{
                    paper:clsx(styles.uploadVideoPopup,styles.multiFilesSelectionPopup)
                }}
            >
                <div className={styles.uploadVideoTitle}>Choose Thumbnails</div>
                <MultipleFilesSelection 
                    confirmationPopupClose={thumbnailPopupClose}
                    setValue={setValue}
                    watch={watch}
                    handleSubmit={handleSubmit}
                    isFilledAllThumbnails={isFilledAllThumbnails}
                    isEdit={isEdit}
                    getValues={getValues}
                    defaultThumbnailFiles={defaultThumbnailFiles}
                    setIsFilledAllThumbnails={setIsFilledAllThumbnails}
                />
            </MatPopup>
            
            <MatPopup
                className={styles.confirmationPopup}
                open={openConfirmationPopup}
            >
                <div className={styles.continuePopup}>
                    <p className={styles.continuetext}>Do you want to continue ?</p>
                    <div className={styles.yesAndnoBtn}>
                        <button className={styles.okBtn} onClick={confirmationYes}>
                            Yes
                        </button>
                        <button className={styles.okBtn} onClick={confirmationNo}>
                            No
                        </button>
                    </div>
                </div>
            </MatPopup>
    </div>
    )
}
export default AddVideo;
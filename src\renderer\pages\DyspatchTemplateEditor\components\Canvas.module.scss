.canvasContainer {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
}

.canvas {
  width: 100%;
  min-height: 400px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  position: relative;

  &.dragOver {
    border-color: #007bff;
    background: #f0f8ff;
  }
}

.emptyCanvas {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #999;
  font-size: 16px;
}

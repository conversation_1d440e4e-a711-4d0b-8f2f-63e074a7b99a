import React, { useState, useRef } from 'react';
import { Stage, Layer, Image, Rect, Text } from 'react-konva';
import { Box, Tooltip } from '@mui/material';
import useImage from 'use-image';
import { useElementSize } from '@mantine/hooks';

const MappingCanvas = ({ 
  screen, 
  hotspots, 
  selectedHotspotId,
  onHotspotSelect 
}) => {
  const [image] = useImage(screen?.image_url);
  const [hoveredHotspotId, setHoveredHotspotId] = useState(null);
  const [stageScale, setStageScale] = useState(1);
  const [stagePos, setStagePos] = useState({ x: 0, y: 0 });
  const { ref, width } = useElementSize();

  // Canvas dimensions
  const canvasWidth = width;
  const canvasHeight = 450;

  // Calculate image scaling to fit canvas while maintaining aspect ratio
  const getImageScale = () => {
    if (!image || !screen) return { scaleX: 1, scaleY: 1, width: 0, height: 0 };
    
    const imageAspect = screen.natural_width / screen.natural_height;
    const canvasAspect = canvasWidth / canvasHeight;
    
    let scaleX, scaleY, width, height;
    
    if (imageAspect > canvasAspect) {
      // Image is wider than canvas
      scaleX = scaleY = canvasWidth / screen.natural_width;
      width = canvasWidth;
      height = screen.natural_height * scaleX;
    } else {
      // Image is taller than canvas
      scaleX = scaleY = canvasHeight / screen.natural_height;
      width = screen.natural_width * scaleX;
      height = canvasHeight;
    }
    
    return { scaleX, scaleY, width, height };
  };

  const imageScale = getImageScale();

  // Convert normalized coordinates to canvas coordinates
  const denormalizeCoords = (normalizedCoords) => {
    return {
      x: normalizedCoords.x * imageScale.width,
      y: normalizedCoords.y * imageScale.height,
      width: normalizedCoords.width * imageScale.width,
      height: normalizedCoords.height * imageScale.height,
    };
  };

  // Handle hotspot click
  const handleHotspotClick = (hotspotId) => {
    onHotspotSelect(hotspotId);
  };

  // Handle zoom
  const handleWheel = (e) => {
    e.evt.preventDefault();
    
    const scaleBy = 1.1;
    const stage = e.target.getStage();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    
    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };
    
    const newScale = e.evt.deltaY > 0 ? oldScale * scaleBy : oldScale / scaleBy;
    const clampedScale = Math.max(0.5, Math.min(2, newScale));
    
    setStageScale(clampedScale);
    setStagePos({
      x: pointer.x - mousePointTo.x * clampedScale,
      y: pointer.y - mousePointTo.y * clampedScale,
    });
  };

  // Get hotspot status color
  const getHotspotColor = (hotspot) => {
    const isSelected = hotspot.id === selectedHotspotId;
    const isHovered = hotspot.id === hoveredHotspotId;

    if (isSelected) {
      return { fill: 'rgba(255, 235, 59, 0.4)', stroke: '#ffeb3b', strokeWidth: 3 };
    }
    
    if (hotspot.has_video_mapping) {
      return { 
        fill: isHovered ? 'rgba(76, 175, 80, 0.4)' : 'rgba(76, 175, 80, 0.2)', 
        stroke: '#4caf50', 
        strokeWidth: isHovered ? 3 : 2 
      };
    }
    
    // Hotspot is ready for video assignment
    return {
      fill: isHovered ? 'rgba(233, 30, 99, 0.4)' : 'rgba(233, 30, 99, 0.2)',
      stroke: '#e91e63',
      strokeWidth: isHovered ? 3 : 2
    };
  };

  return (
    <Box
      sx={{
        border: '1px solid #ccc',
        borderRadius: 1,
        overflow: 'hidden',
        backgroundColor: '#f5f5f5',
        position: 'relative',
      }}
      ref={ref}
    >
      <Stage
        width={canvasWidth}
        height={canvasHeight}
        scaleX={stageScale}
        scaleY={stageScale}
        x={stagePos.x}
        y={stagePos.y}
        onWheel={handleWheel}
        draggable={true}
      >
        <Layer>
          {/* Background Image */}
          {image && (
            <Image
              image={image}
              width={imageScale.width}
              height={imageScale.height}
              listening={false}
            />
          )}
          
          {/* Hotspots */}
          {hotspots.map((hotspot) => {
            const coords = denormalizeCoords(hotspot.coords_json);
            const colors = getHotspotColor(hotspot);
            
            return (
              <React.Fragment key={hotspot.id}>
                <Rect
                  x={coords.x}
                  y={coords.y}
                  width={coords.width}
                  height={coords.height}
                  fill={colors.fill}
                  stroke={colors.stroke}
                  strokeWidth={colors.strokeWidth}
                  dash={hotspot.has_video_mapping ? [] : [5, 5]}
                  onClick={() => handleHotspotClick(hotspot.id)}
                  onTap={() => handleHotspotClick(hotspot.id)}
                  onMouseEnter={() => setHoveredHotspotId(hotspot.id)}
                  onMouseLeave={() => setHoveredHotspotId(null)}
                  style={{ cursor: 'pointer' }}
                />
                
                {/* Element ID Label */}
                {hotspot.element_id && (hoveredHotspotId === hotspot.id || selectedHotspotId === hotspot.id) && (
                  <Text
                    x={coords.x}
                    y={coords.y - 20}
                    text={hotspot.element_id}
                    fontSize={12}
                    fill="#0d1b2a"
                    fontStyle="bold"
                    padding={4}
                    cornerRadius={4}
                    shadowColor="rgba(0,0,0,0.3)"
                    shadowBlur={2}
                    shadowOffset={{ x: 1, y: 1 }}
                    listening={false}
                  />
                )}
              </React.Fragment>
            );
          })}
        </Layer>
      </Stage>
      
      {/* Legend */}
      <Box
        sx={{
          position: 'absolute',
          top: 6,
          right: 6,
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          p: .75,
          borderRadius: 1,
          fontSize: '10px',
          backdropFilter: 'blur(10px)',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
          <Box sx={{ width: 10, height: 10, backgroundColor: '#4caf50', mr: 1, borderRadius: 0.5 }} />
          <span>Has Video</span>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
          <Box sx={{ width: 10, height: 10, backgroundColor: '#e91e63', mr: 1, borderRadius: 0.5 }} />
          <span>No Video</span>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 10, height: 10, backgroundColor: '#ffeb3b', mr: 1, borderRadius: 0.5, border: '1px solid #ffc107' }} />
          <span>Currently Selected</span>
        </Box>
      </Box>
      
      {/* Instructions */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 8,
          left: 8,
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          p: 1,
          borderRadius: 1,
          fontSize: '0.75rem',
        }}
      >
        Click hotspots to assign videos • Scroll to zoom • Drag to pan
      </Box>
    </Box>
  );
};

export default MappingCanvas;

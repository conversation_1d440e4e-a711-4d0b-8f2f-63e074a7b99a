import React, { useState } from 'react';
import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON>,
  Typography,
  Button,
  Chip,
  Box,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VideoLibrary as VideoLibraryIcon,
  Check as CheckIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../../../contexts/NotificationContext';
import { getAdminVideoTooltipScreenHotspotsRoute, getAdminVideoTooltipScreenMappingsRoute } from '../../../../utils/constant';
import { useDeleteScreen } from '../../../../hooks/admin-video-tooltip/useDeleteScreen';
import { useUpdateScreen } from '../../../../hooks/admin-video-tooltip/useUpdateScreen';

interface ScreenCardProps {
  screen: any;
  onScreenChange: () => void;
}

const ScreenCard = ({ screen, onScreenChange }: ScreenCardProps) => {
  const navigate = useNavigate();
  const { showNotification } = useNotification() as { showNotification: (message: string, severity?: string) => void };
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  // React Query hooks
  const deleteScreenMutation = useDeleteScreen();
  const updateScreenMutation = useUpdateScreen();
  
  // Check is_active once and reuse
  const isActive = screen.is_active === 1;
  
  // Loading state from mutations
  const loading = deleteScreenMutation.isLoading || updateScreenMutation.isLoading;

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleToggleActive = async () => {
    try {
      await updateScreenMutation.mutateAsync({
        screenId: screen.id,
        data: { is_active: isActive ? 0 : 1 }
      });
      showNotification(
        `Screen ${isActive ? 'deactivated' : 'activated'} successfully!`,
        'success'
      );
      onScreenChange();
    } catch (error) {
      console.log('Failed to update screen:', error);
      showNotification('Failed to update screen. Please try again.', 'error');
    }
  };

  const handleDelete = async () => {
    try {
      await deleteScreenMutation.mutateAsync(screen.id);
      showNotification('Screen deleted successfully!', 'success');
      setDeleteDialogOpen(false);
      onScreenChange();
    } catch (error) {
      console.log('Failed to delete screen:', error);
      showNotification('Failed to delete screen. Please try again.', 'error');
    }
  };

  const getStatusText = () => {
    return isActive ? 'Active' : 'Inactive';
  };

  return (
    <>
      <Card
      variant='outlined'
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
         
        }}
      >
        <CardContent sx={{ flex: 1 }}>
          {/* Header with name and menu */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'semibold',
                color: '#0d1b2a',
                flex: 1,
                mr: 1,
              }}
            >
              {screen.name}
            </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Chip
                            label={getStatusText()}
                            icon={isActive ? <CheckIcon /> : <CloseIcon />}
                            size="small"
                            color={isActive ? 'success' : 'error'}
                            sx={{
                              '& .MuiChip-label': {
                                fontSize: '10px',
                              },
                              '& .MuiChip-icon': {
                                fontSize: '14px',
                              },
                            }}
                          />
                          <IconButton
                            size="small"
                            onClick={handleMenuOpen}
                            sx={{ color: '#778da9' }}
                          >
                            <MoreVertIcon />
                          </IconButton>
              </Box>
           
          </Box>
         

          {/* Image Preview */}
          <Box
            sx={{
              width: '100%',
              height: 140,
              backgroundColor: '#f5f5f5',
              border: '1px solid #e0e0e0',
              borderRadius: 1,
              mb: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
            }}
          >
            {screen.image_url ? (
              <img
                src={screen.image_url}
                alt={screen.name}
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain',
                }}
              />
            ) : (
              <Typography variant="body2" color="textSecondary">
                No preview
              </Typography>
            )}
          </Box>

          {/* Stats */}
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ color: '#415a77', fontWeight: 'bold' }}>
                {screen.hotspots_count || 0}
              </Typography>
              <Typography variant="caption" sx={{ color: '#778da9' }}>
                Hotspots
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ color: '#4caf50', fontWeight: 'bold' }}>
                {screen.mapped_videos_count || 0}
              </Typography>
              <Typography variant="caption" sx={{ color: '#778da9' }}>
                Videos
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ color: '#ff9800', fontWeight: 'bold' }}>
                {screen.pending_mappings_count || 0}
              </Typography>
              <Typography variant="caption" sx={{ color: '#778da9' }}>
                Pending
              </Typography>
            </Box>
          </Box>
        </CardContent>

        <CardActions sx={{ p: 2, pt: 0 }}>
          <Button
            size="small"
            startIcon={<VisibilityIcon />}
            onClick={() => navigate(getAdminVideoTooltipScreenHotspotsRoute(screen.id))}
            sx={{ color: '#415a77' }}
          >
            Hotspots
          </Button>
          <Button
            size="small"
            startIcon={<VideoLibraryIcon />}
            onClick={() => navigate(getAdminVideoTooltipScreenMappingsRoute(screen.id))}
            sx={{ color: '#415a77' }}
          >
            Video-Mappings
          </Button>
        </CardActions>
      </Card>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => { handleToggleActive(); handleMenuClose(); }}>
          <FormControlLabel
            control={<Switch checked={isActive} size="small" />}
            label={isActive ? 'Deactivate' : 'Activate'}
            sx={{ m: 0 }}
          />
        </MenuItem>
        <MenuItem onClick={() => { setDeleteDialogOpen(true); handleMenuClose(); }}>
          <DeleteIcon sx={{ mr: 1, fontSize: 20 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle component='h4'>Delete Screen</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{screen.name}"? This will also delete all associated hotspots and video mappings.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDelete}
            color="error"
            disabled={loading}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ScreenCard;

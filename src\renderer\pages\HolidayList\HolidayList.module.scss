.holidayListMain {
    padding: 20px 0px;
    height: calc(100% - 100px);

    .addHolidayBtnSection {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        column-gap: 12px;

        .btnSectionTop{
            display: flex;
            column-gap: 12px;
        }

        @media (max-width:991px) {
            flex-direction: column;
            row-gap: 12px;
            .addBtnPopup{
                margin-left: auto;
                display: flex;
            }
        }
        h3{
            font-family: Noto Sans;
            font-size: 24px;
            font-weight: 600;
            margin-top: 0px;
            margin-bottom: 8px;
        }
        p{
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: 500;
            margin-top: 0px;
            margin-bottom: 0px;
        }
    }

}

.buttonContainer{
    display: flex;
    flex-direction: row;
    column-gap: 10px;
}

.addPopupBtn {
    width: 180px;
    height: 40px;
    border-radius: 4px;
    text-decoration: none;
    border: none;
    font-family: Noto Sans;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    background-color: var(--primaryColor);
    color: #fff;
    padding: 6px 8px;
}

.addHolidayPopup.addHolidayPopup {
    padding: 20px;
    width: 100%;
    max-width: 1020px;

    .addLines,.getHolidays{
        height: 38px;
        border-radius: 4px;
        text-decoration: none;
        border: none;
        font-family: Noto Sans;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;
        margin-bottom: 12px;
        padding: 6px 12px;
        @media (max-width:480px) {
            font-size: 14px;
            padding: 4px 8px;
        }
    }

    .timerInputMain{
        width: 100%;
        min-height: 40px;
        padding:6px 10px;
        font-weight: 400;
        line-height: 1.5;
        font-family: Noto Sans;
        font-size: 14px;
        color: var(--primaryColor);
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        flex: 1;
    }

    .inputSection{
        display: flex;
        flex-direction: column;
        max-height: 280px;
        overflow: auto;
        margin-bottom: 20px;
        padding-right: 6px;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }
      
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }
      
        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }

        .addHolidaysHeading.addHolidaysHeading{
            background-color: var(--primaryColor);
            padding: 8px 10px;
            position: sticky;
            z-index: 999;
            top: 0px;
            font-family: Noto Sans;
            font-size: 14px;
            color: #fff;
        }
      

        .inputRow{
            display: grid;
            align-items: center;
            column-gap: 10px;
            grid-template-columns:32% 15% 11% 14% 18% 35px;
            padding-bottom: 12px;

            .addedHolidaysInput1{
                width: 100%;
                min-height: 40px;
                padding:6px 10px;
                font-weight: 400;
                line-height: 1.5;
                font-family: Noto Sans;
                font-size: 14px;
                color: var(--primaryColor);
                background-color: #fff;
                border: 1px solid #ced4da;
                border-radius: 0.25rem;
                flex: 1;
                opacity: 0.7;
                cursor: not-allowed;
                display: flex;
                align-items: center;
            }
            
            .delRow{
               background-color: transparent;
               padding: 0px;
               border: 0px;
               cursor: pointer;
               svg{
                width: 24px;
                height: 24px;
                path{
                    fill:#ff0000
                }
               }
            }
        }

        .inputGroup{
            border-bottom: solid 1px #b3b2b2;
            padding-top: 12px;
            padding-left: 3px;
        }
       
        .InputField {
            width: 100%;
            height: 40px;
            padding: 10px;
            font-weight: 400;
            line-height: 1.5;
            font-family: Noto Sans;
            font-size: 14px;
            color: var(--primaryColor);
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            flex: 1;

            &:focus{
                outline-color: var(--primaryColor);
            }
        }
        
        .textareaError {
            border: 1px solid red;
            &:focus{
            outline: 0;
            }
        }
    
    }
   
    .btnSection {
        display: flex;
        column-gap: 12px;

        button {
            width: 50%;
            height: 40px;
            border-radius: 4px;
            text-decoration: none;
            border: none;
            font-family: Noto Sans;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
            padding: 6px 12px;

            &:disabled {
                opacity: 0.7;
                cursor: not-allowed;
            }
        }
    }
}

.previewPopup.previewPopup {
    padding: 20px;
    width: 100%;
    max-width: 890px;
    
    @media (max-width:767px) {
       margin: 12px;
    }

    .addLines,.getHolidays{
        height: 38px;
        border-radius: 4px;
        text-decoration: none;
        border: none;
        font-family: Noto Sans;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;
        margin-bottom: 12px;
        padding: 6px 12px;
        @media (max-width:480px) {
            font-size: 14px;
            padding: 4px 8px;
        }
    }

    .timerInputMain{
        width: 100%;
        min-height: 40px;
        padding:6px 10px;
        font-weight: 400;
        line-height: 1.5;
        font-family: Noto Sans;
        font-size: 14px;
        color: var(--primaryColor);
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        flex: 1;
    }

    .inputSection{
        display: flex;
        flex-direction: column;
        max-height: 280px;
        overflow: auto;
        margin-bottom: 20px;
        padding-right: 6px;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }
      
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }
      
        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }

        .addHolidaysHeading.addHolidaysHeading{
            background-color: var(--primaryColor);
            padding: 8px 10px;
            position: sticky;
            z-index: 999;
            top: 0px;
            font-family: Noto Sans;
            font-size: 14px;
            color: #fff;
        }
      

        .inputRow{
            display: grid;
            align-items: center;
            column-gap: 10px;
            grid-template-columns:32% 15% 11% 14% 18% 35px;
            padding-bottom: 12px;

            .addedHolidaysInput1{
                width: 100%;
                min-height: 40px;
                padding:6px 10px;
                font-weight: 400;
                line-height: 1.5;
                font-family: Noto Sans;
                font-size: 14px;
                color: var(--primaryColor);
                background-color: #fff;
                border: 1px solid #ced4da;
                border-radius: 0.25rem;
                flex: 1;
                opacity: 0.7;
                cursor: not-allowed;
                display: flex;
                align-items: center;
            }
            
            .delRow{
               background-color: transparent;
               padding: 0px;
               border: 0px;
               cursor: pointer;
               svg{
                width: 24px;
                height: 24px;
                path{
                    fill:#ff0000
                }
               }
            }
        }

        .inputGroup{
            border-bottom: solid 1px #b3b2b2;
            padding-top: 12px;
        }
       
        .InputField {
            width: 100%;
            height: 40px;
            padding: 10px;
            font-weight: 400;
            line-height: 1.5;
            font-family: Noto Sans;
            font-size: 14px;
            color: var(--primaryColor);
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            flex: 1;
        }
        
        .textareaError {
            border: 1px solid red;
            &:focus{
            outline: 0;
            }
        }
    
    }
   
    .btnSection {
        display: flex;
        column-gap: 12px;

        button {
            width: 50%;
            height: 40px;
            border-radius: 4px;
            text-decoration: none;
            border: none;
            font-family: Noto Sans;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
            padding: 6px 12px;

            &:disabled {
                opacity: 0.7;
                cursor: not-allowed;
            }
        }
    }
}

.previewPopupMain {
    .previewPopupHeader {
        margin-bottom: 12px;
        display: flex;
        justify-content: space-between;

        .deliveryDateTitle {
            font-family: Noto Sans;
            font-size: 22px;
            font-weight: 600;
            color: var(--primaryColor);
        }
        .closeIcon{
            background-color: transparent;
            border: 0px;
            padding: 0px;
            position: absolute;
            top:12px;
            right: 12px;
            cursor: pointer;
            svg{
                width: 24px;
                height: 24px;
                path{
                    fill: #ff0000;
                }
            }
        }
    }

    .inputRow {
        display: grid;
        grid-template-columns: 44% 43% auto;
        column-gap: 12px;
        margin-bottom: 16px;
        align-items: flex-end;
        
        @media (max-width:767px) {
            flex-direction: column;
            row-gap: 18px;
            grid-template-columns: unset;
        }

        .lblCheckOut{
            font-family: Noto Sans;
            font-size: 14px;
            margin-bottom: 3px;
            span{
                font-weight: 600;
            }
        }
 
        .checkoutDateMain{
            display: flex;
            flex-direction: column;
            .checkoutDateCol{
                display: flex;
                column-gap: 12px;
            }
        }
      
    }

    .autocompleteDescPanel {
        border-radius: 4px;
        width: 100%;
        max-width: 350px;
        height: 48px;
    }

    .autocompleteDescInnerPanel.autocompleteDescInnerPanel {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        background-color: #fff;
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        padding-right: 4px;
        border-radius: 0px 0px 4px 4px;
    }

    .listAutoComletePanel.listAutoComletePanel {
        width: 100%;
        max-height: 316px;
        padding: 6px 4px 6px 10px;
        margin-top: 4px;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }


        li {
            font-family: Noto Sans;
            font-size: 16px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: var(--primaryColor);
            box-shadow: none;
            padding: 4px 8px;
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 4px;

            &:hover {
                border-radius: 2px;
                background-color: #fff;
                color: #000;
            }

            &[aria-selected="true"] {
                background-color: #fff;
                color: #000;
            }
        }
    }

    .selectDropdown.selectDropdown {
        width: 100%;
        max-width: 100%;
        height: 40px;
        font-family: Noto Sans;
        font-size: 14px;
        &:focus{
            label{
                top:0;
            }
        }
        label{
            font-family: Noto Sans;
            font-size: 16px;
            color: var(--primaryColor);
        }
        input{
            padding: 0px;
        }
    }

    .checkAvailBtn{
        cursor: pointer;
        font-family: Noto Sans;
        font-size: 12px;
        background-color: var(--primaryColor);
        color: #fff;
        border: 0px;
        border-radius: 4px;
        height: 40px;
        &[disabled]{
            opacity: 0.5;
            cursor: not-allowed;
        }
    }
}

.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    margin-top: 16px;
    margin-bottom: 20px;
    max-height: 300px;

    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }

    table {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 35px;
        border-collapse: collapse;
        border-spacing: 0;

        thead {
            tr {
                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-family: Noto Sans;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 6px 12px;
                    color: #fff;
                    height: 35px;
                    position: sticky;
                    top: 0;
                    background: #676f7c;
                    color: #fff;
                }

                td {
                    font-family: Noto Sans;
                    line-height: 2.5;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                }
            }
        }

        tbody {
            background-color: #fff;
            tr {
                margin: 0;
                &:nth-child(even) {
                    background-color: #f2f2f2;
                }

                td {
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 6px 12px;
                    height: 42px;
                }
            }
        }
    }
}


.agGridAdmin {
    --ag-icon-font-code-asc: '\25B2';
    --ag-icon-font-code-desc: '\25BC';
    --ag-icon-font-code-none: '\25B2\25BC';

    .ag-center-cols-viewport {
        min-height: 5000px !important;
    }

    .ag-icon-asc::before {
        content: var(--ag-icon-font-code-asc);
    }

    .ag-icon-none::before {
        content: var(--ag-icon-font-code-none);
        color: green;
        padding: 2px;
        margin-bottom: 5px;
        font-size: 20px !important;
    }

    .ag-root-wrapper {
        .ag-root-wrapper-body {
            .ag-body-horizontal-scroll-viewport {
                overflow-x: auto;

                &::-webkit-scrollbar {
                    width: 8px;
                    height: 6px;
                }

                &::-webkit-scrollbar-track {
                    box-shadow: inset 0 0 6px #a8b2bb;
                    border-radius: 4px;
                }

                &::-webkit-scrollbar-thumb {
                    background: #a8b2bb;
                    border-radius: 4px;
                }
            }

            .ag-header-row {
                .ag-header-cell {
                    padding-left: 20px;
                    padding-right: 20px;
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    color: #fff;
                    background: #676f7c;

                    &:hover {
                        color: #fff;
                        background: #676f7c;
                    }
                }

                .ag-header-cell:not(.ag-column-resizing)+.ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover {
                    color: #fff;
                    background: #676f7c;
                }
            }

            .ag-body-viewport-wrapper.ag-layout-normal {
                overflow-x: scroll;
                overflow-y: scroll;
            }

            ::-webkit-scrollbar {
                -webkit-appearance: none;
                width: 8px;
                height: 6px;
            }

            ::-webkit-scrollbar-thumb {
                border-radius: 4px;
                background: #a8b2bb;
                box-shadow: inset 0 0 6px #a8b2bb;
            }

            .ag-body {
                .ag-body-viewport {
                    .ag-center-cols-clipper {
                        .ag-row-odd {
                            background-color: #f2f2f2;
                        }

                        .ag-cell {
                            cursor: pointer;
                        }

                        .red-border {
                            border: 1px solid red;
                        }
                    }
                }
            }
        }
    }
}

.ag_theme_quartz {
    --ag-foreground-color: #676f7c;
    --ag-background-color: white;
    --ag-header-foreground-color: white;
    --ag-header-background-color: #676f7c;
    --ag-odd-row-background-color: #f2f2f2;
    --ag-header-column-resize-handle-color: rgb(126, 46, 132);
    --ag-font-size: 14px;
    --ag-font-family: monospace;
    --ag-icon-font-code-aggregation: "\f247";
    --ag-icon-font-color-group: red;
    --ag-icon-font-weight-group: normal;
}

.delBtnTbl{
    font-family: Noto Sans;
    border-radius: 4px;
    text-decoration: none;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    background-color: var(--primaryColor);
    color: #fff;
    padding: 6px 12px;
}

.noDataFound{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}


.truncateText{
    &::after{
        content: '';
        display: block;
      }

    p{
        width: 267px; 
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
} 

  .holidayTooltip.holidayTooltip {
    background-color: var(--primaryColor);
    padding: 6px 12px;
    border-radius: 6px;
    max-width: 381px;
    margin-left: 7px;
    margin-bottom: 7px !important;
    margin-top: 0px;
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
    color: #fff;
  }

  .previewNote{
    margin-bottom: 18px;
    font-size: 14px;
    color: #ff0000;
    
    @media (max-width:767px) {
       font-size: 12px;
       margin-bottom: 16px;
    }
  }
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const useUpdateTnc = () => {
  return useMutation(async () => {
    try {
      const response = (await axios.post(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/updateTncVersion`, null)).data;

      if (response?.data) {
        if (typeof response.data === "object" && "error_message" in response.data) {
          throw new Error(response.data.error_message);
        } else {
          return response.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message ?? "");
    }
  });
};

export default useUpdateTnc;

import { useImmer } from "use-immer";
import Loader from "../../components/common/Loader";
import useGetCancelOrderData from "../../hooks/useGetCancelOrderData";
import usePostCancelOrderData from "../../hooks/usePostCancelOrderData";
import { format2DecimalPlaces } from "../../utils/helper";
import { useEffect, useState } from "react";
import { MenuItem, Select } from "@mui/material";
import ReactPaginate from "react-paginate";
import styles from "./CancelOrder.module.scss";
import MatPopup from "../../components/common/MatPopup";
import usePostCloseOrderData from "../../hooks/usePostCloseOrderData";
import { cloneDeep } from "lodash-es";
import { useDebouncedValue } from "@mantine/hooks";
import SearchBar from "../../components/common/SearchBox/SearchBox";

const CancelOrder = () => {
  const [filteredOrders, setFilteredOrders] = useImmer([]);

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [debouncedInputSearchValue] = useDebouncedValue(inputSearchValue.trim(), 1000);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [poLists, setPoLists] = useImmer<any>([]);
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [showPoLinesPopup, setShowPoLinesPopup] = useImmer(false);
  const [disabledSubmitPoLineBtn, setDisabledSubmitPoLineBtn] = useImmer(true);
  const [cancelPoLineData, setCancelPoLineData] = useImmer<any>(null);
  const [closePoLineData, setClosePoLineData] = useImmer<any>(null);
  const [isAllPoLineChecked, setIsAllPoLineChecked] = useImmer<boolean>(false);
  const [meta, setMeta] = useImmer<any>(null);

  const BUYER_ORDER_CANCEL = "BUYER_ORDER_CANCEL";
  const SELLER_ORDER_CANCEL = "SELLER_ORDER_CANCEL";

  const { 
    data: cancelOrderData, 
    isLoading: isCancelOrderDataLoading,
    isFetching: isCancelOrderDataFetching,
   } =
    useGetCancelOrderData(itemsPerPage, currentPage, encodeURIComponent(debouncedInputSearchValue));
  const {
    mutate: saveCancelOrder,
    data: saveCancelOrderData,
    isLoading: isSaveCancelOrderDataLoading,
  } = usePostCancelOrderData();

  const {
    mutate: saveCloseOrder,
    data: saveCloseOrderData,
    isLoading: isSaveCloseOrderDataLoading,
  } = usePostCloseOrderData();

  useEffect(() => {
    if (cancelOrderData?.meta) {
      setMeta(cancelOrderData.meta);
    }

    if (cancelOrderData?.data?.length >= 0) {
      const modifiedArr = cancelOrderData?.data.map((obj: any) => ({
        ...obj,
        editMode: false,
      }));
      setFilteredOrders(modifiedArr);
    }
  }, [cancelOrderData, isCancelOrderDataFetching, isCancelOrderDataLoading]);


  useEffect(() => {
    if (saveCancelOrderData) {
      setApiResponseMessage(saveCancelOrderData);
    }
  }, [saveCancelOrderData]);

  useEffect(() => {
    if (saveCloseOrderData) {
      setApiResponseMessage(saveCloseOrderData);
    }
  }, [saveCloseOrderData]);

  const search = (searchValue: string) => {
    setInputSearchValue(searchValue);
    setCurrentPage(1);
  };


  const confirmationPopupClose = () => {
    setIsAllPoLineChecked(false);
    setShowPoLinesPopup(false);
    setCancelPoLineData(null);
    setClosePoLineData(null);
  };

  const handlePageClick = (event: any) => {
    setCurrentPage(event.selected + 1);
  };

  const displayPoLinesHandler = (poNumber: any, poLines: any, cancelType: string) => {
    setShowPoLinesPopup(true);
    let createPoList = [];

    if(poLines.length === 1){
      createPoList = poLines.map((_poLine: any) => {
        const poLine = cloneDeep(_poLine);
        if(poLine.is_buyer_order_open || cancelType === ''){
          poLine.isChecked = true;
          return poLine
        }
      }).filter(Boolean)
      setDisabledSubmitPoLineBtn(createPoList.length < 1)
    }else{
      createPoList = poLines.map((_poLine: any) => {
        const poLine = cloneDeep(_poLine);
        poLine.isChecked = false;
        if(poLine.is_buyer_order_open || cancelType === ''){
          return poLine
        }
      }).filter(Boolean)
      setDisabledSubmitPoLineBtn(true)
    }
    setPoLists(createPoList)
    if(cancelType){
      setCancelPoLineData({
        data: { po_number: poNumber, type: cancelType, "purchase_order_line_id": {}},
      });
    }else{
      setClosePoLineData({
        "data": {
          "po_number": poNumber,
          "purchase_order_line": {}
        }
      })
    }
  };

  const poLineChangeHandler = (event: any, i: number) => {
    setPoLists((prev: any) => {
      prev[i].isChecked = event.target.checked;
      const checkedPo = prev.filter((po: any) => po.isChecked === true);
      setDisabledSubmitPoLineBtn(checkedPo?.length < 1)
      setIsAllPoLineChecked(checkedPo.length === poLists?.length);
      return prev;
    });
  };

  const allPoLineChecked = (event: any) => {
    const isChecked = event.target.checked;
    setPoLists((prev: any) => {
      prev.forEach((po: any) => { po.isChecked = isChecked; });
      return prev;
    });
    setIsAllPoLineChecked(isChecked);
    setDisabledSubmitPoLineBtn(!isChecked);
  }
  const submitSelectedPoLineClose = () => {
    const _closePoLineData = closePoLineData ? JSON.parse(JSON.stringify(closePoLineData)) : null;

    poLists.forEach((poList: any, i: number) => {
      if (poList.isChecked === true) {
        if(cancelPoLineData){
          cancelPoLineData.data.purchase_order_line_id = { ...cancelPoLineData.data.purchase_order_line_id, [poList.po_line]: poList.purchase_order_line_id }
        }
        if(closePoLineData){
          if (Array.isArray(_closePoLineData.data.purchase_order_line)) {
            _closePoLineData.data.purchase_order_line.push({ id: poList.purchase_order_line_id, po_line: poList.po_line });
          } else {
            _closePoLineData.data.purchase_order_line = [{ id: poList.purchase_order_line_id, po_line: poList.po_line }];
          }
        }
      }
    })
    if (cancelPoLineData) {
      saveCancelOrder(cancelPoLineData);
    }
    if (_closePoLineData) {
      saveCloseOrder(_closePoLineData);
    }
    
    confirmationPopupClose();
  }

  const formatFundingDate = (date: string) => {
    if(date){
      return new Date(date).toLocaleDateString("en-US", { month: "numeric", day: "numeric", year: "2-digit" }).replace(/\//g, '/');
    }
  }
  return (
    <div className="contentMain">
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
                setCurrentPage(1);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>
            <SearchBar
              value={inputSearchValue}
              placeholder={'Search'}
              onChange={(event)=>search(event.target.value)}
              onClear={() => setInputSearchValue('')}
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>Freight Term</th>
                  <th>Internal PO Number</th>
                  <th>PO Number</th>
                  <th>Price</th>
                  <th>Sales Tax</th>
                  <th>Funding Date</th>
                  <th colSpan={3}></th>
                </tr>
              </thead>
              <tbody>
              {isCancelOrderDataLoading ||
              isCancelOrderDataFetching ||
              isSaveCancelOrderDataLoading ||
              isSaveCloseOrderDataLoading ? <tr><td colSpan={9}><span className="loaderImg"><Loader /></span></td></tr> :
                filteredOrders?.length ? (
                  filteredOrders
                    .map((data: any, index: number) => (
                      <tr key={data.buyer_id + index}>
                        <td>{data.freight_term}</td>
                        <td>{data.buyer_internal_po}</td>
                        <td>{data.buyer_po_number}</td>
                        <td>{format2DecimalPlaces(data.buyer_total_price)}</td>
                        <td>{format2DecimalPlaces(data.sales_tax)}</td>
                        <td>{formatFundingDate(data.seller_funding_date)}</td>
                        <td>
                          {  data.show_cancel_btn && 
                            <button
                            className={styles.buyerCancelOrderBtn}
                            onClick={() =>
                              displayPoLinesHandler(data.buyer_po_number, data.purchase_order_lines, BUYER_ORDER_CANCEL)
                            }
                            >
                            Buyer cancel order
                          </button>
                          }
                        </td>
                        <td>
                          { data?.is_claimed && data.show_cancel_btn &&  (
                            <button
                              className={styles.buyerCancelOrderBtn}
                              onClick={() =>
                                displayPoLinesHandler(data.buyer_po_number, data.purchase_order_lines, SELLER_ORDER_CANCEL)
                              }
                            >
                              Seller cancel order
                            </button>
                          )}
                        </td>
                        <td>
                          {data?.is_claimed && (
                            <button
                              className={styles.buyerCancelOrderBtn}
                              onClick={
                                () => displayPoLinesHandler(data.buyer_po_number, data.purchase_order_lines, '')
                              }
                            >
                              Seller Close order
                            </button>
                          )}
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={9} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            {meta && <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={meta.totalPages}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
            forcePage={meta.currentPage > 0 ? meta.currentPage - 1 : undefined}
            />}
          </div>
          <MatPopup
            className={styles.approveRejectPopup}
            open={!!apiResponseMessage}
          >
            <div className={styles.successfullyUpdated}>
              <div className={styles.successfullytext}>
                {apiResponseMessage}
              </div>
              <button
                className={styles.okBtn}
                onClick={() => setApiResponseMessage("")}
              >
                Ok
              </button>
            </div>
          </MatPopup>
          <MatPopup
            className={styles.orderContinuePopup}
            open={showPoLinesPopup}
          >
            <div className={styles.tblscrollPop}>
              <div className={styles.continuePopup}>
                {cancelPoLineData ?
                <>
                {poLists.length > 1 ?
                  <p className={styles.continuetext}>Select the line and press yes to cancel</p>
                  :
                  <p className={styles.continuetext}>Press yes to cancel the order</p>
                }
                </> 
                :
                <>
                {poLists.length > 1 ?
                  <p className={styles.continuetext}>Select the line and press yes to close</p>
                  :
                  <p className={styles.continuetext}>Press yes to close the order</p>
                }
                </> 
                }
                <div className={styles.overFlowForPop}>
                <table>
                  <thead>

                    <tr>
                      <th>
                        {poLists.length > 1 &&
                          
                          <label className="containerChk">
                            <input type="checkbox" checked={isAllPoLineChecked} onChange={(e) => allPoLineChecked(e)} />
                            <span className="checkmark" />
                          </label>
                        }
                      </th>
                      <th>PO Line</th>
                      <th>Description</th>
                      <th>Status</th>
                    </tr>

                  </thead>
                  <tbody>
                    {poLists.map((poList: any, i: number) => {
                      const lines = poList.description.split('\n');
                      const firstLine = lines[0];
                      const restLines = lines.slice(1);
                      return (
                        <tr key={poList.po_line}>
                          <td>
                            {/* <input type="checkbox" checked={poList.isChecked} onChange={(e) => poLineChangeHandler(e, i)}
                            /> */}
                            {poLists.length > 1 &&
                            <label className="containerChk">
                            <input type="checkbox" checked={poList.isChecked} onChange={(e) => poLineChangeHandler(e, i)} />
                            <span className="checkmark" />
                          </label>
                    }
                          </td>
                          <td>{poList.po_line}</td>
                          <td className={styles.listDescription}>
                            <p className={styles.lineHead}>{firstLine}</p>
                            {restLines.map((line: any, index: number) => ( <p key={index}>{line}</p> ))}
                          </td>
                          <td>{poList.status}</td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
                </div>
                <div className={styles.yesAndnoBtn}>
                  <button className={styles.okBtn} onClick={submitSelectedPoLineClose} disabled= {disabledSubmitPoLineBtn}>
                    Yes
                  </button>
                  <button
                    className={styles.okBtn}
                    onClick={confirmationPopupClose}
                  >
                    No
                  </button>
                </div>
              </div>
            </div>

          </MatPopup>
        </div>
    </div>
  );
};

export default CancelOrder;

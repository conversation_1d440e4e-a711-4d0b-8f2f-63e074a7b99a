
import * as yup from "yup";
import { errorText } from "../utils/constant";

export const VideoEditFromSchema = yup.object().shape({
  title: yup.string().required(errorText.required).trim(),
  description: yup.string().nullable(),
  caption: yup.string().nullable(),
  video_s3_url: yup.string().nullable(),
  thumbnail_s3_url: yup.object().shape({
    thumbnail_app: yup.string(),
    thumbnail_safe: yup.string(),
    electron_player: yup.string(),
    intro_desktop: yup.string(),
    intro_mobile: yup.string(),
    intro_tablet: yup.string()
  }),
  subtitle_s3_url : yup.string().nullable(),
  videoTags: yup.array().of(
    yup.string().required(errorText.required)
  ).required(errorText.required).min(1, errorText.arrayMinimum),
  videoInternalTags: yup.array().of(
    yup.string().required(errorText.required)
  ),
  sequence: yup.string().required(errorText.required).test('maxDecimal',(value)=>{
    if (!value) return false; 
    const decimalPlaces = value.split('.')[1];
    return !decimalPlaces || decimalPlaces.length <= 2;
  }),
  videoFile: yup.mixed().default(undefined),
  show_on_ui: yup.boolean().required('Required'),
  share_video_url: yup.string().trim().nullable(),

  subtitleFile: yup.mixed().default(undefined),
  thumbnailFiles: yup.object().shape({
    thumbnail_app: yup.mixed().test('fileRequired1', errorText.fileIsRequired, (value, context) => value !== undefined || (context.parent?.thumbnail_s3_url?.thumbnail_app)),
    thumbnail_safe: yup.mixed().test('fileRequired2', errorText.fileIsRequired, (value, context) => value !== undefined || (context.parent?.thumbnail_s3_url?.thumbnail_safe)),
    electron_player: yup.mixed().test('fileRequired3', errorText.fileIsRequired, (value, context) => value !== undefined || (context.parent?.thumbnail_s3_url?.electron_player)),
    intro_desktop: yup.mixed().test('fileRequired4', errorText.fileIsRequired, (value, context) => value !== undefined || (context.parent?.thumbnail_s3_url?.intro_desktop)),
    intro_mobile: yup.mixed().test('fileRequired5', errorText.fileIsRequired, (value, context) => value !== undefined || (context.parent?.thumbnail_s3_url?.intro_mobile)),
    intro_tablet: yup.mixed().test('fileRequired6', errorText.fileIsRequired, (value, context) => value !== undefined || (context.parent?.thumbnail_s3_url?.intro_tablet))
  }),
  is_large_file : yup.boolean()
});

export type VideoEditFromSchemaType = yup.InferType<typeof VideoEditFromSchema>;


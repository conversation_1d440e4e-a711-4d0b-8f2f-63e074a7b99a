import { useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostSendMessage = () => {

  return useMutation(async (payload: any) => {
    try {
      const url = `${import.meta.env.VITE_API_CHAT_SERVICE}/admin/send-message`;
      const response = await axios.post(
        url,
        {
           data: payload
        }
      );

      if (response.data?.data) {
        return response.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostSendMessage;

// API Types for Admin Video Tooltip functionality

export interface Coords {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface VideoMapping {
  id: string;
  video_url: string;
  is_enabled: boolean;
  priority?: number;
  title?: string;
  description?: string;
  caption_url?: string;
  is_caption_enabled?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Hotspot {
  id: string;
  element_id: string;
  shape_type: 'rect' | 'circle' | 'polygon';
  coords_json: Coords;
  is_enabled: boolean;
  has_video_mapping: boolean;
  video_mapping?: VideoMapping | null;
}

export interface Screen {
  id: string;
  name: string;
  image_url: string;
  natural_width: number;
  natural_height: number;
  is_active: 0 | 1;
  version: number;
  hotspots_count: number;
  mapped_videos_count: number;
  pending_mappings_count: number;
  created_at: string;
  updated_at: string;
  hotspots: Hotspot[];
}

export interface ScreensResponse {
  data: Screen[];
  meta: {
    total_screens: number;
    active_screens: number;
    total_hotspots: number;
    total_mappings: number;
  };
}

export interface ScreenResponse {
  data: Screen;
}

export interface HotspotsSaveRequest {
  hotspots: Hotspot[];
}

export interface HotspotsSaveResponse {
  data: Screen;
  message: string;
}

export interface MappingCreateRequest {
  element_id: string;
  screen_id: string;
  hotspot_id: string;
  video_url: string;
  is_enabled?: 0 | 1;
  title?: string;
  description?: string;
  caption_url?: string;
  is_caption_enabled?: 0 | 1;
}

export interface MappingUpdateRequest {
  video_url?: string;
  is_enabled?: 0 | 1;
  priority?: number;
  title?: string;
  description?: string;
  caption_url?: string;
  is_caption_enabled?: 0 | 1;
}

export interface MappingResponse {
  data: VideoMapping;
}

export interface MappingResolveParams extends Record<string, string> {
  screen_id: string;
  element_id: string;
}

export interface SuccessResponse {
  success: boolean;
}

export interface ErrorResponse {
  message: string;
}

// Upload data types
export interface ScreenUploadData {
  name: string;
  is_active: 0 | 1;
  image?: File;
  image_url?: string;
}

// API Request options
export interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
  body?: string | FormData;
}

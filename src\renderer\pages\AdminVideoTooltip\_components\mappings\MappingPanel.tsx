import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Alert,
  Chip,
  Divider,
  CircularProgress,
  LinearProgress,
  TextField,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Save as SaveIcon,
  VideoLibrary as VideoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  CloudUpload as CloudUploadIcon,
} from '@mui/icons-material';
import { uploadFileAndGetS3Url } from '@bryzos/giss-ui-library';
import VideoPreview from './VideoPreview';

interface MappingPanelProps {
  selectedHotspot: any;
  onMappingUpdate: (hotspotId: string, data: { video_url?: string; is_enabled?: boolean; title?: string; description?: string; caption_url?: string; is_caption_enabled?: boolean }) => Promise<void>;
  disabled?: boolean;
}

const MappingPanel = ({ 
  selectedHotspot, 
  onMappingUpdate,
  disabled = false 
}: MappingPanelProps) => {
  const [videoUrl, setVideoUrl] = useState('');
  const [captionUrl, setCaptionUrl] = useState('');
  const [captionEnabled, setCaptionEnabled] = useState(true);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [error, setError] = useState('');
  const [saving, setSaving] = useState(false);
  const [savingTitleDesc, setSavingTitleDesc] = useState(false);
  const [savingVideo, setSavingVideo] = useState(false);
  const [savingCaption, setSavingCaption] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  
  // File upload states
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedCaptionFile, setSelectedCaptionFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadingCaption, setUploadingCaption] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [captionDragOver, setCaptionDragOver] = useState(false);
  
  // Ref for file input to reset it programmatically
  const fileInputRef = useRef<HTMLInputElement>(null);
  const captionFileInputRef = useRef<HTMLInputElement>(null);

  // Reset all form states when hotspot changes or when video_mapping updates
  useEffect(() => {
    if (selectedHotspot) {
      const currentUrl = selectedHotspot.video_mapping?.video_url || '';
      const currentCaptionUrl = selectedHotspot.video_mapping?.caption_url || '';
      const currentCaptionEnabled = selectedHotspot.video_mapping?.is_caption_enabled !== undefined 
        ? selectedHotspot.video_mapping?.is_caption_enabled 
        : true;
      const currentTitle = selectedHotspot.video_mapping?.title || '';
      const currentDescription = selectedHotspot.video_mapping?.description || '';
      setVideoUrl(currentUrl);
      setCaptionUrl(currentCaptionUrl);
      setCaptionEnabled(currentCaptionEnabled);
      setTitle(currentTitle);
      setDescription(currentDescription);
      setError('');
      // Don't reset selectedFile when updating existing mapping - only reset when switching hotspots
      if (!selectedHotspot.has_video_mapping) {
        setSelectedFile(null);
        setSelectedCaptionFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        if (captionFileInputRef.current) {
          captionFileInputRef.current.value = '';
        }
      } else {
        setSelectedCaptionFile(null);
        if (captionFileInputRef.current) {
          captionFileInputRef.current.value = '';
        }
      }
      setUploading(false);
      setDragOver(false);
    } else {
      // Reset all states when no hotspot is selected
      setVideoUrl('');
      setCaptionUrl('');
      setCaptionEnabled(true);
      setTitle('');
      setDescription('');
      setError('');
      setSelectedFile(null);
      setSelectedCaptionFile(null);
      setUploading(false);
      setUploadingCaption(false);
      setDragOver(false);
      setCaptionDragOver(false);
      
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      if (captionFileInputRef.current) {
        captionFileInputRef.current.value = '';
      }
    }
  }, [selectedHotspot?.id, selectedHotspot?.video_mapping?.id, selectedHotspot?.video_mapping?.video_url, selectedHotspot?.video_mapping?.title, selectedHotspot?.video_mapping?.description, selectedHotspot?.video_mapping?.caption_url, selectedHotspot?.video_mapping?.is_caption_enabled]);

  // Track changes separately for each section
  const hasTitleDescChanges = () => {
    if (!selectedHotspot?.has_video_mapping) return false;
    const currentTitle = selectedHotspot.video_mapping?.title || '';
    const currentDescription = selectedHotspot.video_mapping?.description || '';
    return title !== currentTitle || description !== currentDescription;
  };

  const hasVideoChanges = () => {
    if (!selectedHotspot?.has_video_mapping) return false;
    return selectedFile !== null;
  };

  const hasCaptionChanges = () => {
    if (!selectedHotspot?.has_video_mapping) return false;
    const currentCaptionUrl = selectedHotspot.video_mapping?.caption_url || '';
    const currentCaptionEnabled = selectedHotspot.video_mapping?.is_caption_enabled !== undefined 
      ? selectedHotspot.video_mapping?.is_caption_enabled 
      : true;
    return selectedCaptionFile !== null || 
           captionUrl !== currentCaptionUrl || 
           captionEnabled !== currentCaptionEnabled;
  };

  // File handling functions
  const handleFileSelect = (file: File | null) => {
    // Clear any previous errors when selecting a new file
    setError('');
    
    if (file) {
      // Validate file type
      const validTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];
      if (!validTypes.includes(file.type)) {
        setError('Please select a valid video file (MP4, AVI, MOV, WMV, or WebM).');
        setSelectedFile(null);
        return;
      }

      // Validate file size (100MB max)
      if (file.size > 100 * 1024 * 1024) {
        setError('File size must be less than 100MB.');
        setSelectedFile(null);
        return;
      }

      setSelectedFile(file);
    } else {
      setSelectedFile(null);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const uploadVideoFile = async (file: File): Promise<string> => {
    try {
      setUploading(true);
      const videoUrl = await uploadFileAndGetS3Url(
        file, 
        import.meta.env.VITE_S3_UPLOAD_ADMIN_TOOLTIP_SCREENS_BUCKET, 
        '/staging-video-tooltips/video-tool-tips/', 
        import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', 
        'video', 
        import.meta.env.VITE_ENVIRONMENT
      );
      return videoUrl;
    } catch (error) {
      console.error('Video upload failed:', error);
      throw new Error('Failed to upload video file. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleCaptionFileSelect = (file: File | null) => {
    setError('');
    
    if (file) {
      if (!file.name.endsWith('.vtt')) {
        setError('Please select a valid VTT caption file.');
        setSelectedCaptionFile(null);
        return;
      }

      if (file.size > 1 * 1024 * 1024) {
        setError('Caption file size must be less than 1MB.');
        setSelectedCaptionFile(null);
        return;
      }

      setSelectedCaptionFile(file);
    } else {
      setSelectedCaptionFile(null);
    }
  };

  const handleCaptionDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setCaptionDragOver(true);
  };

  const handleCaptionDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setCaptionDragOver(false);
  };

  const handleCaptionDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setCaptionDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleCaptionFileSelect(files[0]);
    }
  };

  const uploadCaptionFile = async (file: File): Promise<string> => {
    try {
      setUploadingCaption(true);
      const captionUrl = await uploadFileAndGetS3Url(
        file, 
        import.meta.env.VITE_S3_UPLOAD_ADMIN_TOOLTIP_SCREENS_BUCKET, 
        '/staging-video-tooltips/closed-captions/', 
        import.meta.env.VITE_API_SERVICE + '/user/get_signed_url',
        'cc', 
        import.meta.env.VITE_ENVIRONMENT
      );
      return captionUrl;
    } catch (error) {
      console.error('Caption upload failed:', error);
      throw new Error('Failed to upload caption file. Please try again.');
    } finally {
      setUploadingCaption(false);
    }
  };

  const handleUpdateTitleDescription = async () => {
    if (!selectedHotspot.has_video_mapping) {
      setError('No existing mapping to update.');
      return;
    }
    
    try {
      setSavingTitleDesc(true);
      setError('');
      
      await onMappingUpdate(selectedHotspot.id, {
        title: title.trim() || undefined,
        description: description.trim() || undefined,
      });
      setError('');
    } catch (err: any) {
      setError(err.message || 'Failed to update title and description');
    } finally {
      setSavingTitleDesc(false);
    }
  };

  const handleUpdateVideo = async () => {
    if (!selectedHotspot.has_video_mapping) {
      setError('No existing mapping to update.');
      return;
    }
    
    if (!selectedFile) {
      setError('Please select a video file to upload.');
      return;
    }
    
    try {
      setSavingVideo(true);
      setError('');
      
      const finalVideoUrl = await uploadVideoFile(selectedFile);
      setVideoUrl(finalVideoUrl);
      
      await onMappingUpdate(selectedHotspot.id, {
        video_url: finalVideoUrl.trim(),
      });
      
      // Reset form after successful save
      setSelectedFile(null);
      setError('');
      
      // Reset the file input element
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update video');
    } finally {
      setSavingVideo(false);
    }
  };

  const handleUpdateCaption = async () => {
    if (!selectedHotspot.has_video_mapping) {
      setError('No existing mapping to update.');
      return;
    }
    
    try {
      setSavingCaption(true);
      setError('');

      let finalCaptionUrl = captionUrl;
      if (selectedCaptionFile) {
        finalCaptionUrl = await uploadCaptionFile(selectedCaptionFile);
        setCaptionUrl(finalCaptionUrl);
      }
      
      await onMappingUpdate(selectedHotspot.id, {
        caption_url: finalCaptionUrl.trim() || undefined,
        is_caption_enabled: captionEnabled,
      });
      
      // Clear the selected caption file to remove the update button
      setSelectedCaptionFile(null);
      setError('');
      if (captionFileInputRef.current) {
        captionFileInputRef.current.value = '';
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update caption');
    } finally {
      setSavingCaption(false);
    }
  };

  const handleSave = async () => {
    if (!selectedFile) {
      setError('Please select a video file to upload.');
      return;
    }
    
    try {
      setSaving(true);
      setError('');
      
      const finalVideoUrl = await uploadVideoFile(selectedFile);
      setVideoUrl(finalVideoUrl);

      let finalCaptionUrl = captionUrl;
      if (selectedCaptionFile) {
        finalCaptionUrl = await uploadCaptionFile(selectedCaptionFile);
        setCaptionUrl(finalCaptionUrl);
      }
      
      await onMappingUpdate(selectedHotspot.id, {
        video_url: finalVideoUrl.trim(),
        is_enabled: true,
        title: title.trim() || undefined,
        description: description.trim() || undefined,
        caption_url: finalCaptionUrl.trim() || undefined,
        is_caption_enabled: captionEnabled,
      });
      
      // Reset form after successful save
      setSelectedFile(null);
      setSelectedCaptionFile(null);
      setError('');
      
      // Reset the file input element
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      if (captionFileInputRef.current) {
        captionFileInputRef.current.value = '';
      }
    } catch (err: any) {
      setError(err.message || 'Failed to upload video file');
    } finally {
      setSaving(false);
    }
  };

  const getHotspotStatus = () => {
    if (!selectedHotspot.element_id) {
      return { color: 'warning' as const, icon: <WarningIcon />, text: 'Needs Element ID' };
    }
    if (selectedHotspot.has_video_mapping) {
      return { color: 'success' as const, icon: <CheckCircleIcon />, text: 'Video Mapped' };
    }
    return { color: 'info' as const, icon: <VideoIcon />, text: 'Ready for Video' };
  };

  // if (!selectedHotspot) {
  //   return (
  //     <Paper
  //       sx={{
  //         p: 3,
  //         height: 'fit-content',
  //         minHeight: 300,
  //         display: 'flex',
  //         flexDirection: 'column',
  //         alignItems: 'center',
  //         justifyContent: 'center',
  //         backgroundColor: '#f9f9f9',
  //         textAlign: 'center',
  //       }}
  //     >
  //       <VideoIcon sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
  //       <Typography variant="h6" sx={{ color: '#666', mb: 1 }}>
  //         Select a hotspot
  //       </Typography>
  //       <Typography variant="body2" sx={{ color: '#999' }}>
  //         Click on a hotspot in the screen preview to assign a video
  //       </Typography>
  //     </Paper>
  //   );
  // }

  if (!selectedHotspot) {
    return null;
  }


  const status = getHotspotStatus();

  return (
    <Paper sx={{ p: 3, height: "fit-content" }}>
      <Typography variant="h6" sx={{ mb: 2, color: "#0d1b2a" }}>
        Video Mapping
      </Typography>

      {/* Hotspot Info */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
            {selectedHotspot.element_id}
          </Typography>
          <Chip
            icon={status.icon}
            label={status.text}
            color={status.color}
            size="small"
          />
        </Box>
        <Typography variant="caption" sx={{ color: "#666" }}>
          Position: {(selectedHotspot.coords_json.x * 100).toFixed(1)}%,{" "}
          {(selectedHotspot.coords_json.y * 100).toFixed(1)}%
        </Typography>
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* Title and Description Fields */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          label="Title (Optional)"
          value={title}
          size="small"
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter video title"
          sx={{ mb: 3 }}
          disabled={disabled || saving || savingTitleDesc || savingVideo || savingCaption || uploading || uploadingCaption}
        />

        <TextField
          fullWidth
          label="Description (Optional)"
          value={description}
          size="small"
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Enter video description"
          multiline
          rows={3}
          disabled={disabled || saving || savingTitleDesc || savingVideo || savingCaption || uploading || uploadingCaption}
        />

        {/* Update button for title and description */}
        {selectedHotspot.has_video_mapping && hasTitleDescChanges() && (
          <Button
            variant="contained"
            fullWidth
            startIcon={
              savingTitleDesc ? (
                <CircularProgress size={16} />
              ) : (
                <SaveIcon />
              )
            }
            onClick={handleUpdateTitleDescription}
            disabled={disabled || saving || savingTitleDesc || savingVideo || savingCaption || uploading || uploadingCaption}
            sx={{
              mt: 1,
            }}
          >
            {savingTitleDesc ? "Updating..." : "Update Title & Description"}
          </Button>
        )}
      </Box>

      {/* Video File Upload */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, color: "#0d1b2a" }}>
          Upload Video File
        </Typography>

        <Box
          sx={{
            p: 3,
            border: dragOver ? "2px dashed #415a77" : "2px dashed #ccc",
            backgroundColor: dragOver ? "rgba(65, 90, 119, 0.05)" : "#fafafa",
            textAlign: "center",
            cursor: "pointer",
            transition: "all 0.2s ease",
            mb: 2,
          }}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="video/mp4,video/avi,video/mov,video/wmv,video/webm"
            style={{ display: "none" }}
            onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}
            disabled={disabled || saving || savingTitleDesc || savingVideo || savingCaption || uploading || uploadingCaption}
          />
          <CloudUploadIcon sx={{ fontSize: 48, color: "#778da9", mb: 1 }} />
          <Typography variant="h6" sx={{ mb: 1 }}>
            {selectedFile
              ? selectedFile.name
              : "Drop video here or click to browse"}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            MP4, AVI, MOV, WMV, or WebM files, max 100MB
          </Typography>
        </Box>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Upload Progress */}
        {uploading && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Uploading video file...
            </Typography>
            <LinearProgress />
          </Box>
        )}

        {/* Update button for video */}
        {selectedHotspot.has_video_mapping && hasVideoChanges() && (
          <Button
            variant="contained"
            fullWidth
            startIcon={
              savingVideo || uploading ? (
                <CircularProgress size={16} />
              ) : (
                <SaveIcon />
              )
            }
            onClick={handleUpdateVideo}
            disabled={disabled || saving || savingTitleDesc || savingVideo || savingCaption || uploading || uploadingCaption}
            sx={{
              mt: 1,
            }}
          >
            {savingVideo || uploading ? "Updating Video..." : "Update Video"}
          </Button>
        )}
      </Box>

      {/* VTT Caption File Upload */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1.5 }}>
          <Typography variant="subtitle2" sx={{ color: "#666" }}>
            Caption File (Optional)
          </Typography>
          {captionUrl && !selectedCaptionFile && (
            <Chip
              label="Caption available"
              color="success"
              size="small"
              variant="outlined"
            />
          )}
        </Box>

        {captionUrl && !selectedCaptionFile && selectedHotspot.has_video_mapping && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ p: 1.5, backgroundColor: '#f0f7f0', borderRadius: 1, border: '1px solid #c8e6c9', mb: 1.5 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                <CheckCircleIcon sx={{ fontSize: 18, color: '#4caf50' }} />
                <Typography variant="body2" sx={{ color: '#2e7d32', flex: 1 }}>
                  Caption file is currently uploaded
                </Typography>
                <Button
                  size="small"
                  variant="text"
                  onClick={() => captionFileInputRef.current?.click()}
                  sx={{ textTransform: 'none', color: '#2e7d32' }}
                >
                  Replace
                </Button>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={captionEnabled}
                      onChange={(e) => setCaptionEnabled(e.target.checked)}
                      size="small"
                      disabled={disabled || saving || savingTitleDesc || savingVideo || savingCaption || uploadingCaption}
                    />
                  }
                  label={
                    <Typography variant="body2" sx={{ color: '#2e7d32' }}>
                      {captionEnabled ? 'Caption enabled' : 'Caption disabled'}
                    </Typography>
                  }
                />
              </Box>
            </Box>
          </Box>
        )}
        
        <Box
          sx={{
            p: 1.5,
            border: captionDragOver ? '1px dashed #415a77' : '1px dashed #ddd',
            backgroundColor: captionDragOver ? 'rgba(65, 90, 119, 0.03)' : '#fafafa',
            textAlign: 'center',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            mb: 2,
            borderRadius: 1,
            ...(captionUrl && !selectedCaptionFile && { display: 'none' }),
          }}
          onDragOver={handleCaptionDragOver}
          onDragLeave={handleCaptionDragLeave}
          onDrop={handleCaptionDrop}
          onClick={() => captionFileInputRef.current?.click()}
        >
          <input
            ref={captionFileInputRef}
            type="file"
            accept=".vtt"
            style={{ display: 'none' }}
            onChange={(e) => handleCaptionFileSelect(e.target.files?.[0] || null)}
            disabled={disabled || saving || savingTitleDesc || savingVideo || savingCaption || uploadingCaption}
          />
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
            <CloudUploadIcon sx={{ fontSize: 20, color: '#999' }} />
            <Typography variant="body2" sx={{ color: '#666' }}>
              {selectedCaptionFile ? selectedCaptionFile.name : 'Drop VTT file or click to browse'}
            </Typography>
          </Box>
        </Box>
    
        {selectedCaptionFile && selectedHotspot.has_video_mapping && (
          <Box sx={{ mb: 2, p: 1.5, backgroundColor: '#fff3e0', borderRadius: 1, border: '1px solid #ffe0b2' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CloudUploadIcon sx={{ fontSize: 18, color: '#f57c00' }} />
              <Typography variant="body2" sx={{ color: '#e65100', flex: 1 }}>
                New caption selected: {selectedCaptionFile.name}
              </Typography>
              <Button
                size="small"
                variant="text"
                onClick={() => {
                  setSelectedCaptionFile(null);
                  if (captionFileInputRef.current) {
                    captionFileInputRef.current.value = '';
                  }
                }}
                sx={{ textTransform: 'none', color: '#e65100' }}
              >
                Remove
              </Button>
            </Box>
          </Box>
        )}

        {/* Caption Upload Progress */}
        {uploadingCaption && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Uploading caption file...
            </Typography>
            <LinearProgress />
          </Box>
        )}
        {selectedCaptionFile && !selectedHotspot.has_video_mapping && (
          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              sx={{
                '& .MuiFormControlLabel-label': { fontSize: '0.875rem' }
              }}
              control={
                <Switch
                  checked={captionEnabled}
                  onChange={(e) => setCaptionEnabled(e.target.checked)}
                  size="small"
                />
              }
              label="Caption enabled by default"
            />
          </Box>
        )}

        {/* Update button for caption */}
        {selectedHotspot.has_video_mapping && hasCaptionChanges() && (
          <Button
            variant="contained"
            fullWidth
            startIcon={
              savingCaption || uploadingCaption ? (
                <CircularProgress size={16} />
              ) : (
                <SaveIcon />
              )
            }
            onClick={handleUpdateCaption}
            disabled={disabled || saving || savingTitleDesc || savingVideo || savingCaption || uploading || uploadingCaption}
            sx={{
              mt: 1,
            }}
          >
            {savingCaption || uploadingCaption ? "Updating Caption..." : "Update Caption"}
          </Button>
        )}
      </Box>

      {/* Video Preview */}
      {videoUrl && videoUrl.trim() && !error && (
        <>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, color: "#666" }}>
              Preview
            </Typography>
            <VideoPreview 
              videoUrl={videoUrl} 
              captionUrl={captionUrl}
              isCaptionEnabled={captionEnabled}
            />
          </Box>
        </>
      )}

       {/* Submit Button */}
       {selectedFile && !selectedHotspot.has_video_mapping && (
        <>
          <Divider sx={{ my: 2 }} />
          <Button
            fullWidth
            variant="contained"
            startIcon={saving || savingTitleDesc || savingVideo || savingCaption || uploading || uploadingCaption ? <CircularProgress size={16} /> : <SaveIcon />}
            onClick={handleSave}
            disabled={disabled || saving || savingTitleDesc || savingVideo || savingCaption || uploading || uploadingCaption}
            sx={{
              backgroundColor: '#415a77',
              '&:hover': { backgroundColor: '#1b263b' },
              py: 1.5,
            }}
          >
            {saving || savingTitleDesc || savingVideo || savingCaption || uploading || uploadingCaption ? 'Saving Mapping...' : 'Save Video Mapping'}
          </Button>
        </>
      )}

      {/* Debug info */}
      {process.env.NODE_ENV === "development" && (
        <Box sx={{ mt: 2, p: 1, bgcolor: "#f5f5f5", fontSize: "0.75rem" }}>
          <div>videoUrl: {videoUrl || "null"}</div>
          <div>captionUrl: {captionUrl || "null"}</div>
          <div>selectedFile: {selectedFile?.name || "null"}</div>
          <div>selectedCaptionFile: {selectedCaptionFile?.name || "null"}</div>
          <div>error: {error || "null"}</div>
          <div>saving: {saving.toString()}</div>
          <div>uploading: {uploading.toString()}</div>
          <div>hasChanges: {hasChanges.toString()}</div>
        </Box>
      )}

      {/* Current Mapping Info */}
      {selectedHotspot.has_video_mapping && selectedHotspot.video_mapping && (
        <>
          <Divider sx={{ my: 2 }} />
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1, color: "#666" }}>
              Current Mapping
            </Typography>
            <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
              <Chip
                label={
                  selectedHotspot.video_mapping.is_enabled
                    ? "Enabled"
                    : "Disabled"
                }
                color={
                  selectedHotspot.video_mapping.is_enabled
                    ? "success"
                    : "default"
                }
                size="small"
              />
              {/* <Chip
                label={`Priority: ${selectedHotspot.video_mapping.priority}`}
                variant="outlined"
                size="small"
              /> */}
            </Box>
          </Box>
        </>
      )}

      {/* Help Text */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: "#f9f9f9", borderRadius: 1 }}>
        <Typography variant="caption" sx={{ color: "#666" }}>
          <strong>Supported formats:</strong> MP4, AVI, MOV, WMV, WebM files
          <br />
          <strong>File size limit:</strong> Maximum 100MB per video
          <br />
          <strong>Note:</strong> Videos will be uploaded to S3 and the URL will
          be automatically generated
        </Typography>
      </Box>
    </Paper>
  );
};

export default MappingPanel;

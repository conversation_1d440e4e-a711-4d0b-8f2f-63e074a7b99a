.propertiesPanel {
  width: 280px;
  background: white;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.closeBtn {
  padding: 4px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #666;
}

.panelContent {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.propertyGroup {
  margin-bottom: 20px;

  label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #666;
  }

  input,
  select,
  textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }

  textarea {
    resize: vertical;
  }
}

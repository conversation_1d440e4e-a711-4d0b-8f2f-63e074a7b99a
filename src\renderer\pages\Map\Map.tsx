import React, { useRef, useEffect, useState, useContext } from "react";
import {
  Annotation,
  ComposableMap,
  Geographies,
  Geography,
  Marker,
} from 'react-simple-maps';
import usa from './map.json';
import states from './states.json';
import { Box, MenuItem, Select } from "@mui/material";
import styles from "./Map.module.scss";
import { ReactComponent as CloseIcon } from '../../../assests/images/close-icon.svg';
import { ReactComponent as ClosePopupIcon } from '../../../assests/images/Icon_Close.svg';
import { ReactComponent as DownloadIcon } from '../../../assests/images/icondownload.svg';
import { geoCentroid } from "d3-geo";
import { v4 as uuidv4 } from 'uuid';
import { adminSuffixUrlList, commomKeys, folderPathList, prefixUrl, uploadFileAndGetS3Url } from "@bryzos/giss-ui-library";
import axios from "axios";
import { useImmer } from "use-immer";
import { filterArrray } from "../../utils/helper";
import clsx from "clsx";
import Loader from "../../components/common/Loader";
import MatPopup from "../../components/common/MatPopup";
import Tooltip from "@mui/material/Tooltip/Tooltip";
import { certStatusObj, colorCode, loaderActionText } from "../../utils/constant";
import { CommonCtx } from "../AppContainer";
import SearchBar from "../../components/common/SearchBox/SearchBox";

const Map = ({filteredaPendingCompanies,  companyData, confirmationPopupClose, uploadCompanyCertificate, referenceData, deleteCompanyResaleCert, closeResaleCertPopup, isDeleteCompanyResaleCertLoading, isCompanyResaleCertUploadLoading, companyResaleCertificateLoading, companyResaleCertificateFetching, allCompanyLoading, allCompanyFetching, setDeleteCertData, deleteCertData, setCertState, certState}: any) => {
  const [openAddPopup, setOpenAddPopup] = useState(false);
  const [certFileName, setCertFileName] = useState('');
  const [certExpiration, setCertExpiration] = useState('');
  const [inputSearchValue, setInputSearchValue] = useState<string>("");
  const [certStateList, setCertStateList] = useImmer<any>([]);
  const [filteredCertStateList, setFilteredCertStateList] = useImmer<any>([]);
  const [stateList, setStateList] = useImmer<any>([]);
  const [showLoader, setShowLoader] = useState<boolean>(false);
  const [showConfirmationPopup, setShowConfirmationPopup] = useState<boolean>(false);
  const [loaderText, setLoaderText] = useState<string>(loaderActionText.loading);
  const [certFileData, setCertFileData] = useState<File | null>(null);
  const showPopupFormAnyComponent = useContext(CommonCtx);

  useEffect(() => {
    if (filteredaPendingCompanies?.length && companyData) {
      const company = filteredaPendingCompanies.find((company: any) => company.id === companyData.id);
      getCertStateListData(company);
    }
  }, [filteredaPendingCompanies]);

  useEffect(() => {
    getCertStateListData(companyData);
  }, [companyData])

  useEffect(() => {
    if (referenceData?.ref_states && certStateList) {
      const stateData = [...referenceData.ref_states];
      stateData.forEach((data, i) => {
        if (certStateList.find(certData => certData.state_id === data.id)) {
          stateData[i].isAvailable = true;
        }
        else {
          stateData[i].isAvailable = false;
        }
      });
      setStateList(stateData);

    }

  }, [referenceData?.ref_states, certStateList])

  useEffect(()=>{
    if(isCompanyResaleCertUploadLoading){
      setLoaderText(loaderActionText.submit)
    }
  },[isCompanyResaleCertUploadLoading])

  useEffect(()=>{
    if(isDeleteCompanyResaleCertLoading){
      setLoaderText(loaderActionText.delete)
    }
  },[isDeleteCompanyResaleCertLoading])

  const getCertStateListData = (company) => {
    if(company?.length > 0 && inputSearchValue.length !== 0){
      search(inputSearchValue)
    } else if (company?.certificateData) {
      setCertStateList([...company.certificateData]);
      setFilteredCertStateList([...company.certificateData]);
    } else{
      setCertStateList([]);
      setFilteredCertStateList([]);
    }
  }

  const handleAddSubmitBtn = (certificateUrl: string) => {
    const resaleCertData: { state_id: any; expiration_date: string; file_name: string; cerificate_url_s3: string; }[] = [];
    certState.forEach(stateData => {
      if (stateData && certificateUrl) {
        const obj = {
          "state_id": stateData.id,
          "expiration_date": certExpiration,
          "file_name": certFileName,
          "cerificate_url_s3": certificateUrl
        }
        resaleCertData.push(obj);
      }
    })
    const payload = {
      "data": {
        "company_id": String(companyData.id),
        "resale_certificate": resaleCertData
      }
    }


    setOpenAddPopup(false);
    uploadCompanyCertificate(payload);
    setCertFileName('')
    setCertFileData(null)
    setCertExpiration('')
    setInputSearchValue('')
  }
  const handleUploadFile = async () => {
    try {
      if (certFileData) {
        setShowLoader(true);
        setLoaderText(loaderActionText.submit)
        const certUrlS3 = await uploadFileAndGetS3Url(certFileData, import.meta.env.VITE_S3_UPLOAD_COMPANY_RESALE_CERT_BUCKET_NAME, folderPathList.companyResaleCert + companyData.id + '/', import.meta.env.VITE_API_ADMIN_SERVICE_NODE + adminSuffixUrlList.getSignedUrl, prefixUrl.resaleCertPrefix, import.meta.env.VITE_ENVIRONMENT);
        if(certUrlS3){
          handleAddSubmitBtn(certUrlS3);
        }else{
          showPopupFormAnyComponent(commomKeys.errorContent);
        }
        setShowLoader(false);
      }
    } catch (error) {
      console.log(error)
      setCertFileName('');
      setCertFileData(null);
      setShowLoader(false);
      setLoaderText(loaderActionText.loading)
    }
  }

  const handleStateOnClick = (i: number, data: any) => {
    setCertState((prev) => {
      if (prev[i]) {
        prev[i] = null;
      } else {
        prev[i] = { ...data }
      }
      return prev;
    })
  }

  const handleSubmitDisabled = () => {
    const disableCondition = !!(certExpiration && certFileName && certFileData && certState.filter(Boolean).length !== 0);

    return !disableCondition;
  }

  const search = (searchValue: string) => {
    setInputSearchValue(searchValue);
    if (searchValue) {
      const _filterArrray = filterArrray(certStateList, searchValue.trim(), [
        "stateName",
        "stateCode",
      ]);
      if (_filterArrray?.length) {
        setFilteredCertStateList(_filterArrray);
      } else {
        setFilteredCertStateList([]);
      }
    } else {
      setFilteredCertStateList(certStateList ? certStateList : []);
    }
  };

  const handleDeleteCompanyCert = (certData) => {
    setShowConfirmationPopup(true)
    setDeleteCertData(certData);
  }

  const confirmationPopupYes = () => {
    const payload = {
      "data": [
        {
          "cert_id": deleteCertData.id
        }
      ]
    }
    setShowConfirmationPopup(false)
    confirmationPopupClose();
    deleteCompanyResaleCert(payload);
  }

  const clearAddFieldsData = () => {
    setCertFileName('')
    setCertFileData(null);
    setCertState([])
    setCertExpiration('')

  }

  const handleOnchangeFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length !== 0) {
      const file = e.target.files[0];
      setCertFileName(file.name);
      setCertFileData(file);
    } else {
      setCertFileName('');
      setCertFileData(null);
    }
  }

  return (
    <>
      <Box className={styles.mainContent} bgcolor={'#fff'} padding={'20px'}>
        {showLoader ||
          isCompanyResaleCertUploadLoading ||
          companyResaleCertificateLoading ||
          companyResaleCertificateFetching ||
          isDeleteCompanyResaleCertLoading ||
          allCompanyLoading ||
          allCompanyFetching ?
          (<div className="loaderImg resaleCertLoader">
            <span className="loaderCert"><Loader /></span> <span className="loadingTxt">{loaderText}...</span>
          </div>)
          :
          !openAddPopup ?
            (
              <>
                <button className={styles.closePopupBtn} onClick={closeResaleCertPopup}><ClosePopupIcon /></button>
                <p className={styles.addCompanyTitle}>{companyData ? companyData.company_name : ''}</p>
                <div className={styles.addCompanyTitle1}>Resale Certs</div>
                <div className={styles.topSeaction}>
                  <button className={styles.addCompanyBtn} onClick={() => setOpenAddPopup(true)} >ADD</button>
                  <SearchBar
                    value={inputSearchValue}
                    placeholder={"Search State"}
                    onChange={(event)=> search(event.target.value)}
                    onClear={() => setInputSearchValue('')}
                  />
                </div>
                <div className={styles.certStateList}>
                  {filteredCertStateList.length !== 0 ? filteredCertStateList.map((data: any, i: number) => (
                    <div className={styles.certTile} key={i}>
                      <div className={clsx(styles.certBox, data.status === certStatusObj.expire && styles.certExpire)}>
                        <div className={styles.certHeader}>
                          <span><a href={data.cerificate_url_s3}><DownloadIcon/></a></span> <span onClick={() => { handleDeleteCompanyCert(data) }}><CloseIcon /></span>
                        </div>
                        <div className={styles.contentMain}>
                        <span>{data.stateName} ({data.stateCode})</span>
                        </div>
                      </div>

                    </div>
                  ))
                    :
                    'No Data Found'
                  }
                </div>

                <div className={styles.mapContent}>
                  <ComposableMap
                    projection='geoAlbersUsa'
                    projectionConfig={{
                      scale: 1000,
                    }}
                    fill='white'
                    stroke='black'
                    strokeWidth={1}
                  >
                    <Geographies geography={usa} >
                      {(geographies) => {
                        geographies.geographies.forEach((data, i) => {
                          const findCertStateList = certStateList.find(certData => certData.stateCode === data.id);
                          if (findCertStateList) {
                            if(findCertStateList.status === certStatusObj.expire){
                              geographies.geographies[i].properties.highlighted = colorCode.red;
                            }else{
                              geographies.geographies[i].properties.highlighted = colorCode.green;
                            }
                            
                          }
                          else {
                            geographies.geographies[i].properties.highlighted = colorCode.grey;
                          }
                        })
                        return (
                          <>
                            {geographies.geographies.map((geo) => {

                              const firstRowCoordinates = geo.geometry.coordinates[0];

                              // Calculate the centroid of the first row coordinates
                              const centroid = geoCentroid({ type: "Polygon", coordinates: [firstRowCoordinates] });
                              const smallStateList = ['NH', 'RI', 'NJ', 'MD', 'VT', 'MA', 'CT', 'DE', 'DC']
                              return (
                                <>
                                  <Geography
                                    key={geo.rsmKey}
                                    geography={geo}
                                    fill={geo.properties.highlighted}
                                    color="#fff"
                                    style={{
                                      default: { outline: "none" },
                                      hover: { outline: "none" },
                                      pressed: { outline: "none" },
                                    }}
                                  />
                                  <Marker coordinates={centroid} >
                                    {!smallStateList.find((smallState) => geo.id.startsWith(smallState)) &&
                                      <text fontSize="10" fill="white" color="white" textAnchor="middle">
                                        {geo.id}
                                      </text>
                                    }
                                  </Marker>
                                  {geo.id === 'NH' &&
                                    <Annotation
                                      subject={centroid}
                                      dx={15}
                                      dy={95}
                                      connectorProps={{
                                        stroke: "#FF5533",
                                        strokeWidth: 3,
                                        strokeLinecap: "square",
                                        display: 'none'
                                      }}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                          <rect x="0" y="0" width="24" height="24" rx="4" ry="4"  fill={geo.properties.highlighted}></rect>
                                          <text x="5" y="15" fontFamily="Verdana" fontSize="10" fill="#000"> {geo.id}</text>
                                        </g>
                                      </svg>
                                    </Annotation>
                                  }
                                   {geo.id === 'RI' &&
                                    <Annotation
                                      subject={centroid}
                                      dx={110}
                                      dy={82}
                                      connectorProps={{
                                        stroke: "#FF5533",
                                        strokeWidth: 3,
                                        strokeLinecap: "square",
                                        display: 'none'
                                      }}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                          <rect x="0" y="0" width="24" height="24" rx="4" ry="4"  fill={geo.properties.highlighted}></rect>
                                          <text x="5" y="15" fontFamily="Verdana" fontSize="10" fill="#000"> {geo.id}</text>
                                        </g>
                                      </svg>
                                    </Annotation>
                                  }

                                {geo.id === 'NJ' &&
                                    <Annotation
                                      subject={centroid}
                                      dx={37}
                                      dy={90}
                                      connectorProps={{
                                        stroke: "#FF5533",
                                        strokeWidth: 3,
                                        strokeLinecap: "square",
                                        display: 'none'
                                      }}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                          <rect x="0" y="0" width="24" height="24" rx="4" ry="4"  fill={geo.properties.highlighted}></rect>
                                          <text x="5" y="15" fontFamily="Verdana" fontSize="10" fill="#000"> {geo.id}</text>
                                        </g>
                                      </svg>
                                    </Annotation>
                                  }

                                {geo.id === 'MD' &&
                                    <Annotation
                                      subject={centroid}
                                      dx={0}
                                      dy={185}
                                      connectorProps={{
                                        stroke: "#FF5533",
                                        strokeWidth: 3,
                                        strokeLinecap: "square",
                                        display: 'none'
                                      }}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                          <rect x="0" y="0" width="24" height="24" rx="4" ry="4"  fill={geo.properties.highlighted}></rect>
                                          <text x="5" y="15" fontFamily="Verdana" fontSize="10" fill="#000"> {geo.id}</text>
                                        </g>
                                      </svg>
                                    </Annotation>
                                  }

                                 {geo.id === 'VT' &&
                                    <Annotation
                                      subject={centroid}
                                      dx={61}
                                      dy={66}
                                      connectorProps={{
                                        stroke: "#FF5533",
                                        strokeWidth: 3,
                                        strokeLinecap: "square",
                                        display: 'none'
                                      }}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                          <rect x="0" y="0" width="24" height="24" rx="4" ry="4"  fill={geo.properties.highlighted}></rect>
                                          <text x="5" y="15" fontFamily="Verdana" fontSize="10" fill="#000"> {geo.id}</text>
                                        </g>
                                      </svg>
                                    </Annotation>
                                  }

                                   {geo.id === 'MA' &&
                                    <Annotation
                                      subject={centroid}
                                      dx={43}
                                      dy={70}
                                      connectorProps={{
                                        stroke: "#FF5533",
                                        strokeWidth: 3,
                                        strokeLinecap: "square",
                                        display: 'none'
                                      }}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                          <rect x="0" y="0" width="24" height="24" rx="4" ry="4"  fill={geo.properties.highlighted}></rect>
                                          <text x="5" y="15" fontFamily="Verdana" fontSize="10" fill="#000"> {geo.id}</text>
                                        </g>
                                      </svg>
                                    </Annotation>
                                  }

                               {geo.id === 'CT' &&
                                    <Annotation
                                      subject={centroid}
                                      dx={52}
                                      dy={90}
                                      connectorProps={{
                                        stroke: "#FF5533",
                                        strokeWidth: 2,
                                        strokeLinecap: "square",
                                        display: 'none'
                                      }}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                          <rect x="0" y="0" width="24" height="24" rx="4" ry="4"  fill={geo.properties.highlighted}></rect>
                                          <text x="5" y="15" fontFamily="Verdana" fontSize="10" fill="#000"> {geo.id}</text>
                                        </g>
                                      </svg>
                                    </Annotation>
                                  }

                                {geo.id === 'DE' &&
                                    <Annotation
                                      subject={centroid}
                                      dx={75}
                                      dy={69}
                                      connectorProps={{
                                        stroke: "#FF5533",
                                        strokeWidth: 2,
                                        strokeLinecap: "square",
                                        display: 'none'
                                      }}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                          <rect x="0" y="0" width="24" height="24" rx="4" ry="4"  fill={geo.properties.highlighted}></rect>
                                          <text x="5" y="15" fontFamily="Verdana" fontSize="10" fill="#000"> {geo.id}</text>
                                        </g>
                                      </svg>
                                    </Annotation>
                                  }

                                {geo.id === 'DC' &&
                                    <Annotation
                                      subject={centroid}
                                      dx={96}
                                      dy={97}
                                      connectorProps={{
                                        stroke: "#FF5533",
                                        strokeWidth: 2,
                                        strokeLinecap: "square",
                                        display: 'none'
                                      }}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                          <rect x="0" y="0" width="24" height="24" rx="4" ry="4"  fill={geo.properties.highlighted}></rect>
                                          <text x="5" y="15" fontFamily="Verdana" fontSize="10" fill="#000"> {geo.id}</text>
                                        </g>
                                      </svg>
                                    </Annotation>
                                  }
                                  
                                </>
                              );
                            })}

                          </>
                        );
                      }}
                    </Geographies>
                  </ComposableMap>
                </div>
              </>
            )
            :
            (
              <div className={styles.addCertificatePopup}>
                <button className={styles.closePopupBtn} onClick={() => {setOpenAddPopup(false); clearAddFieldsData();}}><ClosePopupIcon /></button>
                <p className={styles.addCompanyTitle}>Add Certificate </p>
                
                <span className={styles.lblCert}>1 - Select States: </span>
                <div className={styles.stateStyle}>
                  {stateList?.map((statesData: any, i: number) => (
                    <Tooltip title={statesData.isAvailable && 'State has a certificate'}
                    classes={{
                      popper: styles.statesTooltip,
                      tooltip: styles.tooltip,
                    }}
                    >
                        <div onClick={() => { !statesData.isAvailable && handleStateOnClick(i, statesData) }} className={clsx(styles.resaleCertList, certState[i] && styles.highlightState, statesData.isAvailable && styles.disabledCert)} key={i}>
                        {/* {!statesData.isAvailable && <span>abc </span>} */}
                        {statesData.value}
                      </div>
                      </Tooltip>
                  ))}
                </div>
                <div>
                  <div className={styles.uplaodCertMain}>
                    <span className={styles.lblCert}>2 - Select Certificate to upload: </span>
                    <input type="file" id="uplaodCert" onChange={(e) => { handleOnchangeFile(e) }} />
                    <label className={styles.uplaodCertBtn} htmlFor="uplaodCert">Choose file</label>

                    <span className={styles.uplaodCertName}>{certFileName}</span>

                  </div>
               
                </div>
                <div className={styles.selectDropdownMain}>
                 <span className={styles.lblCert}>3 - Certificate Expiry: </span>
                  <Select
                    className={styles.showdropdwn}
                    value={
                      certExpiration !== null && certExpiration !== undefined && certExpiration !== ''
                        ? certExpiration
                        : 'Choose One'
                    }
                    onChange={(event) => {
                      setCertExpiration(event.target.value);
                    }}
                  >
                    <MenuItem key={'Choose One'} value={'Choose One'} disabled>
                      {'Choose One'}
                    </MenuItem>
                    {referenceData?.ref_resale_cert_expiration?.map((item, index) => (
                      <MenuItem key={index} value={item.expiration_value}>
                        <span>{item.expiration_display_string}</span>
                      </MenuItem>
                    ))}
                  </Select>
                </div>
                <div className={styles.submitBtnMain}>
                  <button
                    className={styles.submitBtn}
                    onClick={handleUploadFile}
                    disabled={handleSubmitDisabled()}
                  >
                    Submit
                  </button>
                </div>
              </div>
            )
        }

      </Box>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={clsx(styles.continuetext, styles.continuetext1)}>{deleteCertData.stateName}</p>
          <p className={styles.continuetext}>Are you sure you want to delete Resale Cert?</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={()=>{setShowConfirmationPopup(false)}}>
              No
            </button>
          </div>
        </div>
      </MatPopup> 
    </>

  );
};

export default Map;
import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Divider,
  Chip,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';

const HotspotProperties = ({ 
  selectedHotspot, 
  hotspots,
  allScreens = [],
  currentScreenId = '',
  onHotspotUpdate, 
  onHotspotDelete,
  disabled = false,
  saveError = null,
  validationError = null,
  onInputValueChange
}: {
  selectedHotspot: any;
  hotspots: any[];
  allScreens?: any[];
  currentScreenId?: string;
  onHotspotUpdate: (hotspotId: any, updates: any) => void;
  onHotspotDelete: (hotspotId: string) => void;
  disabled?: boolean;
  saveError?: string | null;
  validationError?: string | null;
  onInputValueChange?: (hotspotId: string, value: string) => void;
}) => {
  const [elementId, setElementId] = useState('');
  const [error, setError] = useState('');
  const [hasUserTyped, setHasUserTyped] = useState(false);

  // Validate element ID for uniqueness and format
  const validateElementId = useCallback((value: string, excludeHotspotId?: string) => {
    const trimmed = value.trim();
    
    if (!trimmed) {
      return 'Element ID is required';
    }
    
    // Check format
    if (!/^[a-zA-Z0-9_-]+$/.test(trimmed)) {
      return 'Element ID can only contain letters, numbers, hyphens, and underscores';
    }
    
    // Check uniqueness within current screen
    const isDuplicateInScreen = hotspots.some(
      h => h.id !== excludeHotspotId && h.element_id?.trim() === trimmed
    );
    
    if (isDuplicateInScreen) {
      return 'Element ID must be unique within this screen';
    }
    
    // Check uniqueness across all screens
    if (allScreens && Array.isArray(allScreens) && currentScreenId) {
      const isDuplicateAcrossScreens = allScreens.some((screen: any) => {
        if (screen.id === currentScreenId) return false;
        return (screen.hotspots || []).some((h: any) => {
          return h.element_id?.trim() === trimmed;
        });
      });
      
      if (isDuplicateAcrossScreens) {
        return 'Element ID must be unique across all screens';
      }
    }
    
    return '';
  }, [hotspots, allScreens, currentScreenId]);

  useEffect(() => {
    if (!hasUserTyped && selectedHotspot) {
      const currentValue = selectedHotspot.element_id || '';
      setElementId(currentValue);
      
      const errorToShow = validationError || validateElementId(currentValue, selectedHotspot.id);
      setError(errorToShow);
      
      if (onInputValueChange) {
        onInputValueChange(selectedHotspot.id, currentValue);
      }
    } else if (!selectedHotspot) {
      setElementId('');
      setError('');
      setHasUserTyped(false);
    }
    
  }, [selectedHotspot?.id]);
  
  
  useEffect(() => {
    if (validationError && selectedHotspot && !hasUserTyped) {
      setError(validationError);
    }
  }, [validationError, selectedHotspot?.id, hasUserTyped]);

  const handleElementIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setElementId(value);
    setHasUserTyped(true);
  
    const validationError = validateElementId(value, selectedHotspot?.id);
    setError(validationError);
    
    if (selectedHotspot && onInputValueChange) {
      onInputValueChange(selectedHotspot.id, value);
    }
    
    if (!validationError && selectedHotspot) {
      const trimmed = value.trim();
      const currentValue = (selectedHotspot.element_id || '').trim();
      if (trimmed !== currentValue) {
        onHotspotUpdate(selectedHotspot.id, { element_id: trimmed });
      }
    }
  };

  const handleElementIdBlur = () => {
    if (!selectedHotspot) {
      setHasUserTyped(false);
      return;
    }
    
    const trimmed = elementId.trim();
    const validationError = validateElementId(trimmed, selectedHotspot.id);
    
    if (validationError) {
      setError(validationError);
      return;
    }


    const currentValue = (selectedHotspot.element_id || '').trim();
    if (trimmed !== currentValue) {
      onHotspotUpdate(selectedHotspot.id, { element_id: trimmed });
    }
    setError('');
    setHasUserTyped(false);
  };

  const handleDelete = () => {
    if (selectedHotspot) {
      onHotspotDelete(selectedHotspot.id);
    }
  };

  const formatCoordinate = (value: number) => {
    return (value * 100).toFixed(1) + '%';
  };

  if (!selectedHotspot) {
    return (
      <Paper
        sx={{
          p: 3,
          height: 'fit-content',
          minHeight: 200,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f9f9f9',
        }}
      >
        <Typography variant="body2" color="textSecondary" textAlign="center">
          Select a hotspot to view and edit its properties
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 3, height: 'fit-content' }}>
      <Typography variant="h6" sx={{ mb: 2, color: '#0d1b2a' }}>
        Hotspot Properties
      </Typography>

      {/* Element ID */}
      <Box sx={{ mb: 1 }}>
        <TextField
          fullWidth
          label="Element ID"
          value={elementId}
          onChange={handleElementIdChange}
          onBlur={handleElementIdBlur}
          error={!!error || !!validationError || !!(saveError && !selectedHotspot.element_id)}
          helperText={
            error || 
            validationError ||
            (saveError && !selectedHotspot.element_id ? saveError : '')
          }
          disabled={disabled}
          placeholder="e.g., purchase-button"
        />
      </Box>
      <Typography variant="caption" sx={{ color: '#666', display: 'block' }}>
        Unique identifier for this hotspot (e.g., "purchase-button"). Must be unique across all screens.
      </Typography>

      <Divider sx={{ my: 2 }} />

      {/* Status */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
          Status
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Chip
            icon={selectedHotspot.element_id ? <CheckCircleIcon /> : <ErrorIcon />}
            label={selectedHotspot.element_id ? 'Configured' : 'Needs Element ID'}
            color={selectedHotspot.element_id ? 'success' : 'warning'}
            size="small"
          />
          <Chip
            label={selectedHotspot.has_video_mapping ? 'Has Video' : 'No Video'}
            color={selectedHotspot.has_video_mapping ? 'success' : 'default'}
            size="small"
          />
        </Box>
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* Dimensions (Read-only) */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
          Dimensions (Normalized)
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1 }}>
          <TextField
            label="X Position"
            value={formatCoordinate(selectedHotspot.coords_json.x)}
            size="small"
            InputProps={{ readOnly: true }}
          />
          <TextField
            label="Y Position"
            value={formatCoordinate(selectedHotspot.coords_json.y)}
            size="small"
            InputProps={{ readOnly: true }}
          />
          <TextField
            label="Width"
            value={formatCoordinate(selectedHotspot.coords_json.width)}
            size="small"
            InputProps={{ readOnly: true }}
          />
          <TextField
            label="Height"
            value={formatCoordinate(selectedHotspot.coords_json.height)}
            size="small"
            InputProps={{ readOnly: true }}
          />
        </Box>
        <Typography variant="caption" sx={{ color: '#666', mt: 1, display: 'block' }}>
          Coordinates are normalized (0-100%) relative to the screen image
        </Typography>
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* Actions */}
      <Box>
        <Button
          variant="outlined"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={handleDelete}
          disabled={disabled}
          fullWidth
        >
          Delete Hotspot
        </Button>
      </Box>
    </Paper>
  );
};

export default HotspotProperties;

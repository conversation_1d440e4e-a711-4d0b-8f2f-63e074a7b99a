import { useDrop } from 'react-dnd';
import { useTemplateStore } from '../stores/templateStore';
import type { BlockContent, BlockType } from '../types';
import { CanvasBlock } from './CanvasBlock';
import styles from './Canvas.module.scss';

interface CanvasProps {
  selectedBlockId: string | null;
  onSelectBlock: (id: string | null) => void;
}

export function Canvas({ selectedBlockId, onSelectBlock }: CanvasProps) {
  const { blocks, addBlock, moveBlock, settings } = useTemplateStore();

  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'block',
    drop: (item: { type?: BlockType; draggedBlock?: BlockContent; fromIndex?: number }, monitor) => {
      const didDrop = monitor.didDrop();
      if (didDrop) return;

      if (item.type) {
        const newBlock: BlockContent = {
          id: `block-${Date.now()}`,
          type: item.type,
          content: getDefaultContent(item.type),
          styles: getDefaultStyles(item.type),
        };
        addBlock(newBlock);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
    }),
  }), [addBlock]);

  const handleDropBlock = (dragIndex: number, dropIndex: number) => {
    if (dragIndex !== dropIndex) {
      moveBlock(dragIndex, dropIndex);
    }
  };

  const getDefaultContent = (type: BlockType): string => {
    switch (type) {
      case 'heading1': return 'Heading 1';
      case 'heading2': return 'Heading 2';
      case 'heading3': return 'Heading 3';
      case 'paragraph': return 'Enter your text here...';
      case 'button': return 'Click Here';
      case 'image': return 'https://via.placeholder.com/600x300';
      case 'html': return '<div>Custom HTML</div>';
      default: return '';
    }
  };

  const getDefaultStyles = (type: BlockType) => {
    switch (type) {
      case 'heading1':
        return { fontSize: '32px', color: '#333333', textAlign: 'left' as const };
      case 'heading2':
        return { fontSize: '24px', color: '#333333', textAlign: 'left' as const };
      case 'heading3':
        return { fontSize: '18px', color: '#333333', textAlign: 'left' as const };
      case 'paragraph':
        return { fontSize: '14px', color: '#555555', textAlign: 'left' as const };
      case 'button':
        return {
          backgroundColor: '#007bff',
          color: '#ffffff',
          textAlign: 'center' as const,
          padding: '12px 30px',
          borderRadius: '4px',
        };
      case 'spacer':
        return { height: '20px' };
      case 'divider':
        return { border: '1px solid #e0e0e0', padding: '20px 0' };
      default:
        return {};
    }
  };

  return (
    <div className={styles.canvasContainer}>
      <div
        ref={drop as any}
        className={`${styles.canvas} ${isOver ? styles.dragOver : ''}`}
        style={{
          maxWidth: `${settings.maxWidth}px`,
          backgroundColor: settings.contentBackgroundColor,
        }}
      >
        {blocks.length === 0 ? (
          <div className={styles.emptyCanvas}>
            Drag blocks here to start building your template
          </div>
        ) : (
          blocks.map((block, index) => (
            <CanvasBlock
              key={block.id}
              block={block}
              index={index}
              isSelected={selectedBlockId === block.id}
              onSelect={() => onSelectBlock(block.id)}
              onDropBlock={handleDropBlock}
            />
          ))
        )}
      </div>
    </div>
  );
}

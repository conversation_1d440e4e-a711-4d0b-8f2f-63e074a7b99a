import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";

interface UploadReferenceDataParams {
  formData: FormData;
  file_type: 'search' | 'pricing';
}

const useUploadReferenceData = () => {
  return useMutation(async ({ formData, file_type }: UploadReferenceDataParams) => {
    try {
      // Add the file_type to the FormData
      formData.append('file_type', file_type);

      const response = await axios.post(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/upload-file`,
        // 'http://node-new-env-env.eba-8a5friti.us-east-1.elasticbeanstalk.com/widget-admin-dashboard/excel',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data?.data
        ) {
          throw new Error(response.data.data.error_message);
          
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message || error?.response?.data?.message || "");
    }
  });
};

export default useUploadReferenceData; 
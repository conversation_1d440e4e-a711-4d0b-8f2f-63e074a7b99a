import CloseIcon from '@mui/icons-material/Close';
import { useTemplateStore } from '../stores/templateStore';
import styles from './PropertiesPanel.module.scss';

interface PropertiesPanelProps {
  selectedBlockId: string | null;
  onClose: () => void;
}

export function PropertiesPanel({ selectedBlockId, onClose }: PropertiesPanelProps) {
  const { blocks, updateBlock } = useTemplateStore();

  if (!selectedBlockId) return null;

  const block = blocks.find((b) => b.id === selectedBlockId);
  if (!block) return null;

  const handleStyleChange = (key: string, value: string) => {
    updateBlock(block.id, {
      styles: {
        ...block.styles,
        [key]: value,
      },
    });
  };

  const handleAttributeChange = (key: string, value: string) => {
    updateBlock(block.id, {
      attributes: {
        ...block.attributes,
        [key]: value,
      },
    });
  };

  const handleContentChange = (value: string) => {
    updateBlock(block.id, { content: value });
  };

  return (
    <div className={styles.propertiesPanel}>
      <div className={styles.panelHeader}>
        <h3>{block.type.charAt(0).toUpperCase() + block.type.slice(1)} Properties</h3>
        <button className={styles.closeBtn} onClick={onClose}>
          <CloseIcon sx={{ fontSize: 18 }} />
        </button>
      </div>

      <div className={styles.panelContent}>
        {(block.type === 'heading1' ||
          block.type === 'heading2' ||
          block.type === 'heading3' ||
          block.type === 'paragraph') && (
          <>
            <div className={styles.propertyGroup}>
              <label>Text</label>
              <textarea
                value={block.content}
                onChange={(e) => handleContentChange(e.target.value)}
                rows={3}
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>Text Align</label>
              <select
                value={block.styles?.textAlign || 'left'}
                onChange={(e) => handleStyleChange('textAlign', e.target.value)}
              >
                <option value="left">Left</option>
                <option value="center">Center</option>
                <option value="right">Right</option>
              </select>
            </div>

            <div className={styles.propertyGroup}>
              <label>Font Size</label>
              <input
                type="text"
                value={block.styles?.fontSize || ''}
                onChange={(e) => handleStyleChange('fontSize', e.target.value)}
                placeholder="e.g., 16px"
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>Color</label>
              <input
                type="color"
                value={block.styles?.color || '#000000'}
                onChange={(e) => handleStyleChange('color', e.target.value)}
              />
            </div>
          </>
        )}

        {block.type === 'button' && (
          <>
            <div className={styles.propertyGroup}>
              <label>Button Text</label>
              <input
                type="text"
                value={block.content}
                onChange={(e) => handleContentChange(e.target.value)}
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>URL</label>
              <input
                type="text"
                value={block.attributes?.href || ''}
                onChange={(e) => handleAttributeChange('href', e.target.value)}
                placeholder="https://example.com"
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>Background Color</label>
              <input
                type="color"
                value={block.styles?.backgroundColor || '#007bff'}
                onChange={(e) => handleStyleChange('backgroundColor', e.target.value)}
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>Text Color</label>
              <input
                type="color"
                value={block.styles?.color || '#ffffff'}
                onChange={(e) => handleStyleChange('color', e.target.value)}
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>Alignment</label>
              <select
                value={block.styles?.textAlign || 'center'}
                onChange={(e) => handleStyleChange('textAlign', e.target.value)}
              >
                <option value="left">Left</option>
                <option value="center">Center</option>
                <option value="right">Right</option>
              </select>
            </div>

            <div className={styles.propertyGroup}>
              <label>Border Radius</label>
              <input
                type="text"
                value={block.styles?.borderRadius || ''}
                onChange={(e) => handleStyleChange('borderRadius', e.target.value)}
                placeholder="e.g., 4px"
              />
            </div>
          </>
        )}

        {block.type === 'image' && (
          <>
            <div className={styles.propertyGroup}>
              <label>Image URL</label>
              <input
                type="text"
                value={block.content}
                onChange={(e) => handleContentChange(e.target.value)}
                placeholder="https://example.com/image.jpg"
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>Alt Text</label>
              <input
                type="text"
                value={block.attributes?.alt || ''}
                onChange={(e) => handleAttributeChange('alt', e.target.value)}
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>Width</label>
              <input
                type="text"
                value={block.styles?.width || ''}
                onChange={(e) => handleStyleChange('width', e.target.value)}
                placeholder="e.g., 100% or 300px"
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>Alignment</label>
              <select
                value={block.styles?.textAlign || 'center'}
                onChange={(e) => handleStyleChange('textAlign', e.target.value)}
              >
                <option value="left">Left</option>
                <option value="center">Center</option>
                <option value="right">Right</option>
              </select>
            </div>
          </>
        )}

        {block.type === 'spacer' && (
          <div className={styles.propertyGroup}>
            <label>Height</label>
            <input
              type="text"
              value={block.styles?.height || '20px'}
              onChange={(e) => handleStyleChange('height', e.target.value)}
              placeholder="e.g., 20px"
            />
          </div>
        )}

        {block.type === 'divider' && (
          <>
            <div className={styles.propertyGroup}>
              <label>Border Style</label>
              <input
                type="text"
                value={block.styles?.border || '1px solid #e0e0e0'}
                onChange={(e) => handleStyleChange('border', e.target.value)}
                placeholder="e.g., 1px solid #e0e0e0"
              />
            </div>

            <div className={styles.propertyGroup}>
              <label>Spacing</label>
              <input
                type="text"
                value={block.styles?.padding || '20px 0'}
                onChange={(e) => handleStyleChange('padding', e.target.value)}
                placeholder="e.g., 20px 0"
              />
            </div>
          </>
        )}

        {block.type === 'html' && (
          <div className={styles.propertyGroup}>
            <label>HTML Content</label>
            <textarea
              value={block.content}
              onChange={(e) => handleContentChange(e.target.value)}
              rows={10}
              style={{ fontFamily: 'monospace', fontSize: '12px' }}
            />
          </div>
        )}

        <div className={styles.propertyGroup}>
          <label>Padding</label>
          <input
            type="text"
            value={block.styles?.padding || ''}
            onChange={(e) => handleStyleChange('padding', e.target.value)}
            placeholder="e.g., 10px or 10px 20px"
          />
        </div>

        <div className={styles.propertyGroup}>
          <label>Margin</label>
          <input
            type="text"
            value={block.styles?.margin || ''}
            onChange={(e) => handleStyleChange('margin', e.target.value)}
            placeholder="e.g., 10px or 10px 20px"
          />
        </div>
      </div>
    </div>
  );
}

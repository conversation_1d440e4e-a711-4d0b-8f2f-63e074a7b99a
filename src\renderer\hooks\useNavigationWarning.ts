import React, { useEffect, useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Custom hook to warn users about unsaved changes when navigating away
 * @param {boolean} hasUnsavedChanges - Whether there are unsaved changes
 * @param {string} message - Custom warning message
 */
export const useNavigationWarning = (hasUnsavedChanges: boolean, message: string = 'You have unsaved changes. Are you sure you want to leave?') => {
  const navigate = useNavigate();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<{ to: any; options?: any } | null>(null);

  // Warn on browser refresh/close
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    };

    if (hasUnsavedChanges) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges, message]);

  // Handle dialog confirmation
  const handleConfirm = useCallback(() => {
    setDialogOpen(false);
    if (pendingNavigation) {
      navigate(pendingNavigation.to, pendingNavigation.options || {});
      setPendingNavigation(null);
    }
  }, [pendingNavigation, navigate]);

  // Handle dialog cancellation
  const handleCancel = useCallback(() => {
    setDialogOpen(false);
    setPendingNavigation(null);
  }, []);

  // Create a navigation function that checks for unsaved changes
  const navigateWithWarning = useCallback((to: string | number, options: any = {}) => {
    if (hasUnsavedChanges) {
      setPendingNavigation({ to, options });
      setDialogOpen(true);
      return false;
    }
    navigate(to as any, options);
    return true;
  }, [hasUnsavedChanges, navigate]);

  return {
    navigateWithWarning,
    dialogOpen,
    message,
    handleConfirm,
    handleCancel,
  };
};

/**
 * Hook to create a safe navigation function for links/buttons
 * @param {boolean} hasUnsavedChanges - Whether there are unsaved changes
 * @param {Function} onNavigate - Function to call when navigation is confirmed
 * @param {string} message - Custom warning message
 */
export const useSafeNavigation = (hasUnsavedChanges: boolean, onNavigate: () => void, message: string = 'You have unsaved changes. Are you sure you want to leave?') => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [pendingEvent, setPendingEvent] = useState<React.MouseEvent | null>(null);

  // Handle dialog confirmation
  const handleConfirm = useCallback(() => {
    setDialogOpen(false);
    if (pendingEvent) {
      pendingEvent.preventDefault();
    }
    if (onNavigate) {
      onNavigate();
    }
    setPendingEvent(null);
  }, [onNavigate, pendingEvent]);

  // Handle dialog cancellation
  const handleCancel = useCallback(() => {
    setDialogOpen(false);
    setPendingEvent(null);
  }, []);

  const handleNavigation = useCallback((e: React.MouseEvent) => {
    if (hasUnsavedChanges) {
      e.preventDefault();
      setPendingEvent(e);
      setDialogOpen(true);
    } else if (onNavigate) {
      onNavigate();
    }
  }, [hasUnsavedChanges, onNavigate]);

  return {
    handleNavigation,
    dialogOpen,
    message,
    handleConfirm,
    handleCancel,
  };
};

.nexusDataContainer {
  width: 100%;
  height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.searchBox {
  margin-bottom: 12px;
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0;

  @media (max-width: 767px) {
    justify-content: flex-start;
  }
}

.searchContainer {
  display: flex;
  position: relative;
  
  input {
    padding-right: 30px;
    height: 40px;
    border-radius: 4px;
    border: solid 1px #c4c8d1;
    font-size: 14px;
    padding: 0px 12px;
    
    &:focus {
      outline: none;
    }
  }
  
  .clearInputIcon {
    position: absolute;
    top: 8px;
    right: 6px;
    cursor: pointer;
    z-index: 99;
    background-color: transparent;
    border: 0px;
    padding: 0px;
    display: flex;
    align-items: center;
    
    svg {
      width: 24px;
      height: 24px;
      path {
        fill: var(--primaryColor);
      }
    }
  }
}

.searchInput {
  margin-left: auto;
  
  @media (max-width: 767px) {
    width: 100%;
    margin-left: 0;
  }
}

.noDataFound {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  width: 100%;
  text-align: center;

  .errorMessage {
    font-size: 16px;
    color: #d32f2f;
    text-align: center;
  }
}

.ag_theme_quartz {
  --ag-foreground-color: #676f7c;
  --ag-background-color: white;
  --ag-header-foreground-color: white;
  --ag-header-background-color: #676f7c;
  --ag-odd-row-background-color: #f2f2f2;
  --ag-header-column-resize-handle-color: rgb(126, 46, 132);
  --ag-font-size: 14px;
  --ag-font-family: monospace;
  --ag-icon-font-code-aggregation: "\f247";
  --ag-icon-font-color-group: red;
  --ag-icon-font-weight-group: normal;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.agGridAdmin {
  --ag-icon-font-code-asc: '\25B2';
  --ag-icon-font-code-desc: '\25BC';
  --ag-icon-font-code-none: '\25B2\25BC';
  
  .ag-center-cols-viewport {
    min-height: 5000px !important;
  }
  
  .ag-icon-asc::before {
    content: var(--ag-icon-font-code-asc);
  }
  
  .ag-icon-none::before {
    content: var(--ag-icon-font-code-none);
    color: green;
    padding: 2px;
    margin-bottom: 5px;
    font-size: 20px !important;
  }
  
  .ag-root-wrapper {
    .ag-root-wrapper-body {
      .ag-body-horizontal-scroll-viewport {
        overflow-x: auto;
        
        &::-webkit-scrollbar {
          width: 8px;
          height: 6px;
        }
        
        &::-webkit-scrollbar-track {
          box-shadow: inset 0 0 6px #a8b2bb;
          border-radius: 4px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #a8b2bb;
          border-radius: 4px;
        }
      }
      
      .ag-header-row {
        .ag-header-cell {
          padding-left: 20px;
          padding-right: 20px;
          line-height: 1.2;
          font-weight: 600;
          font-size: 16px;
          margin: 0;
          text-align: left;
          color: #fff;
          background: #676f7c;
          
          &:hover {
            color: #fff;
            background: #676f7c;
          }
        }
        
        .ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover {
          color: #fff;
          background: #676f7c;
        }
      }
      
      .ag-body-viewport-wrapper.ag-layout-normal {
        overflow-x: scroll;
        overflow-y: scroll;
      }
      
      ::-webkit-scrollbar {
        -webkit-appearance: none;
        width: 8px;
        height: 6px;
      }
      
      ::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background: #a8b2bb;
        box-shadow: inset 0 0 6px #a8b2bb;
      }
      
      .ag-body {
        .ag-body-viewport {
          .ag-center-cols-clipper {
            .ag-row-odd {
              background-color: #f2f2f2;
            }
            
            .ag-cell {
              cursor: pointer;
            }
            
            .red-border {
              border: 1px solid red;
            }
          }
        }
      }
    }
  }
}

.header_initial {
  font-weight: bold;
  font-size: 15px;
  padding-right: 10px;
  padding-left: 10px;
}

.cell_default {
  align-content: center;
  padding-right: 10px;
  padding-left: 10px;
}

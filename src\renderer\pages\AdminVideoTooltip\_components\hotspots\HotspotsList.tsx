import React from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  CropFree as HotspotIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';

const HotspotsList = ({ 
  hotspots, 
  selectedHotspotId, 
  onHotspotSelect, 
  onHotspotDelete,
  disabled = false 
}) => {
  const getHotspotStatus = (hotspot) => {
    if (!hotspot.element_id) {
      return { color: 'warning', icon: <ErrorIcon />, text: 'Needs ID' };
    }
    if (hotspot.has_video_mapping) {
      return { color: 'success', icon: <CheckCircleIcon />, text: 'Complete' };
    }
    return { color: 'info', icon: <CheckCircleIcon />, text: 'Ready' };
  };

  const formatCoordinates = (coords) => {
    return `${(coords.x * 100).toFixed(0)}%, ${(coords.y * 100).toFixed(0)}% (${(coords.width * 100).toFixed(0)}×${(coords.height * 100).toFixed(0)}%)`;
  };

  if (hotspots.length === 0) {
    return (
      <Paper
        sx={{
          p: 3,
          textAlign: 'center',
          backgroundColor: '#f9f9f9',
        }}
      >
        <HotspotIcon sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
        <Typography variant="h6" sx={{ color: '#666', mb: 1 }}>
          No hotspots yet
        </Typography>
        <Typography variant="body2" sx={{ color: '#999' }}>
          Use the Rectangle tool to draw your first hotspot
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height: 'fit-content', maxHeight: 400, overflow: 'auto' }}>
      <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
        <Typography variant="h6" sx={{ color: '#0d1b2a' }}>
          Hotspots ({hotspots.length})
        </Typography>
        <Typography variant="caption" sx={{ color: '#666' }}>
          Click to select and edit
        </Typography>
      </Box>

      <List sx={{ p: 0 }}>
        {hotspots.map((hotspot, index) => {
          const status = getHotspotStatus(hotspot);
          const isSelected = hotspot.id === selectedHotspotId;

          return (
            <ListItem
              key={hotspot.id}
              disablePadding
              sx={{
                borderBottom: index < hotspots.length - 1 ? '1px solid #f0f0f0' : 'none',
              }}
            >
              <ListItemButton
                selected={isSelected}
                onClick={() => onHotspotSelect(hotspot.id)}
                disabled={disabled}
                sx={{
                  '&.Mui-selected': {
                    backgroundColor: 'rgba(65, 90, 119, 0.1)',
                    borderLeft: '3px solid #415a77',
                  },
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  <HotspotIcon 
                    sx={{ 
                      color: isSelected ? '#415a77' : '#999',
                      fontSize: 20,
                    }} 
                  />
                </ListItemIcon>

                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: isSelected ? 'medium' : 'normal',
                          color: isSelected ? '#415a77' : '#0d1b2a',
                        }}
                      >
                        {hotspot.element_id || `Hotspot ${index + 1}`}
                      </Typography>
                      <Chip
                        icon={status.icon}
                        label={status.text}
                        size="small"
                        color={status.color}
                        sx={{ height: 20, fontSize: '0.7rem' }}
                      />
                    </Box>
                  }
                  secondary={
                    <Typography variant="caption" sx={{ color: '#666' }}>
                      {formatCoordinates(hotspot.coords_json)}
                    </Typography>
                  }
                />

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Tooltip title="Focus on canvas">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        onHotspotSelect(hotspot.id);
                      }}
                      disabled={disabled}
                      sx={{ color: '#666' }}
                    >
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Delete hotspot">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        onHotspotDelete(hotspot.id);
                      }}
                      disabled={disabled}
                      sx={{ 
                        color: '#f44336',
                        '&:hover': {
                          backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        },
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
    </Paper>
  );
};

export default HotspotsList;

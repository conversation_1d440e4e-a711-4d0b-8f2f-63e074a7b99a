import CodeIcon from '@mui/icons-material/Code';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import styles from './SnippetLibrary.module.scss';

interface SnippetLibraryProps {
  onInsert: (snippet: string) => void;
}

export function SnippetLibrary({ onInsert }: SnippetLibraryProps) {
  const snippets = [
    {
      name: 'Basic Table',
      code: `<table role="presentation" width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td>Content here</td>
  </tr>
</table>`,
    },
    {
      name: 'But<PERSON>',
      code: `<!--[if mso]>
<v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="#" style="height:40px;v-text-anchor:middle;width:200px;" arcsize="10%" stroke="f" fillcolor="#007bff">
<w:anchorlock/>
<center>
<![endif]-->
<a href="#" style="background-color: #007bff; border-radius: 4px; color: #ffffff; display: inline-block; font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; padding: 12px 30px; text-decoration: none;">
  Click Here
</a>
<!--[if mso]>
</center>
</v:roundrect>
<![endif]-->`,
    },
    {
      name: 'Responsive Image',
      code: `<img src="https://example.com/image.jpg" alt="Description" style="display: block; max-width: 100%; height: auto;" width="600" />`,
    },
    {
      name: 'Two Column',
      code: `<table role="presentation" width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td valign="top" width="50%">
      <!-- Left column -->
      <table role="presentation" width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
          <td style="padding: 10px;">
            Left content
          </td>
        </tr>
      </table>
    </td>
    <td valign="top" width="50%">
      <!-- Right column -->
      <table role="presentation" width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
          <td style="padding: 10px;">
            Right content
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>`,
    },
    {
      name: 'Liquid For Loop',
      code: `{% for item in items %}
  <p>{{ item.name }}</p>
{% endfor %}`,
    },
    {
      name: 'Liquid If Statement',
      code: `{% if condition %}
  <p>Content when true</p>
{% else %}
  <p>Content when false</p>
{% endif %}`,
    },
    {
      name: 'Variable',
      code: `{{ variable_name }}`,
    },
    {
      name: 'Spacer',
      code: `<tr>
  <td height="20" style="font-size: 0; line-height: 0;">
    &nbsp;
  </td>
</tr>`,
    },
    {
      name: 'Divider',
      code: `<tr>
  <td style="padding: 20px 0;">
    <table width="100%" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td style="border-top: 1px solid #e0e0e0;"></td>
      </tr>
    </table>
  </td>
</tr>`,
    },
  ];

  return (
    <div className={styles.snippetLibrary}>
      <div className={styles.stickyHeader}>
        <h3>Snippets</h3>
      </div>
      <div className={styles.snippetList}>
        {snippets.map((snippet) => (
          <div key={snippet.name} className={styles.snippetItem}>
            <div className={styles.snippetHeader}>
              <CodeIcon sx={{ fontSize: 14 }} />
              <span>{snippet.name}</span>
            </div>
            <button
              className={styles.insertBtn}
              onClick={() => onInsert(snippet.code)}
              title="Insert snippet"
            >
              <ContentCopyIcon sx={{ fontSize: 14 }} />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}

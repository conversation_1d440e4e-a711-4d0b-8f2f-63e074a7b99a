import { useState, useEffect, useRef } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Paper,
  Alert,
  CircularProgress,
  Link,
  Button,
  Grid,
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  Visibility as VisibilityIcon,
} from "@mui/icons-material";
import { useNotification } from "../../contexts/NotificationContext";
import useGetScreen from "../../hooks/admin-video-tooltip/useGetScreen";
import { useCreateMapping } from "../../hooks/admin-video-tooltip/useCreateMapping";
import { useUpdateMapping } from "../../hooks/admin-video-tooltip/useUpdateMapping";
import MappingCanvas from "./_components/mappings/MappingCanvas";
import UnassignedHotspots from "./_components/mappings/UnassignedHotspots";
import MappingPanel from "./_components/mappings/MappingPanel";
import {
  getAdminVideoTooltipScreenHotspotsRoute,
  routePaths,
} from "../../utils/constant";
import type {
  Hotspot,
  MappingCreateRequest,
  MappingUpdateRequest,
} from "../../types/api";

// Extended hotspot type for API response that includes flattened video mapping data
interface ApiHotspot
  extends Omit<Hotspot, "video_mapping" | "has_video_mapping"> {
  video_url?: string;
  video_mapping_id?: string;
  mapping_priority?: number;
  mapping_title?: string;
  mapping_description?: string;
  has_video_mapping: boolean | number;
}

const MappingsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { showNotification } = useNotification() as {
    showNotification: (message: string, severity?: string) => void;
  };

  // React Query hooks
  const {
    data: screen,
    isLoading: loading,
    error: queryError,
    refetch,
  } = useGetScreen(id as string);

  // Mutations
  const createMappingMutation = useCreateMapping();
  const updateMappingMutation = useUpdateMapping();

  // Local state
  const [hotspots, setHotspots] = useState<Hotspot[]>([]);
  const [selectedHotspotId, setSelectedHotspotId] = useState<string | null>(
    null
  );
  // Ref to preserve selection across refetches
  const preservedSelectedHotspotIdRef = useRef<string | null>(null);

  // Transform hotspots from API to frontend format
  const transformHotspotsFromAPI = (
    hotspots: ApiHotspot[],
    imageWidth: number,
    imageHeight: number
  ): Hotspot[] => {
    if (!hotspots || !imageWidth || !imageHeight) return [];

    return hotspots.map((hotspot: ApiHotspot) => {
      const coords = hotspot.coords_json;
      const isNormalized =
        coords.x <= 1 &&
        coords.y <= 1 &&
        coords.width <= 1 &&
        coords.height <= 1;

      const hasVideoMapping =
        hotspot.has_video_mapping === 1 || hotspot.has_video_mapping === true;
      const videoMapping =
        hasVideoMapping && hotspot.video_mapping_id
          ? {
              id: hotspot.video_mapping_id,
              video_url: hotspot.video_url || "",
              is_enabled: true,
              priority: hotspot.mapping_priority || 10,
              title: (hotspot as any).title || undefined,
              description: (hotspot as any).description || undefined,
              caption_url: (hotspot as any).caption_url || undefined,
              is_caption_enabled: (hotspot as any).is_caption_enabled !== undefined ? (hotspot as any).is_caption_enabled : true,
            }
          : null;

      console.log(
        `🔍 Hotspot ${hotspot.element_id}: coords=${JSON.stringify(
          coords
        )}, isNormalized=${isNormalized}, hasMapping=${hasVideoMapping}, videoUrl=${
          hotspot.video_url
        }`
      );

      return {
        ...hotspot,
        id:
          hotspot.id ||
          `hotspot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        coords_json: isNormalized
          ? coords
          : {
              x: coords.x / imageWidth,
              y: coords.y / imageHeight,
              width: coords.width / imageWidth,
              height: coords.height / imageHeight,
            },
        has_video_mapping: hasVideoMapping,
        video_mapping: videoMapping,
      } as Hotspot;
    });
  };

  // Process screen data when it changes
  useEffect(() => {
    if (screen) {
      console.log("🔍 Mappings - Screen data from API:", screen);
      console.log("🔍 Mappings - Raw hotspots from API:", screen.hotspots);

      const transformedHotspots = transformHotspotsFromAPI(
        (screen.hotspots || []) as ApiHotspot[],
        screen.natural_width || 1920,
        screen.natural_height || 1080
      );

      console.log(
        "🔄 Mappings - Transformed hotspots for frontend:",
        transformedHotspots
      );

      const hotspotsWithMappings = transformedHotspots.filter(
        (h: Hotspot) => h.has_video_mapping
      );
      console.log(
        "🎥 Mappings - Hotspots with video mappings:",
        hotspotsWithMappings
      );

      setHotspots(transformedHotspots);

      // Preserve the current selection if it still exists in the new data
      const hotspotIdToPreserve = preservedSelectedHotspotIdRef.current || selectedHotspotId;
      if (hotspotIdToPreserve) {
        const currentHotspotStillExists = transformedHotspots.find(
          (h: Hotspot) => h.id === hotspotIdToPreserve
        );
        if (currentHotspotStillExists) {
          // Keep the current selection and ensure ref is updated
          setSelectedHotspotId(hotspotIdToPreserve);
          preservedSelectedHotspotIdRef.current = hotspotIdToPreserve;
          return;
        }
      }

      // Only reset selection if current selection is invalid or doesn't exist
      const configurableHotspotsWithMappings = transformedHotspots.filter(
        (h: Hotspot) => h.element_id && h.has_video_mapping
      );
      if (configurableHotspotsWithMappings.length > 0) {
        const newSelection = configurableHotspotsWithMappings[0].id;
        setSelectedHotspotId(newSelection);
        preservedSelectedHotspotIdRef.current = newSelection;
      } else {
        const unassignedHotspots = transformedHotspots.filter(
          (h: Hotspot) => h.element_id && !h.has_video_mapping
        );
        if (unassignedHotspots.length > 0) {
          const newSelection = unassignedHotspots[0].id;
          setSelectedHotspotId(newSelection);
          preservedSelectedHotspotIdRef.current = newSelection;
        }
      }
    }
  }, [screen]);

  const handleHotspotSelect = (hotspotId: string) => {
    setSelectedHotspotId(hotspotId);
    preservedSelectedHotspotIdRef.current = hotspotId;
  };

  const handleMappingUpdate = async (
    hotspotId: string,
    mappingData: {
      video_url?: string;
      is_enabled?: boolean;
      title?: string;
      description?: string;
      caption_url?: string;
      is_caption_enabled?: boolean;
    }
  ) => {
    try {
      const hotspot = hotspots.find((h) => h.id === hotspotId);
      if (!hotspot) {
        throw new Error("Hotspot not found");
      }

      let response;
      if (hotspot.has_video_mapping && hotspot.video_mapping) {
        const updatePayload: MappingUpdateRequest = {
          video_url: mappingData.video_url,
          title: mappingData.title,
          description: mappingData.description,
          caption_url: mappingData.caption_url,
          ...(mappingData.is_enabled !== undefined && {
            is_enabled: mappingData.is_enabled ? 1 : 0,
          }),
          ...(mappingData.is_caption_enabled !== undefined && {
            is_caption_enabled: mappingData.is_caption_enabled ? 1 : 0,
          }),
        };
        response = await updateMappingMutation.mutateAsync({
          id: hotspot.video_mapping.id,
          data: updatePayload,
        });
      } else {
        const mappingPayload: MappingCreateRequest = {
          element_id: hotspot.element_id,
          screen_id: screen.id,
          hotspot_id: hotspot.id,
          video_url: mappingData.video_url || "",
          is_enabled: mappingData.is_enabled ? 1 : 0,
          title: mappingData.title,
          description: mappingData.description,
          caption_url: mappingData.caption_url,
          is_caption_enabled: mappingData.is_caption_enabled ? 1 : 0,
        };
        console.log("🔄 Creating mapping with payload:", mappingPayload);
        response = await createMappingMutation.mutateAsync(mappingPayload);
      }

      const updatedHotspots = hotspots.map((h) => {
        if (h.id === hotspotId) {
          const existingMapping = h.video_mapping;
          return {
            ...h,
            has_video_mapping: true,
            video_mapping: {
              id: response.data.id,
              video_url: mappingData.video_url || existingMapping?.video_url || "",
              is_enabled: mappingData.is_enabled !== undefined ? mappingData.is_enabled : (existingMapping?.is_enabled || true),
              title: mappingData.title !== undefined ? mappingData.title : existingMapping?.title,
              description: mappingData.description !== undefined ? mappingData.description : existingMapping?.description,
              caption_url: mappingData.caption_url !== undefined ? mappingData.caption_url : existingMapping?.caption_url,
              is_caption_enabled: mappingData.is_caption_enabled !== undefined ? mappingData.is_caption_enabled : (existingMapping?.is_caption_enabled !== undefined ? existingMapping?.is_caption_enabled : true),
            },
          };
        }
        return h;
      }) as Hotspot[];

      setHotspots(updatedHotspots);
      showNotification("Video mapping saved successfully!", "success");
      await refetch();
    } catch (err: any) {
      throw new Error((err as Error)?.message || "Failed to save mapping");
    }
  };

  const selectedHotspot = hotspots.find((h) => h.id === selectedHotspotId);
  const configurableHotspots = hotspots.filter((h) => h.element_id);

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (queryError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {(queryError as Error)?.message ||
            "Failed to load screen. Please try again."}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          backgroundColor: "white",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: "bold",
              color: "#0d1b2a",
              fontSize: "24px",
            }}
          >
            Video-Mappings — {screen?.name}
          </Typography>

          {/* Progress indicator */}
          {configurableHotspots.length > 0 && (
            <Typography
              variant="caption"
              sx={{
                color: "#666",
                backgroundColor: "#f5f5f5",
                px: 1,
                py: 0.5,
                borderRadius: 1,
              }}
            >
              {configurableHotspots.filter((h) => h.has_video_mapping).length}{" "}
              of {configurableHotspots.length} mapped
            </Typography>
          )}
        </Box>
        <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
          <Link
            component="button"
            variant="body2"
            onClick={() => navigate(`/${routePaths.adminVideoTooltipScreens}`)}
            sx={{
              color: "#415a77",
              textDecoration: "none",
              display: "inline-flex",
              alignItems: "center",
              "&:hover": {
                textDecoration: "underline",
              },
            }}
          >
            <ArrowBackIcon sx={{ fontSize: 16, mr: 1 }} />
            Back to Screens
          </Link>
        </Box>
      </Paper>

      {/* Content */}
      <Box sx={{ flex: 1, py: 3 }}>
        {configurableHotspots.length === 0 ? (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              height: "400px",
              textAlign: "center",
            }}
          >
            <Typography
              variant="h6"
              sx={{
                color: "#778da9",
                mb: 3,
              }}
            >
              {hotspots.length === 0
                ? "No hotspots defined for this screen."
                : "No hotspots are ready for video mapping."}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "#999",
                mb: 3,
              }}
            >
              {hotspots.length === 0
                ? "Create some hotspots first, then return here to assign videos."
                : "Hotspots need Element IDs before they can be mapped to videos."}
            </Typography>
            <Button
              variant="contained"
              startIcon={<VisibilityIcon />}
              onClick={() =>
                navigate(getAdminVideoTooltipScreenHotspotsRoute(id as string))
              }
              sx={{
                backgroundColor: "#415a77",
                "&:hover": {
                  backgroundColor: "#1b263b",
                },
              }}
            >
              Open Hotspots Editor
            </Button>
          </Box>
        ) : (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <MappingCanvas
                  screen={screen}
                  hotspots={hotspots}
                  selectedHotspotId={selectedHotspotId}
                  onHotspotSelect={handleHotspotSelect}
                />

                {/* Unassigned Hotspots */}
                <UnassignedHotspots
                  hotspots={hotspots}
                  selectedHotspotId={selectedHotspotId}
                  onHotspotSelect={handleHotspotSelect}
                  disabled={
                    loading ||
                    createMappingMutation.isLoading ||
                    updateMappingMutation.isLoading
                  }
                />
              </Box>
            </Grid>

            {/* Right Column - Mapping Panel */}
            <Grid item xs={12} md={4}>
              <MappingPanel
                selectedHotspot={selectedHotspot}
                onMappingUpdate={handleMappingUpdate}
                disabled={
                  loading ||
                  createMappingMutation.isLoading ||
                  updateMappingMutation.isLoading
                }
              />
            </Grid>
          </Grid>
        )}
      </Box>
    </Box>
  );
};

export default MappingsPage;

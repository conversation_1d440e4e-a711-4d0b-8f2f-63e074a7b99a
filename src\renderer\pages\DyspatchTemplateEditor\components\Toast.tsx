import { useEffect } from 'react';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import styles from './Toast.module.scss';

interface ToastProps {
  message: string;
  visible: boolean;
  onClose: () => void;
  duration?: number;
}

export function Toast({ message, visible, onClose, duration = 3000 }: ToastProps) {
  useEffect(() => {
    if (visible) {
      const timer = setTimeout(onClose, duration);
      return () => clearTimeout(timer);
    }
  }, [visible, duration, onClose]);

  if (!visible) return null;

  return (
    <div className={styles.toast}>
      <CheckCircleIcon sx={{ fontSize: 16 }} />
      <span>{message}</span>
    </div>
  );
}

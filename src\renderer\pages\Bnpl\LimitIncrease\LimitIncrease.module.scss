.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 35px;
    max-height: 700px;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }


    table {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 35px;
        border-collapse: collapse;
        border-spacing: 0;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }


        thead {
            tr {



                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 6px 12px;
                    color: #fff;
                    height: 35px;
                    position: sticky;
                    top: 0;
                     background: #676f7c;
                    color: #fff;

                }

                td {
                    line-height: 2.5;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;



                }
            }
        }

        tbody {
            background-color: #fff;
            tr {
                margin: 0;


                &:nth-child(even) {
                    background-color: #f2f2f2;
                }

                td {
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 6px 12px;
                    height: 42px;

                    .approvalBtn {
                        display: inline-block;
                        text-align: center;
                        padding: 6px 11px;
                        border: 1px solid transparent;
                        border-radius: 0.25rem;
                        background-color: #343a40;
                        font-size: 15px;
                        color: #fff;
                        cursor: pointer;
                    }

                    .rejectBtn {
                        display: inline-block;
                        text-align: center;
                        padding: 6px 23px;
                        border: 1px solid transparent;
                        border-radius: 0.25rem;
                        background-color: #343a40;
                        font-size: 15px;
                        color: #fff;
                        cursor: pointer;

                    }

                }
            }
        }
    }
}

.refreshCreditLimitHandler {
    display: inline-block;
    text-align: center;
    padding: 6px 17px;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    background-color: #343a40;
    font-size: 15px;
    color: #fff;
    cursor: pointer;
}

.refreshPop {
    padding: 15px;

    table{

        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 35px;
        border-collapse: collapse;
        border-spacing: 0;
        tbody{
            padding: 8px;
            tr{
                &:nth-child(even) {
                    background-color: #f2f2f2;
                    height: 34px;
                    border-collapse: collapse;
                }
                td{
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 6px 12px;
                    height: 42px;
                    @media screen and (max-width: 768px) and (min-width: 320px) {
                        font-size: 10px;
               
                   }
                }
            }
        }
    }

    .crossBtn {
        border: 1px solid #000;
        background: none;
        position: absolute;
        top: 14px;
        right: 15px;
        font-size: 14px;
        border-radius: 50%;
        padding-bottom: 3px;
        padding-right: 7px;
        cursor: pointer;

        &:hover {
            border: 1px solid red;
            color: red;
        }
    }

    .okBtn {
        width: 100%;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;

    }

}

.loaderCenter {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 100px;
}

.successfullyUpdatedpopup{
    h2{
        display: none;
    }

.successfullyUpdated {
    padding: 20px;
    text-align: center;
    width: 300px;
    @media screen and (max-width: 768px) and (min-width: 320px) {
        width: 240px;
    }

    .successfullytext {
        text-align: center;
        font-size: 20px;
        margin-bottom: 24px;
        color: var(--primaryColor);
    }

    .okBtn {
        width: 100%;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;
    }


}
}

.searchBox {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;

    .showdropdwn {
        width: 82px;
        height: 38px;
        padding: 4px;
    }

}

.PaginationNumber {
    ul {
        list-style: none;
        display: flex;
        padding: 0px;


        li {
            position: relative;
            display: block;
            padding:10px;
            margin-left: -1px;
            line-height: 1.25;
            color: var(--primaryColor);
            background-color: #fff;
            border: 1px solid #dee2e6;
            cursor: pointer;


            &:active{
               
                color: #fff;
                background-color: var(--primaryColor);
                border-color: var(--primaryColor);
            }

        }

    }


}
.loaderImg {

    text-align: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 200px;
  
  }
.dashboardMain {
    display: flex;
    height: 100%;
    width: 100%;
    background-color: #fff;

    .sideNavMain.sideNavMain{
       z-index: 99 !important;
     }

    .LeftCol {
        width: 100%;
        min-width: 362px;
        // max-width: 15%;
        background-color: #393E47;
        height: 100%;
        z-index: 99;
        display: flex;
        flex-direction: column;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 100%;
            min-width: 320px;
        }

        .bryzosLogo {
            padding: 20px 20px 0px 20px;
            display: block;
            display: flex;
            justify-content: space-between;
            svg{
                margin-bottom: 20px;
            }

            .logoNew{
                width: 190px;
                height: auto;
                margin-bottom: 20px;
            }
            .crossBtn {
                // border: 1px solid #fff;
                background: none;
                position: absolute;
                top: 13px;
                right: 19px;
                font-size: 17px;
                border-radius: 50%;
                padding-bottom: 1px;
                cursor: pointer;
                color: #fff;
                font-weight: 500;

                &:hover {
                    border: 1px solid red;
                    color: red;
                }
            }


            .extendedWidget {
                color: #fff;
                font-size: 18px;
                padding-top: 5px;
                span{
                    font-size: 16px;
                    display: inline-block;
                    margin-top: 3px;
                }
            }
        }

        .bryzosLogos {
            padding: 20px;
            height: calc(100% - 220px);
        }

        .logoutBtn {
            padding: 20px;

            button {
                height: 40px;
                font-size: 16px;
                line-height: 1.6;
                color: #fff;
                transition: all 0.1s;
                background-color: var(--primaryColor);
                border: 1px solid var(--white);
                border-radius: 4px;
                cursor: pointer;
                width: 260px;

                &:hover {
                    background-color: #333;
                    border: 1px solid #fff;
                }
            }

        }
    }



    .MainContent {
        flex-grow: 1;
        height: 100vh;
        padding: 20px;
        background-color: #f8f9fc;
    }

}

.dashhead {
    display: flex;
    margin-bottom: 20px;
    gap: 10px;

    .sidebarBtn {
        border: 1px solid #000;
        border-radius: 4px;
        padding: 8px 8px 6px 8px;
        cursor: pointer;


        svg {
            height: 20px;
            width: 20px;
        }
    }

    .prosuctionLiveText {
        border: 5px solid gray;
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        text-transform: uppercase;
        font-weight: bold;
        font-size: 16px;
        color: #ff0000;

    }
}
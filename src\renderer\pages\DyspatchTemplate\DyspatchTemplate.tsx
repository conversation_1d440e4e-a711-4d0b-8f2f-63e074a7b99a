import { useImmer } from "use-immer";
import useGetDyspatchTemplate from "../../hooks/useGetDyspatchTemplate";
import { useEffect, useState } from "react";
import { Select, MenuItem } from "@mui/material";
import { filterArrray, format2DecimalPlaces } from "../../utils/helper";
import styles from "./DyspatchTemplate.module.scss";
import ConfiramtionBox from "../../components/common/ConfiramtionBox";
import useRefreshDispatchTemplate from "../../hooks/useRefreshDispatchTemplate";
import ReactPaginate from "react-paginate";
import SucessErrorPopup from "../../components/common/SucessErrorPopup";
import Loader from "../../components/common/Loader";
import SearchBar from "../../components/common/SearchBox/SearchBox";

const DyspatchTemplate = () => {
  const [filteredDyspatchTemplate, setFilteredDyspatchTemplate] = useImmer<any>(
    []
  );
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [selectedTemplateId, setSelectedTemplateId] = useImmer<string | null>(
    null
  );
  const [perPageEntriesOptions] = useImmer([10, 25, 50, 100]);
  const [inputSearchValue, setInputSearchValue] = useImmer("");
  const [itemOffset, setItemOffset] = useImmer(0);
  const [itemsPerPage, setItemsPerPage] = useImmer(10);
  const [currentPage, setCurrentPage] = useImmer(0);
  const [sucessErrorPopupMessage, setSucessErrorPopupMessage] = useImmer("");

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredDyspatchTemplate.length / itemsPerPage);

  const { data: dyspatchTemplateData, isLoading: isDyspatchTemplateLoading } =
    useGetDyspatchTemplate();
  const {
    mutate: refreshDispatchTemplate,
    isLoading: isRefreshDispatchTemplateLoading,
    data: refreshDispatchTemplateData,
    error: refreshDispatchTemplateError,
  } = useRefreshDispatchTemplate();

  useEffect(() => {
    if(dyspatchTemplateData?.length > 0 && inputSearchValue.length !== 0 && !isDyspatchTemplateLoading){
      search(inputSearchValue)
    } else if (dyspatchTemplateData && !isDyspatchTemplateLoading) {
      setFilteredDyspatchTemplate(dyspatchTemplateData);
    }
  }, [dyspatchTemplateData, isDyspatchTemplateLoading, inputSearchValue]);

  useEffect(() => {
    if (refreshDispatchTemplateData && !isRefreshDispatchTemplateLoading) {
      if (!refreshDispatchTemplateError) {
        setSucessErrorPopupMessage(refreshDispatchTemplateData);
      }
    }
  }, [
    refreshDispatchTemplateData,
    isRefreshDispatchTemplateLoading,
    refreshDispatchTemplateError,
  ]);

  const search = (searchValue: string) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(searchValue);
    if (searchValue) {
      const _filterArrray = filterArrray(
        dyspatchTemplateData,
        searchValue.trim(),
        ["template_id", "subject", "time_stamp", "event"]
      );
      if (_filterArrray?.length) {
        setFilteredDyspatchTemplate(_filterArrray);
      } else {
        setFilteredDyspatchTemplate([]);
      }
    } else {
      setFilteredDyspatchTemplate(dyspatchTemplateData);
    }
  };

  const refresh = (templateId: string) => {
    setShowConfirmationPopup(true);
    setSelectedTemplateId(templateId);
  };

  const confirmationYes = () => {
    if (selectedTemplateId) {
      refreshDispatchTemplate({
        data: {
          template_id: selectedTemplateId,
        },
      });
    }

    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setSelectedTemplateId(null);
  };

  const handlePageClick = (event: any) => {
    const newOffset =
      (event.selected * itemsPerPage) % filteredDyspatchTemplate.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  return (
    <div className="contentMain">
      {isDyspatchTemplateLoading || isRefreshDispatchTemplateLoading ? (

        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>
            <SearchBar
              value={inputSearchValue}
              placeholder={'Search'}
              onChange={(event)=>search(event.target.value)}
              onClear={() => setInputSearchValue('')}
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>Template Id</th>
                  <th>Subject</th>
                  <th>Event</th>
                  <th>Time Stamp</th>
                  {/* <th colSpan={2}></th> */}
                </tr>
              </thead>
              <tbody>
                {filteredDyspatchTemplate?.length ? (
                  filteredDyspatchTemplate
                    .slice(itemOffset, endOffset)
                    .map((data: any, index: number) => (
                      <tr key={data.id}>
                        <td>{data.template_id}</td>
                        <td>{data.subject}</td>
                        <td>{data.event}</td>
                        <td>{data.time_stamp}</td>
                        {/* <td>
                          <button
                            className={styles.approvalBtn}
                            onClick={() => refresh(data.template_id)}
                          >
                            Refresh
                          </button>
                        </td> */}
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={5} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
        </>
      )}

      <ConfiramtionBox
        openConfirmationPopup={showConfirmationPopup}
        confirmationYes={confirmationYes}
        confirmationNo={confirmationPopupClose}
      />
      <SucessErrorPopup
        open={!!sucessErrorPopupMessage}
        messageText={sucessErrorPopupMessage}
        onPopupClose={() => {
          setSucessErrorPopupMessage("");
        }}
      />
    </div>
  );
};

export default DyspatchTemplate;

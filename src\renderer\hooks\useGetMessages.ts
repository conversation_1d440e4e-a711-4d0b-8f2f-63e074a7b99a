import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

const useGetMessages = () => {
  return useMutation(
    async (referenceId: string) => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_CHAT_SERVICE}/admin/get-messages/${referenceId}`
        );
        if (response.data?.data) {
          if (
            typeof response.data.data === "object" &&
            "error_message" in response.data.data
          ) {
            throw new Error(response.data.data.error_message);
          } else {
            return response.data.data;
          }
        } else {
          return null;
        }
      } catch (error: any) {
        throw new Error(error?.message);
      }
    }
  );
};

export default useGetMessages;

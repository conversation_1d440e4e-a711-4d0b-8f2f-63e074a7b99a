import { yupResolver } from "@hookform/resolvers/yup";
import { useFieldArray, useForm } from "react-hook-form";
import * as yup from "yup";
import useGetCustomDepositHistory from "../../../hooks/useGetCustomDepositHistory";
import { Select, MenuItem } from "@mui/material";
import { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useImmer } from "use-immer";
import { filterArrray } from "../../../utils/helper";
import usePostCustomDepositSetting from "../../../hooks/usePostCustomDepositSetting";
import Loader from "../../../components/common/Loader";
import { cloneDeep } from "lodash";
import styles from "./CustomSetting.module.scss";
import MatPopup from "../../../components/common/MatPopup";
import useDialogStore from "../../../components/common/DialogPopup/DialogStore";
import { confirmationPopupKeys} from "../../../utils/constant";
import SearchBar from "../../../components/common/SearchBox/SearchBox";

const CustomSetting = () => {
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const [disableSubmitBtn, setDisableSubmitBtn] = useState(false);
  const [editedData, setEditedDatae] = useImmer<{
    userId: any;
    deposit: any;
  }>({ userId: null, deposit: null });

  const [formatedCustomDepositHistory, setFormatedCustomDepositHistory] =
    useImmer<any>([]);
  const [filteredCustomDepositHistory, setFilteredCustomDepositHistory] =
    useImmer<any>([]);

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(
    filteredCustomDepositHistory.length / itemsPerPage
  );

  const { showCommonDialog, resetDialogStore } = useDialogStore();

  const {
    data: customDepositHistoryData,
    isLoading: isCustomDepositHistoryDataLoading,
    isFetching: isCustomDepositHistoryDataFetching,
  } = useGetCustomDepositHistory();

  const {
    mutate: saveCustomDeposit,
    isLoading: isSaveCustomDepositLoading,
    data: saveCustomDepositData,
  } = usePostCustomDepositSetting();

  useEffect(() => {
    if (isCustomDepositHistoryDataFetching) {
      return;
    }

    if (customDepositHistoryData) {
      const arr = customDepositHistoryData.map((obj: any) => ({
        ...obj,
        editMode: false,
      }));
      if(arr?.length > 0 && inputSearchValue.length !== 0){
        search(inputSearchValue, arr)
      }else{
        setFilteredCustomDepositHistory(arr);
        setFormatedCustomDepositHistory(arr);
      }
    } else {
      setFilteredCustomDepositHistory([]);
    }
  }, [customDepositHistoryData, isCustomDepositHistoryDataFetching, inputSearchValue]);

  useEffect(()=>{
    if(formatedCustomDepositHistory?.length > 0 && inputSearchValue.length !== 0){
      search(inputSearchValue)
    }
  },[formatedCustomDepositHistory])

  const sendDataToServer = (userId: any) => {
    saveCustomDeposit({
      data: {
        user_id: userId,
        deposit_percentage: editedData.deposit,
      },
    });
  };

  const search = (searchValue: string, searchData: any = formatedCustomDepositHistory) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(searchValue);
    if (searchValue) {
      const _filterArrray = filterArrray(
        searchData,
        searchValue.trim(),
        [
          "user_id",
          "company_name",
          "user_name",
          "deposit_percentage",
          "created_date",
        ]
      );
      if (_filterArrray?.length) {
        setFilteredCustomDepositHistory(_filterArrray.slice(0, itemsPerPage));
      } else {
        setFilteredCustomDepositHistory([]);
      }
    } else {
      setFilteredCustomDepositHistory(
        searchData ? searchData : []
      );
    }
  };

  const editModeHandler = (history: any, editState: boolean) => {
    if (history) {
      if (editState) {
        setDisableSubmitBtn(!history.deposit_percentage)
        setEditedDatae((prev) => ({
          userId: history.id,
          deposit: history.deposit_percentage,
        }));
      } else {
        setEditedDatae({ userId: null, deposit: null });
      }

      setFilteredCustomDepositHistory((prev: any) => {
        const data = cloneDeep(prev);
        data.forEach((obj: any) => {
          if (obj.id === history.id) {
            obj.editMode = editState;
          } else {
            obj.editMode = false;
          }
        });
        return data;
      });
    }
  };

  const handlePageClick = (event: any) => {
    const newOffset =
      (event.selected * itemsPerPage) % filteredCustomDepositHistory.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };


  const showCustomDepositePopup = (user_id: any) => {
    showCommonDialog(null, confirmationPopupKeys.confirmationContent, null, resetDialogStore, 
      [{name: confirmationPopupKeys.confirmation.yes, action: () =>{handleCustomDepositePopup(user_id)}},{name: confirmationPopupKeys.confirmation.no, action: resetDialogStore}])
   }

  const handleCustomDepositePopup = (user_id: any) => {
    sendDataToServer(user_id);
    editModeHandler(history, false);
    resetDialogStore()
  };


  return (
    <div className="contentMain">
      {isCustomDepositHistoryDataLoading || isCustomDepositHistoryDataFetching || isSaveCustomDepositLoading ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <div>
          <p className={styles.globalDeposittext}>Custom Deposit Setting</p>
          <div>
            <div className={styles.searchBox}>
              <Select
                className={styles.showdropdwn}
                value={itemsPerPage}
                onChange={(event) => {
                  setItemsPerPage(+event.target.value);
                }}
              >
                {perPageEntriesOptions.map((item, index) => (
                  <MenuItem key={index} value={item}>
                    <span>{item}</span>
                  </MenuItem>
                ))}
              </Select>

              <SearchBar
                  value={inputSearchValue}
                  placeholder={'Search'}
                  onChange={(event)=>search(event.target.value)}
                  onClear={() => setInputSearchValue('')}
              />
            </div>
              <div className={styles.tblscroll}>
                <table>
                  <thead>
                    <tr>
                      <th>User Id</th>
                      <th>Company Name</th>
                      <th>User Name</th>
                      <th>Deposit Percentage</th>
                      <th>Created Date</th>
                      <th></th>
                      <th></th>
                    </tr>
                  </thead>

                  <tbody>
                    { filteredCustomDepositHistory?.length ? (
                    filteredCustomDepositHistory
                      ?.slice(itemOffset, endOffset)
                      .map(
                        (history: any) =>
                          history.is_active && (
                            <tr key={history.id}>
                              {history.editMode ? (
                                <>
                                  <td>{history.user_id}</td>
                                  <td>{history.company_name}</td>
                                  <td>{history.user_name}</td>
                                  <td>
                                    <input
                                      className={styles.dateInput}
                                      defaultValue={history.deposit_percentage}
                                      onChange={(e) =>{
                                        setEditedDatae((prev) => ({
                                          ...prev,
                                          deposit: e?.target.value,
                                        }))
                                        setDisableSubmitBtn(e?.target.value.length === 0)
                                      }}
                                      type="tel"
                                    />
                                  </td>
                                  <td>{history.created_date}</td>
                                  <td className={styles.w150}>
                                    <button
                                      className={styles.editBtn}
                                      onClick={() => {
                                        editModeHandler(history, false);
                                      }}
                                    >
                                      Cancel Edit
                                    </button>
                                  </td>
                                  <td className={styles.w150}>
                                    <button
                                      className={styles.editBtn}
                                      disabled={disableSubmitBtn}
                                      onClick={() => {
                                        showCustomDepositePopup(history.user_id);
                                      }}
                                    >
                                      Submit
                                    </button>
                                  </td>
                                </>
                              ) : (
                                <>
                                  <td>{history.user_id}</td>
                                  <td>{history.company_name}</td>
                                  <td>{history.user_name}</td>
                                  <td>{history.deposit_percentage}</td>
                                  <td>{history.created_date}</td>
                                  <td className={styles.w150}>
                                    <button
                                      className={styles.editBtn}
                                      onClick={() => {
                                        editModeHandler(history, true);
                                      }}
                                    >
                                      Edit
                                    </button>
                                  </td>
                                  <td className={styles.w150}></td>
                                </>
                              )}
                            </tr>
                          )
                      )
                    ) : (
                      <tr>
                        <td colSpan={7} className={"noDataFoundTd"}>No data found</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            <div className={"PaginationNumber"}>
              <ReactPaginate
                breakLabel="..."
                nextLabel=">"
                onPageChange={handlePageClick}
                pageRangeDisplayed={5}
                pageCount={pageCount}
                previousLabel="<"
                renderOnZeroPageCount={(props) =>
                  props.pageCount > 0 ? undefined : null
                }
                forcePage={pageCount > 0 ? currentPage : -1}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomSetting;

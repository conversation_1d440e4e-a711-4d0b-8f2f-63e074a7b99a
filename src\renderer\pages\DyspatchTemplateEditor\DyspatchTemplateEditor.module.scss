// Fix for Monaco editor aria container causing body overflow
:global {
  body:has(.app) {
    overflow: hidden !important;
  }

  #root:has(.app) {
    overflow: hidden !important;
    height: 100vh;
  }

  .monaco-aria-container {
    position: fixed !important;
    overflow: hidden !important;
  }
}

.app {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 60px);
  background: #f5f5f5;
  overflow: hidden;
  position: relative;
}

.mainContent {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.welcomeScreen {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
}

.welcomeContent {
  text-align: center;
  max-width: 600px;

  h2 {
    font-size: 28px;
    margin-bottom: 12px;
    color: #333;
  }

  p {
    font-size: 16px;
    color: #666;
    margin-bottom: 24px;
  }
}

.loaderImg {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  margin-top: 200px;
}
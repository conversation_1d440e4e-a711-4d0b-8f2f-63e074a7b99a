import { useDrag, useDrop } from 'react-dnd';
import DeleteIcon from '@mui/icons-material/Delete';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import type { BlockContent } from '../types';
import { useTemplateStore } from '../stores/templateStore';
import styles from './CanvasBlock.module.scss';

interface CanvasBlockProps {
  block: BlockContent;
  index: number;
  isSelected: boolean;
  onSelect: () => void;
  onDropBlock: (dragIndex: number, dropIndex: number) => void;
}

export function CanvasBlock({
  block,
  index,
  isSelected,
  onSelect,
  onDropBlock,
}: CanvasBlockProps) {
  const { removeBlock, updateBlock } = useTemplateStore();

  const [{ isDragging }, drag, dragPreview] = useDrag(() => ({
    type: 'canvas-block',
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }), [index]);

  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'canvas-block',
    hover: (item: { index: number }) => {
      if (item.index !== index) {
        onDropBlock(item.index, index);
        item.index = index;
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }), [index, onDropBlock]);

  const handleContentChange = (content: string) => {
    updateBlock(block.id, { content });
  };

  const renderBlockContent = () => {
    switch (block.type) {
      case 'heading1':
      case 'heading2':
      case 'heading3':
        return (
          <div
            contentEditable
            suppressContentEditableWarning
            onBlur={(e) => handleContentChange(e.currentTarget.textContent || '')}
            style={{
              ...block.styles,
              outline: 'none',
            }}
          >
            {block.content}
          </div>
        );
      case 'paragraph':
        return (
          <div
            contentEditable
            suppressContentEditableWarning
            onBlur={(e) => handleContentChange(e.currentTarget.textContent || '')}
            style={{
              ...block.styles,
              outline: 'none',
            }}
          >
            {block.content}
          </div>
        );
      case 'button':
        return (
          <div style={{ textAlign: block.styles?.textAlign || 'center' }}>
            <button
              style={{
                ...block.styles,
                cursor: 'pointer',
                border: 'none',
              }}
              onClick={(e) => e.preventDefault()}
            >
              {block.content}
            </button>
          </div>
        );
      case 'image':
        return (
          <div style={{ textAlign: block.styles?.textAlign || 'center' }}>
            <img
              src={block.content}
              alt="Email image"
              style={{
                maxWidth: '100%',
                height: 'auto',
                ...block.styles,
              }}
            />
          </div>
        );
      case 'spacer':
        return (
          <div
            style={{
              height: block.styles?.height || '20px',
              backgroundColor: 'transparent',
            }}
          />
        );
      case 'divider':
        return (
          <hr
            style={{
              border: 'none',
              borderTop: block.styles?.border || '1px solid #e0e0e0',
              margin: block.styles?.padding || '20px 0',
            }}
          />
        );
      case 'html':
        return (
          <div
            style={{
              padding: '10px',
              backgroundColor: '#f9f9f9',
              border: '1px dashed #ccc',
              borderRadius: '4px',
              position: 'relative',
            }}
          >
            <div
              style={{
                fontSize: '10px',
                color: '#666',
                marginBottom: '5px',
                fontFamily: 'monospace',
              }}
            >
              Custom HTML Block
            </div>
            <div
              dangerouslySetInnerHTML={{ __html: block.content }}
              style={{
                minHeight: '40px',
                overflow: 'auto',
              }}
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div
      ref={(node: HTMLDivElement | null) => drag(drop(node)) as any}
      className={`${styles.canvasBlock} ${isSelected ? styles.selected : ''} ${isDragging ? styles.dragging : ''} ${
        isOver ? styles.dropTarget : ''
      }`}
      onClick={onSelect}
    >
      <div className={styles.blockControls}>
        <div ref={dragPreview as any} className={styles.dragHandle}>
          <DragIndicatorIcon sx={{ fontSize: 16 }} />
        </div>
        <button
          className={styles.deleteBtn}
          onClick={(e) => {
            e.stopPropagation();
            removeBlock(block.id);
          }}
        >
          <DeleteIcon sx={{ fontSize: 14 }} />
        </button>
      </div>
      <div className={styles.blockContent}>{renderBlockContent()}</div>
    </div>
  );
}

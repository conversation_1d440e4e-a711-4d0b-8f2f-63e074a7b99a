import { Box, Typography, Button } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import ScreenCard from './ScreenCard';

const ScreensList = ({ screens, onScreensChange, onUploadClick }) => {
  if (!screens || screens.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '400px',
          textAlign: 'center',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            color: '#778da9',
            mb: 3,
          }}
        >
          No screens yet.
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onUploadClick}
          sx={{
            backgroundColor: '#415a77',
            '&:hover': {
              backgroundColor: '#1b263b',
            },
          }}
        >
          Upload Screen
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      {/* Summary Stats (if available) */}
      {screens.length > 0 && (
        <Box
          sx={{
            my: 3,
            p: 2,
            backgroundColor: 'rgba(76, 175, 80, 0.1)',
            border: '1px solid #4caf50',
            borderRadius: 1,
          }}
        >
          <Typography
            variant="h6"
            sx={{
              color: '#4caf50',
              fontWeight: 'semibold',
              mb: 0.5,
            }}
          >
            {screens.length} screen{screens.length !== 1 ? 's' : ''} configured 🎉
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: '#4caf50',
            }}
          >
            {screens.reduce((total, screen) => total + (screen.hotspots_count || 0), 0)} hotspots defined • {' '}
            {screens.reduce((total, screen) => total + (screen.mapped_videos_count || 0), 0)} videos mapped • {' '}
            {screens.reduce((total, screen) => total + (screen.pending_mappings_count || 0), 0)} tooltips pending
          </Typography>
        </Box>
      )}

      {/* Screens Grid */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
          gap: 3,
        }}
      >
        {screens.map((screen) => (
          <ScreenCard
            key={screen.id}
            screen={screen}
            onScreenChange={onScreensChange}
          />
        ))}
      </Box>
    </Box>
  );
};

export default ScreensList;

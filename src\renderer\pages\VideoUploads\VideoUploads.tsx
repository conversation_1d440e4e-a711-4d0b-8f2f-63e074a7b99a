import { useImmer } from "use-immer";
import Loader from "../../components/common/Loader";
import useGetSafeUploads from "../../hooks/useGetSafeUploads";
import { useCallback, useContext, useEffect, useRef, useState } from "react";
import ReactPaginate from "react-paginate";
import useSaveHideCarouselImage from "../../hooks/useSaveHideCarouselImage";
import { CommonCtx } from "../AppContainer";
import { Select, MenuItem, Checkbox } from "@mui/material";
import styles from "./VideoUploads.module.scss";
import MatPopup from "../../components/common/MatPopup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { VideoUploadsFromSchema, VideoUploadsFromSchemaType } from "../../models/videoUploads.model";
import useGetAllUploadedVideos from "../../hooks/useGetAllUploadedVideos";
import { useDebouncedValue } from "@mantine/hooks";
import { useDropzone } from "react-dropzone";
import clsx from "clsx";
import { ReactComponent as PlayIcon } from '../../../assests/images/playIcon.svg';
import { ReactComponent as CloseArrow } from '../../../assests/images/closePop.svg';
import { ReactComponent as VideoPlayIcon } from '../../../assests/images/VideoPlay-Icon.svg';
import AddVideo from "./components/AddVideo";
import useGetAllVideoLibraryTags from "../../hooks/useGetAllVideoLibraryTags";
import usePostSaveVideo from "../../hooks/usePostSaveVideo";
import UseImageAPIConfig from "../../hooks/UseImageAPIConfig";
import EditVideo from "./components/EditVideo";
import usePostUpdateVideo from "../../hooks/usePostUpdateVideo";
import { USE_IMAGEKIT, USE_IMGIX, convertUtcToCtTimeUsingDayjs, videoUploadData } from '@bryzos/giss-ui-library'; 
import VideoPlayer from "../../components/VideoPlayer";
import { Tooltip } from "@mui/material";
import CopyButton from "./components/CopyButton";
import SearchBar from "../../components/common/SearchBox/SearchBox";

const VideoUploads = () => {
    const {
        register,
        control,
        handleSubmit,
        reset,
        watch,
        setValue,
        getValues,
        formState: { errors },
    } = useForm<VideoUploadsFromSchemaType>({
        defaultValues: {
            title: '',
            description: '',
            caption: '',
            video_s3_url: '',
            thumbnail_s3_url:{}
        },
        resolver: yupResolver(VideoUploadsFromSchema),
        mode: "onSubmit",
    });
    const [itemOffset, setItemOffset] = useState(0);
    const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [videoUploads, setVideoUploads] = useImmer<any[]>([]);
    const [filteredSafeUploads, steFilteredSafeUploads] = useImmer<any[]>([]);
    const [inputSearchValue, setInputSearchValue] = useState("");
    const [debouncedInputSearchValue] = useDebouncedValue(inputSearchValue.trim(), 1000);
    const [showSaveBtn, setShowSaveBtn] = useState(false);
    const [openVideoUploadPopup, setOpenVideoUploadPopup] = useState(false);
    const [openVideoEditPopup, setOpenVideoEditPopup] = useState(false);
    const [showLoader, setShowLoader] = useState(false);
    const [meta, setMeta] = useImmer<any>(null);
    const [isImigix, setisImigix]  = useState(false);
    const [isImageKit, setIsImageKit]  = useState(false);
    const [editVieoData, setEditVieoData] = useState(null);
    const [tagMap, setTagMap] = useState<any>(null);
    const [uploadVideoLoaderText, setUploadVideoLoaderText] = useState(false);
    const [openVideoPopup, setOpenVideoPopup] = useState<any>(false);
    const [videoThumbnailUrl, setVideoThumbnailUrl] = useState<string>('');
    const [playVideoUrl, setPlayVideoUrl] = useState<any>(null);
    const [subtitleUrl, setSubtitleUrl] = useState<string|null>(null);
    const [targetedVideoToPlay, setTargetedVideoToPlay] = useState<any>(null);
    const videoRef = useRef(null);

    const { data:imageConfig, isLoading:imageConfigLoading, error:imageConfigError } = UseImageAPIConfig();

    const showPopupFormAnyComponent = useContext(CommonCtx);

    const {
        data: videoLibraryTagsData,
    } = useGetAllVideoLibraryTags();

    const {
        data: videoUploadsData,
        isLoading: isVideoUploadsLoading,
        isFetching: isVideoUploadsFetching,
    } = useGetAllUploadedVideos(itemsPerPage, currentPage, encodeURIComponent(debouncedInputSearchValue));
    

    
    const {
        mutate: saveVideo,
        data: saveVideoData,
        isLoading: isSaveVideoLoading,
    }  = usePostSaveVideo();

    const {
        mutate: updateVideo,
        data: updateVideoData,
        isLoading: isUpdateVideoLoading,
    }  = usePostUpdateVideo();

    const getTagDisplayTitle =(tag:string)=>{
        if(tagMap && tagMap[tag]){
            return tagMap[tag].display_title;
        }
        return "";
    }

    useEffect(()=>{
        if(videoLibraryTagsData && !tagMap){
            const queryParamMapping = videoLibraryTagsData.reduce((acc:any, item:any) => {
                acc[item.query_param] = item;
                return acc;
            }, {});

            setTagMap(queryParamMapping);
        }
    },[videoLibraryTagsData]);

    useEffect(()=>{
        if(imageConfig){
            const imgixObj = imageConfig.data.find((res:any) => res.config_key === USE_IMGIX);
                if (imgixObj) {
                    setisImigix(imgixObj.config_value);
                }
                const imgeKitObj = imageConfig.data.find((res:any) => res.config_key === USE_IMAGEKIT);
                if (imgeKitObj) {
                    setIsImageKit(imgeKitObj.config_value);
                }
        }
    },[imageConfig])

    useEffect(() => {
        if (videoUploadsData?.meta) {
          setMeta(videoUploadsData.meta);
        }
        const _videoUploadsData = videoUploadsData?.data?.length ? videoUploadsData.data : [];
        setVideoUploads(_videoUploadsData);
        steFilteredSafeUploads(_videoUploadsData);
    }, [videoUploadsData, isVideoUploadsFetching, isVideoUploadsLoading]);

    useEffect(() => {
        if (!isSaveVideoLoading) {
            if (saveVideoData) {
                showPopupFormAnyComponent(saveVideoData);
                setUploadVideoLoaderText(false);
            }
        }
    }, [
        saveVideoData,
        isSaveVideoLoading,
    ]);

    useEffect(() => {
        if (!isUpdateVideoLoading) {
            if (updateVideoData) {
                showPopupFormAnyComponent(updateVideoData );
                setUploadVideoLoaderText(false);
            }
        }
    }, [
        updateVideoData,
        isUpdateVideoLoading,
    ]);

    const handlePageClick = (event: any) => {
        setCurrentPage(event.selected + 1);
    };

    const getPreview = (fileUrl: string, alt_text: string)  => {
        if (fileUrl) {
            const extension = isVideo(fileUrl);
            return extension ? (
                <VideoPlayer
                    url={updateURL(fileUrl)}
                    width={"100%"}
                    height={"100%"}
                    videoRef={videoRef}
                />
            ) : (
                <img src={updateURL(fileUrl)} alt={alt_text} height="50" />
            );
        } else {
            return <></>;
        }
    };

    function isVideo(fileUrl: string) {
        const videoExtensions = [".mp4", ".mov", ".wmv", ".avi", ".flv"];

        const extension = fileUrl.substring(fileUrl.lastIndexOf(".")).toLowerCase();
        return videoExtensions.includes(extension)
            ? extension.replace(".", "")
            : null;
    }

    function updateURL(imgUrl: string): string | undefined {
        if(imgUrl){
            if(isImageKit){
                return`${import.meta.env.VITE_VIDEO_LIBRARY_IMAGEKIT_PREFIX}${imgUrl.split(".com")[1]}${import.meta.env.VITE_VIDEO_LIBRARY_IMAGEKIT_SUFFIX}`;
            }else if(isImigix){
                return`${import.meta.env.VITE_VIDEO_LIBRARY_IMGIX_PREFIX}${imgUrl.split(".com")[1]}${import.meta.env.VITE_VIDEO_LIBRARY_IMGIX_SUFFIX}`;
            }else{
                return `${import.meta.env.VITE_VIDEO_LIBRARY_CLOUD_FRONT}${imgUrl.split(".com")[1]}`;
            }
        }
    }

    const search = (searchString: string) => {
        setCurrentPage(1);
        setInputSearchValue(searchString);
    };

    const handleUploadVideoOnclick = () => {
        setOpenVideoUploadPopup(true);
    }
    const confirmationPopupClose = () => {
        setOpenVideoUploadPopup(false);
    }

    const handleEdit = (editData:any)=>{
        const separatedEditData = {...editData};
        separatedEditData.thumbnailFiles = separatedEditData.thumbnail_s3_url
        setEditVieoData(separatedEditData);
        setOpenVideoEditPopup(true);
    }

    const handleUpdateVideo = (payload:any)=>{
        setOpenVideoEditPopup(false);
        updateVideo(payload);
        setEditVieoData(null);

    }

    const handleEditClose = ()=>{
        setOpenVideoEditPopup(false);
        setEditVieoData(null);
    }

    const closeVideoPopup = () => {
        setOpenVideoPopup(false);
        setPlayVideoUrl(null);
        setTargetedVideoToPlay(null);
        setSubtitleUrl(null);
    }

    const handleClickOnThumbnail = (videoUpload: any) => {
        setTargetedVideoToPlay(videoUpload);
        setVideoThumbnailUrl(videoUpload.thumbnail_s3_url?.electron_player); 
        setOpenVideoPopup(videoUpload.thumbnail_s3_url?.electron_player); 
        setVideoThumbnailUrl(videoUpload.thumbnail_s3_url.thumbnail_app); 
        setOpenVideoPopup(true); 
        setPlayVideoUrl(videoUpload.video_s3_url);
        setSubtitleUrl((videoUpload.subtitle_s3_url)?`${import.meta.env.VITE_VIDEO_LIBRARY_CLOUD_FRONT}${videoUpload.subtitle_s3_url?.split(".com")[1]}`:null);
    } 
 
    return (
        <div>
            {isVideoUploadsLoading ||
                isVideoUploadsFetching ||
                isSaveVideoLoading ||
                isUpdateVideoLoading ||
                showLoader ||
                imageConfigLoading ? (
                <div className={styles.loaderImg}>
                    <Loader /> {uploadVideoLoaderText && <span>{videoUploadData}</span>}
                </div>
            ) : (
                <div>
                    <div className={styles.searchBox}>
                        <Select
                            className={styles.showdropdwn}
                            value={itemsPerPage}
                            onChange={(event) => {
                                setItemsPerPage(+event.target.value);
                            }}
                        >
                            {perPageEntriesOptions.map((item, index) => (
                                <MenuItem key={index} value={item}>
                                    <span>{item}</span>
                                </MenuItem>
                            ))}
                        </Select>
                        <div className={styles.searchRightSection}>
                            <button
                                className={styles.saveBtnf}
                                onClick={handleUploadVideoOnclick}
                            >
                                Upload Video
                            </button>
                            <SearchBar
                                value={inputSearchValue}
                                placeholder={"Search"}
                                onChange={(event)=>search(event.target.value)}
                                onClear={()=> {setInputSearchValue('')}}
                            />
                        </div>
                    </div>

                    <div className={styles.tblscroll}>
                        <table>
                            <thead>
                                <tr>
                                    <th>Show<br/>on UI</th>
                                    <th>Large<br/> Video</th>
                                    <th>Subtitles</th>
                                    <th>Title</th>
                                    <th className={styles.thDescription}>Description</th>
                                    <th>Caption</th>
                                    <th>Sequence</th>
                                    <th>Subtitles</th>
                                    <th>Video</th>
                                    <th>Internal Tags</th>
                                    <th>Tags</th>
                                    <th>Share Video URL</th>
                                    <th>View Counts</th>
                                    <th>Uploaded on</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {videoUploads?.length > 0 ? (
                                    videoUploads
                                        .map((videoUpload: any, i) => (
                                            <tr key={videoUpload.id}>
                                                <td>{<Checkbox checked={videoUpload.show_on_ui === 1} disabled = {true} />}</td>
                                                <td>{<Checkbox checked={videoUpload.is_large_file === 1} disabled = {true} />}</td>
                                                <td>{<Checkbox checked={videoUpload.subtitle_s3_url !== null} disabled = {true} />}</td>
                                                <td>{videoUpload.title}</td>
                                                <td className={styles.videoDescription}>{videoUpload.description}</td>
                                                <td>{videoUpload.caption}</td>
                                                <td>{videoUpload.sequence}</td>
                                                <td>{<Checkbox checked={videoUpload.subtitle_s3_url !== null} disabled = {true} />}</td>
                                                <td className={styles.videoTbl} >
                                                    {
                                                        <div className={styles.videoThumbBox}>
                                                            <img src={updateURL(videoUpload?.thumbnail_s3_url?.electron_player)} alt={videoUpload.title} />
                                                            <div className={styles.overlay} onClick={() => {handleClickOnThumbnail(videoUpload)}}>
                                                                {videoUpload.video_s3_url && <span className={styles.VideoPlayIcon}><VideoPlayIcon /></span>}
                                                            </div>
                                                        </div>
                                                    }
                                                </td>
                                                <td>{videoUpload.internal_tags}</td>
                                                <td>{getTagDisplayTitle(videoUpload.tags)}</td>
                                                <td>
                                                    {(videoUpload?.share_video_url) && (
                                                        <div className={styles.share_video_url_div}>
                                                        <Tooltip title={videoUpload.share_video_url}>
                                                            <a
                                                            href={videoUpload.share_video_url}
                                                            target="_blank"
                                                            rel="share_video_link"
                                                        >
                                                            <p>Click here</p>
                                                        </a>
                                                        </Tooltip>
                                                        <CopyButton url={videoUpload?.share_video_url}/>
                                                        </div>
                                                    )}
                                                    </td>
                                                <td className={styles.videoViewCountColumn}>{videoUpload.view_count}</td>
                                                <td>{convertUtcToCtTimeUsingDayjs(videoUpload.created_date)}</td>
                                                <td>
                                                    <button
                                                        className={styles.editBtnf}
                                                        onClick={()=>{handleEdit(videoUpload);}}
                                                    >
                                                        Edit
                                                    </button>
                                                </td>
                                            </tr>
                                        ))
                                ) : (
                                    <tr>
                                        <td colSpan={10} className={"noDataFoundTd"}>
                                            No data found
                                        </td>
                                    </tr>
                                )}
                            </tbody>

                        </table>
                    </div>
                    <div className="PaginationNumber">
                        <div className="saveBtn">
                            {meta && <ReactPaginate
                                breakLabel="..."
                                nextLabel=">"
                                onPageChange={handlePageClick}
                                pageRangeDisplayed={5}
                                pageCount={meta.totalPages}
                                previousLabel="<"
                                renderOnZeroPageCount={(props) =>
                                    props.pageCount > 0 ? undefined : null
                                }
                                forcePage={meta.currentPage > 0 ? meta.currentPage - 1 : undefined}
                            />}
                        </div>
                    </div>
                </div>
            )}
            <MatPopup
                className={styles.orderContinuePopup}
                open={openVideoUploadPopup}
                classes={{
                    paper:styles.uploadVideoPopup
                }}
            >
                <div className={styles.uploadVideoTitle}>Upload Video</div>
                <AddVideo setUploadVideoLoaderText={setUploadVideoLoaderText} confirmationPopupClose={confirmationPopupClose}  saveVideo={saveVideo} showLoader={showLoader} setShowLoader={setShowLoader} />
            </MatPopup>

            <MatPopup
                className={styles.orderContinuePopup}
                open={openVideoEditPopup}
                classes={{
                    paper:styles.uploadVideoPopup
                }}
            >
                <div className={styles.uploadVideoTitle}>Edit Video</div>
                <EditVideo setUploadVideoLoaderText={setUploadVideoLoaderText} confirmationPopupClose={handleEditClose} videoTags={tagMap} updateVideo={handleUpdateVideo} showLoader={showLoader} setShowLoader={setShowLoader} videoData= {editVieoData} updateURL={updateURL} />
            </MatPopup>

            <MatPopup
                className={styles.orderContinuePopup}
                open={!!openVideoPopup}
                classes={{
                    paper:styles.showVideoPopup
                }}
            >
                <div className={styles.closeBtn}>
                        <button onClick={closeVideoPopup}><CloseArrow/></button>
                    </div>
                    <div className={styles.uploadVideoImgPopup}>
                    {playVideoUrl ? <>
                         <VideoPlayer
                    url={updateURL(playVideoUrl)}
                    width={"100%"}
                    height={"100%"}
                    videoRef={videoRef}
                    autoPlay={true}
                    captionUrl = {subtitleUrl}
                /></> :
                <img src={updateURL(videoThumbnailUrl)} alt={targetedVideoToPlay?.title} />
                }
                    </div>
                
            </MatPopup>
        </div>
    );
};

export default VideoUploads;

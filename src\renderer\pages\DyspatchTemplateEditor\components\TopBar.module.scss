.topBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  height: 50px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.topBarLeft,
.topBarRight {
  display: flex;
  align-items: center;
  gap: 12px;
}

.backBtn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  color: #333;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.2s;

  &:hover {
    background: #f0f0f0;
  }
}

.modeToggle,
.viewToggle {
  display: flex;
  background: #f0f0f0;
  border-radius: 6px;
  padding: 2px;
}

.modeBtn,
.viewBtn {
  padding: 6px 12px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  transition: all 0.2s;

  &.active {
    background: white;
    color: #333;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.divider {
  width: 1px;
  height: 24px;
  background: #ddd;
  flex-shrink: 0;
}

.templateField {
  display: flex;
  align-items: center;
  gap: 6px;
}

.templateLabel {
  font-size: 13px;
  font-weight: 500;
  color: #666;
  white-space: nowrap;
}

.templateName {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.templateNameInput {
  width: 180px;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
}

.subjectField {
  display: flex;
  align-items: center;
  gap: 6px;
}

.subjectLabel {
  font-size: 13px;
  font-weight: 500;
  color: #666;
  white-space: nowrap;
}

.subjectInput {
  width: 200px;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
}

.iconBtn {
  padding: 8px;
  background: transparent;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;

  &:hover {
    background: #f0f0f0;
  }
}

.saveBtn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background: #218838;
  }

  &:disabled {
    background: #e0e0e0;
    color: #999;
    cursor: not-allowed;
  }
}

.duplicateBtn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;

  &:hover {
    background: #5a6268;
  }
}

.sendTestBtn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background: #138496;
  }

  &:disabled {
    background: #e0e0e0;
    color: #999;
    cursor: not-allowed;
  }
}

.htmlErrorBadge {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  background: #fff3f3;
  color: #d32f2f;
  border: 1px solid #ffcdd2;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  animation: errorPulse 2s ease-in-out infinite;
}

@keyframes errorPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.exportBtn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;

  &:hover {
    background: #0056b3;
  }
}

.loaderOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
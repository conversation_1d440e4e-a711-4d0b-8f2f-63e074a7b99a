import { Select, MenuItem } from "@mui/material";
import { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useImmer } from "use-immer";
import Loader from "../../../components/common/Loader";
import MatPopup from "../../../components/common/MatPopup";
import useApproveRejectAchCreditPayment from "../../../hooks/useApproveRejectAchCreditPayment";
import useGetAchCreditPayment from "../../../hooks/useGetAchCreditPayment";
import { filterArrray } from "../../../utils/helper";
import styles from "./AchCreditPayment.module.scss";
import SearchBar from "../../../components/common/SearchBox/SearchBox";

const AchCreditPayment = () => {
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [modifiedAchCreditPayment, setModifiedAchCreditPayment] = useImmer([]);
  const [filteredAchCreditPayments, setFilteredAchCreditPayments] = useImmer(
    []
  );
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [selectedPaymentData, setSelectedPaymentData] = useImmer<{
    isApproved: 0 | 1 | null;
    achPayment: any;
  }>({ isApproved: null, achPayment: null });

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredAchCreditPayments.length / itemsPerPage);

  const { data: achCreditPaymentData, isLoading: isAchCreditPaymentLoading } =
    useGetAchCreditPayment();
  const {
    mutate: approveRejectAchCreditOrder,
    isLoading: isApproveRejectAchCreditPaymentLoading,
    data: approveRejectAchCreditPaymentData,
    error: approveRejectAchCreditPaymentError,
  } = useApproveRejectAchCreditPayment();

  useEffect(() => {
    if (achCreditPaymentData) {
      const _achCreditPaymentData = achCreditPaymentData.map(
        (achPayment: any) => {
          let status = "";

          if (achPayment.is_active === 1) {
            if (achPayment.is_approved === null) {
              status = "Pending";
            } else if (achPayment.is_approved === 0) {
              status = "Rejected";
            } else if (achPayment.is_approved === 1) {
              status = "Approved";
            }
          } else if (
            achPayment.is_active === 0 &&
            achPayment.is_approved === 0
          ) {
            status = "Rejected";
          }
          return { ...achPayment, status };
        }
      );
      setModifiedAchCreditPayment(
        _achCreditPaymentData ? _achCreditPaymentData : []
      );
      setFilteredAchCreditPayments(
        _achCreditPaymentData ? _achCreditPaymentData : []
      );
    } else {
      setModifiedAchCreditPayment([]);
      setFilteredAchCreditPayments([]);
    }
  }, [achCreditPaymentData]);

  useEffect(()=>{
    if(modifiedAchCreditPayment?.length > 0){
      search(inputSearchValue)
    }
  },[modifiedAchCreditPayment, inputSearchValue])

  useEffect(() => {
    if (
      approveRejectAchCreditPaymentData &&
      !isApproveRejectAchCreditPaymentLoading
    ) {
      if (!approveRejectAchCreditPaymentError) {
        setApiResponseMessage(approveRejectAchCreditPaymentData);
      }
    }
  }, [
    approveRejectAchCreditPaymentData,
    isApproveRejectAchCreditPaymentLoading,
    approveRejectAchCreditPaymentError,
  ]);

  useEffect(() => {
    setCurrentPage(0);
    setItemOffset(0);
  }, [itemsPerPage]);

  const search = (searchValue: string) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(searchValue);
    if (searchValue) {
      const _filterArrray = filterArrray(
        modifiedAchCreditPayment,
        searchValue.trim(),
        [
          "account_number",
          "bank_name",
          "email_id",
          "full_name",
          "routing_number",
          "status",
        ]
      );
      if (_filterArrray?.length) {
        setFilteredAchCreditPayments(_filterArrray);
      } else {
        setFilteredAchCreditPayments([]);
      }
    } else {
      setFilteredAchCreditPayments(modifiedAchCreditPayment);
    }
  };

  const onClickApproveRejectHandler = (isApproved: 0 | 1, achPayment: any) => {
    setShowConfirmationPopup(true);
    setSelectedPaymentData({ isApproved, achPayment });
  };

  const confirmationPopupYes = () => {
    if (selectedPaymentData) {
      approveRejectAchCreditOrder({
        data: {
          id: selectedPaymentData.achPayment.id,
          user_id: selectedPaymentData.achPayment.user_id,
          is_approved: selectedPaymentData.isApproved,
        },
      });
    }

    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setSelectedPaymentData({ isApproved: null, achPayment: null });
  };

  const handlePageClick = (event: any) => {
    const newOffset =
      (event.selected * itemsPerPage) % filteredAchCreditPayments.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  return (
    <div className="contentMain">
      {isAchCreditPaymentLoading || isApproveRejectAchCreditPaymentLoading ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>

            <SearchBar 
              placeholder={"Search"} 
              value={inputSearchValue} 
              onChange={(event)=>search(event.target.value)}              
              onClear={()=>{setInputSearchValue('')}}            
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>Account Number</th>
                  <th>Bank Name</th>
                  <th>Email Id</th>
                  <th>Full Name</th>
                  <th>Routing Number</th>
                  <th>Status</th>
                  <th colSpan={2}></th>
                </tr>
              </thead>
              <tbody>
                {filteredAchCreditPayments?.length ? (
                  filteredAchCreditPayments
                    .slice(itemOffset, endOffset)
                    .map((achPayment: any) => (
                    <tr key={achPayment.user_id}>
                      <td>{achPayment.account_number}</td>
                      <td>{achPayment.bank_name}</td>
                      <td>{achPayment.email_id}</td>
                      <td>{achPayment.full_name}</td>
                      <td>{achPayment.routing_number}</td>
                      <td>{achPayment.status}</td>
                      <td>
                        {achPayment.is_approved === null && (
                          <button
                            className={styles.approvalBtn}
                            onClick={() =>
                              onClickApproveRejectHandler(1, achPayment)
                            }
                          >
                            Approve
                          </button>
                        )}
                      </td>
                      <td>
                        {achPayment.is_approved === null && (
                          <button
                            className={styles.rejectBtn}
                            onClick={() =>
                              onClickApproveRejectHandler(0, achPayment)
                            }
                          >
                            Reject
                          </button>
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={8} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
        </div>
      )}
      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>Do you want to continue ?</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
    </div>
  );
};

export default AchCreditPayment;

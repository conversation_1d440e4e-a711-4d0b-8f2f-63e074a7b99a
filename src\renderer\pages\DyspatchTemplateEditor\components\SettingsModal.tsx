import CloseIcon from '@mui/icons-material/Close';
import { useTemplateStore } from '../stores/templateStore';
import styles from './SettingsModal.module.scss';

interface SettingsModalProps {
  onClose: () => void;
}

export function SettingsModal({ onClose }: SettingsModalProps) {
  const { settings, setSettings } = useTemplateStore();

  const handleChange = (key: keyof typeof settings, value: any) => {
    setSettings({ [key]: value });
  };

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h2>Template Settings</h2>
          <button className={styles.closeBtn} onClick={onClose}>
            <CloseIcon sx={{ fontSize: 20 }} />
          </button>
        </div>

        <div className={styles.modalContent}>
          <div className={styles.settingsForm}>
            <div className={styles.formGroup}>
              <label>Template Name</label>
              <input
                type="text"
                value={settings.templateName}
                onChange={(e) => handleChange('templateName', e.target.value)}
                placeholder="Enter template name"
              />
            </div>

            <div className={styles.formGroup}>
              <label>Subject Line</label>
              <input
                type="text"
                value={settings.subjectLine}
                onChange={(e) => handleChange('subjectLine', e.target.value)}
                placeholder="Enter email subject"
              />
            </div>

            <div className={styles.formGroup}>
              <label>Email Background Color</label>
              <div className={styles.colorInput}>
                <input
                  type="color"
                  value={settings.emailBackgroundColor}
                  onChange={(e) => handleChange('emailBackgroundColor', e.target.value)}
                />
                <input
                  type="text"
                  value={settings.emailBackgroundColor}
                  onChange={(e) => handleChange('emailBackgroundColor', e.target.value)}
                  placeholder="#f4f4f4"
                />
              </div>
            </div>

            <div className={styles.formGroup}>
              <label>Content Background Color</label>
              <div className={styles.colorInput}>
                <input
                  type="color"
                  value={settings.contentBackgroundColor}
                  onChange={(e) => handleChange('contentBackgroundColor', e.target.value)}
                />
                <input
                  type="text"
                  value={settings.contentBackgroundColor}
                  onChange={(e) => handleChange('contentBackgroundColor', e.target.value)}
                  placeholder="#ffffff"
                />
              </div>
            </div>

            <div className={styles.formGroup}>
              <label>Font Family</label>
              <select
                value={settings.fontFamily}
                onChange={(e) => handleChange('fontFamily', e.target.value as 'Arial' | 'Georgia' | 'Verdana')}
              >
                <option value="Arial">Arial</option>
                <option value="Georgia">Georgia</option>
                <option value="Verdana">Verdana</option>
              </select>
            </div>

            <div className={styles.formGroup}>
              <label>Max Width (px)</label>
              <input
                type="number"
                value={settings.maxWidth}
                onChange={(e) => handleChange('maxWidth', parseInt(e.target.value) || 600)}
                min="400"
                max="800"
              />
            </div>
          </div>

          <div className={styles.modalActions}>
            <button className={styles.btnPrimary} onClick={onClose}>
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 20px;
    max-height: 670px;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }


    table {
        thead{
            position: relative;
            z-index: 100;
        }

        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 35px;
        border-collapse: collapse;
        border-spacing: 0;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }


        thead {

            tr {

                border: 1px solid gray;

                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 6px 12px;
                    color: #fff;
                    height: 35px;
                    position: sticky;
                    top: 0;
                    background: #676f7c;
                    color: #fff;
                    z-index: 9;
                    &:nth-child(2){
                        width: 350px;
                    }

                }

                td {
                    line-height: 2.5;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    &:nth-child(even) {
                        background-color: #f2f2f2;

                    }

                }
            }
        }

        tbody {
            background-color: #fff;

            tr {
                margin: 0;
                &:nth-child(even) {
                    background-color: #f2f2f2;

                }

                td {
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 6px 12px;
                    height: 42px;
                    white-space: pre-wrap;

                    &:first-child{
                        width: 100px;
                    }

                    img{
                        object-fit: contain;
                        max-width: 130px;
                    }

                    .messageText {
                        box-shadow: none;
                        outline: none;
                        height: 38px;
                        width: 180px;
                        padding: 6px;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 1.5;
                        color: #495057;
                        background-color: #fff;
                        background-clip: padding-box;
                        border: 1px solid #ced4da;
                        border-radius: 0.25rem;
                        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    .videoThumbBox{
                        width: 240px;
                        height: 150px;
                        .custom-video-player{
                            height: 100%;
                        }
                    }

                }
            }
        }
    }
}

.searchBox {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        display: flex;
        flex-direction: column;
    }

    .showdropdwn {
        width: 82px;
        height: 38px;
        padding: 4px;
    }

    .searchInput {
        box-shadow: none;
        outline: none;
        height: 38px;
        padding: 6px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;

        position: absolute;
        right: 20px;
    }
}

.loaderImg {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 400px;
}

.saveButton {
    width: 70px;
    height: 38px;
    color: #fff;
    background-color: var(--primaryColor);
    border-color: #122b40;
    border-radius: 5px;
    cursor: pointer;


    @media screen and (max-width: 768px) and (min-width: 320px) {
        margin-left: 0px;
        margin-top: 10px;
    }

}

.saveBtnf {
    width: 70px;
    height: 38px;
    color: #fff;
    background-color: var(--primaryColor);
    border-color: #122b40;
    border-radius: 5px;
    cursor: pointer;
    position: absolute;
    right: 213px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        position: unset;
        margin-top: 10px;
    }
}

.noteText {
    font-size: 18px;
    line-height: normal;
    margin-bottom: 20px;
    font-weight: 600;
    color: var(--primaryColor);
    text-align: center;
    color: red
}

.showCommentsBtn {
    width: 120px;
    height: 30px;
    border-radius: 4px;
    text-decoration: none;
    border: none;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    background-color: var(--primaryColor);
    color: #fff;
    padding: 0px 12px;
}

.showCommentPopup {
    overflow: auto;
    h2 {
        display: none;
    }

    .continuePopup {
        padding: 20px;
        text-align: center;

        @media screen and (max-width: 767px) and (min-width: 320px) {
            width: 240px;
        }

        .continuetext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);
        }

        .yesAndnoBtn {
            display: flex;
            gap: 10px;
            padding-top: 20px;

            .okBtn {
                width: 100%;
                height: 45px;
                border-radius: 6px;
                text-decoration: none;
                gap: 8px;
                border: none;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                background-color: var(--primaryColor);
                color: #fff;

                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
            }

        }
    }
}

.commentListPopup{
    width: 550px;
    .tblscrollPop{
        .continuetext{
            margin-top: 0px;
            margin-bottom: 12px;
            font-weight: 600;
            text-align: left;
            font-size: 20px;
        }
        .tblscroll{
            margin-bottom: 0px;
            max-height: 450px;
            table{
                margin-bottom: 12px;
                thead{
                    position: relative;
                    z-index: 100;
                }
                
                tr{
                    th{
                        font-size: 14px;
                        padding: 6px;
                        &:first-child{
                            white-space: pre-line;
                        }
                    }
                    td{
                        font-size: 14px;
                        padding: 6px;
                        text-align: left;
                        input{
                            box-shadow: none;
                            outline: none;
                            height: 38px;
                            padding: 6px;
                            font-size: 14px;
                            font-weight: 400;
                            line-height: 1.5;
                            color: #495057;
                            background-color: #fff;
                            background-clip: padding-box;
                            border: 1px solid #ced4da;
                            border-radius: 0.25rem;
                            margin: 0px;
                        }
                    }
                   }
            }
          
        }
    }
}

.showCommentPopupLoader{
    .commentListPopup{
        height: 300px;
    }
}


.errorStyle.errorStyle {
    .tooltip.tooltip {
      background-color: #ff5d47;
      color: #fff;
      font-family: Noto Sans;
      font-size: 10px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #fff;
      margin-bottom: 5px;
    }
  
  }

.reactionDiv {
    display: flex;
    flex-direction: row;
    align-items: center;
    column-gap: 10px;

    .reactionSpan {
        display: inline-flex;
        align-items: center;
    }
    .reactionIcon {
        display: flex;
        margin-right: 2px;
    }
    .reactionText {
        vertical-align: middle;
    }
}
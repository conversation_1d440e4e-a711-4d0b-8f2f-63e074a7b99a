.snippetLibrary {
  width: 180px;
  background: white;
  border-right: 1px solid #e0e0e0;
  padding: 12px;
  overflow-y: auto;

  h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.snippetList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.snippetItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  transition: all 0.2s;

  &:hover {
    background: #f0f0f0;
    border-color: #007bff;
  }
}

.snippetHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #333;
}

.insertBtn {
  padding: 4px 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #007bff;
}

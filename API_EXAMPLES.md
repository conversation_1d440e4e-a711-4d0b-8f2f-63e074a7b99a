# API Examples for Dyspatch Template Editor

## POST - Save/Create Template (Store HTML in DB)

### Endpoint
```
POST /dyspatch/templates
```

### Request Headers
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer <token>"
}
```

### Request Body (Create New Template)
```json
{
  "templateName": "welcome_email_template",
  "displayName": "Welcome Email Template",
  "fileName": "welcome_email_template.html",
  "subjectLine": "Welcome to Our Platform!",
  "html": "<!DOCTYPE html><html><head><title>Welcome</title></head><body><h1>Welcome {{userName}}!</h1></body></html>"
}
```

### Request Body (Update Existing Template)
```json
{
  "templateId": "123e4567-e89b-12d3-a456-************",
  "templateName": "welcome_email_template",
  "displayName": "Welcome Email Template (Updated)",
  "fileName": "welcome_email_template.html",
  "subjectLine": "Welcome to Our Platform!",
  "html": "<!DOCTYPE html><html><head><title>Welcome</title></head><body><h1>Welcome {{userName}}!</h1><p>Thank you for joining!</p></body></html>"
}
```

### Success Response (201 Created / 200 Updated)
```json
{
  "success": true,
  "message": "Template saved successfully",
  "data": {
    "templateId": "123e4567-e89b-12d3-a456-************",
    "templateName": "welcome_email_template",
    "displayName": "Welcome Email Template",
    "fileName": "welcome_email_template.html",
    "subjectLine": "Welcome to Our Platform!",
    "html": "<!DOCTYPE html><html><head><title>Welcome</title></head><body><h1>Welcome {{userName}}!</h1></body></html>",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "createdBy": "<EMAIL>",
    "version": 1
  }
}
```

### Error Response (400 Bad Request)
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid template data",
    "details": [
      {
        "field": "html",
        "message": "HTML content is required"
      },
      {
        "field": "templateName",
        "message": "Template name must be alphanumeric with underscores only"
      }
    ]
  }
}
```

### Error Response (500 Internal Server Error)
```json
{
  "success": false,
  "error": {
    "code": "DATABASE_ERROR",
    "message": "Failed to save template to database"
  }
}
```

---

## GET - Fetch Template(s)

### Endpoint 1: Get All Templates (List)
```
GET /dyspatch/templates
```

### Query Parameters (Optional)
```
?page=1&limit=20&search=welcome&sortBy=updatedAt&sortOrder=desc
```

### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "templateId": "123e4567-e89b-12d3-a456-************",
        "templateName": "welcome_email_template",
        "displayName": "Welcome Email Template",
        "fileName": "welcome_email_template.html",
        "subjectLine": "Welcome to Our Platform!",
        "html": "<!DOCTYPE html><html><head><title>Welcome</title></head><body><h1>Welcome {{userName}}!</h1></body></html>",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z",
        "createdBy": "<EMAIL>",
        "version": 1
      },
      {
        "templateId": "223e4567-e89b-12d3-a456-426614174001",
        "templateName": "password_reset_template",
        "displayName": "Password Reset Template",
        "fileName": "password_reset_template.html",
        "subjectLine": "Reset Your Password",
        "html": "<!DOCTYPE html><html><head><title>Password Reset</title></head><body><h1>Reset Password</h1><p>Click here: {{resetLink}}</p></body></html>",
        "createdAt": "2024-01-14T09:20:00Z",
        "updatedAt": "2024-01-14T09:20:00Z",
        "createdBy": "<EMAIL>",
        "version": 2
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3
    }
  }
}
```

---

### Endpoint 2: Get Single Template by ID
```
GET /dyspatch/templates/:templateId
```

### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "templateId": "123e4567-e89b-12d3-a456-************",
    "templateName": "welcome_email_template",
    "displayName": "Welcome Email Template",
    "fileName": "welcome_email_template.html",
    "subjectLine": "Welcome to Our Platform!",
    "html": "<!DOCTYPE html><html><head><title>Welcome</title><style>body { font-family: Arial; }</style></head><body><h1>Welcome {{userName}}!</h1><p>Thank you for joining us.</p></body></html>",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "createdBy": "<EMAIL>",
    "updatedBy": "<EMAIL>",
    "version": 1,
    "metadata": {
      "variables": ["userName"],
      "estimatedSize": "2.5 KB"
    }
  }
}
```

---

### Endpoint 3: Get Template by Name
```
GET /dyspatch/templates/name/:templateName
```

### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "templateId": "123e4567-e89b-12d3-a456-************",
    "templateName": "welcome_email_template",
    "displayName": "Welcome Email Template",
    "fileName": "welcome_email_template.html",
    "subjectLine": "Welcome to Our Platform!",
    "html": "<!DOCTYPE html><html><head><title>Welcome</title></head><body><h1>Welcome {{userName}}!</h1></body></html>",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "version": 1
  }
}
```

---

### Error Response (404 Not Found)
```json
{
  "success": false,
  "error": {
    "code": "TEMPLATE_NOT_FOUND",
    "message": "Template with ID '123e4567-e89b-12d3-a456-************' not found"
  }
}
```

---

## Database Schema Example

### SQL Table Structure
```sql
CREATE TABLE dyspatch_templates (
  template_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_name VARCHAR(255) UNIQUE NOT NULL,
  display_name VARCHAR(255) NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  subject_line VARCHAR(500),
  html TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_by VARCHAR(255),
  version INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT true
);

CREATE INDEX idx_template_name ON dyspatch_templates(template_name);
CREATE INDEX idx_created_at ON dyspatch_templates(created_at);
CREATE INDEX idx_updated_at ON dyspatch_templates(updated_at);
```

### MongoDB Schema Example
```javascript
{
  templateId: ObjectId,
  templateName: String, // unique index
  displayName: String,
  fileName: String,
  subjectLine: String,
  html: String, // Store as text, can be large
  createdAt: Date,
  updatedAt: Date,
  createdBy: String,
  updatedBy: String,
  version: Number,
  isActive: Boolean
}
```

---

## Frontend Integration Example

### Using Axios

```typescript
// Save/Create Template
const saveTemplate = async (payload: {
  templateId?: string;
  templateName: string;
  displayName: string;
  fileName: string;
  subjectLine: string;
  html: string;
}) => {
  try {
    const url = `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/dyspatch/templates`;
    const response = await axios.post(url, payload, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });
    
    return response.data;
  } catch (error: any) {
    if (error.response?.data?.error) {
      throw new Error(error.response.data.error.message);
    }
    throw error;
  }
};

// Get Single Template
const getTemplate = async (templateId: string) => {
  try {
    const response = await axios.get(
      `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/dyspatch/templates/${templateId}`
    );
    return response.data.data;
  } catch (error: any) {
    if (error.response?.status === 404) {
      throw new Error('Template not found');
    }
    throw error;
  }
};

// Get All Templates
const getAllTemplates = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
}) => {
  try {
    const response = await axios.get(
      `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/dyspatch/templates`,
      { params }
    );
    return response.data.data;
  } catch (error: any) {
    throw error;
  }
};
```

---

## Notes

1. **HTML Storage**: Store HTML as `TEXT` or `LONGTEXT` in SQL databases, or as `String` in MongoDB. HTML can be large, so ensure your database column type supports it.

2. **Sanitization**: Consider sanitizing HTML before storing to prevent XSS attacks if templates are user-generated.

3. **Versioning**: The `version` field helps track template changes over time.

4. **Large HTML**: For very large HTML templates (>1MB), consider:
   - Using compression before storing
   - Storing in blob storage (S3, etc.) and saving only the URL in DB
   - Using database-specific large text types

5. **Indexing**: Index `template_name` for fast lookups, and `created_at`/`updated_at` for sorting.

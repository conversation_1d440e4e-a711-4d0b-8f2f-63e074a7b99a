import { BRYZOS_SEND_INVOICE_GROUP, routePaths } from "../../utils/constant";
import { Link, useLocation } from "react-router-dom";
import useGetIndicator from "../../hooks/useGetIndicator";
import React, { useEffect, useContext, useState } from "react";
import styles from "./SideNavigation.module.scss";
import { useImmer } from "use-immer";
import { UserRoleCtx } from "../../pages/AppContainer";
import clsx from "clsx";

type Navigation = {
  title: string;
  link: string;
  isShowIndecater: boolean;
  disabled: boolean;
  isActive: boolean;
  subMenus?: Navigation[];
};

type Props = {
  sidebarDocked: boolean;
  toggleSidebar: () => void;
};

const SideNavigation: React.FC<Props> = ({ sidebarDocked, toggleSidebar }) => {
  const { data: indicatorData, isLoading: isIndicatorDataLoading } = useGetIndicator();
  const [searchText, setSearchText] = useState<string>('');
  const userRole = useContext(UserRoleCtx);
  const location = useLocation();

  let navigations;

    if (userRole === BRYZOS_SEND_INVOICE_GROUP) {
      navigations = [
        {
          title: "Send Invoice Email",
          link: routePaths.sendInvoiceEmail,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
        },
      ];
    } else {
      navigations = [
        {
          title: "User",
          link: "",
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [
            {
              title: "Create User",
              link: `/${routePaths.user}/${routePaths.create}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "List Users",
              link: `/${routePaths.user}/${routePaths.list}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Pre-Approved Email Ids",
              link: `/${routePaths.user}/${routePaths.preApproved}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Pending Users",
              link: `/${routePaths.user}/${routePaths.pending}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Companies",
              link: `/${routePaths.user}/${routePaths.pendingCompanyList}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Cohorts",
              link: `/${routePaths.user}/${routePaths.cohortList}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Spread Users",
              link: `/${routePaths.user}/${routePaths.discountedUsers}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Bnpl",
          link: "",
          isShowIndecater: indicatorData?.BNPL?.is_indicator,
          disabled: false,
          isActive: true,
          subMenus: [
            {
              title: "Approve",
              link: `/${routePaths.bnpl}/${routePaths.bnplApproveReject}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Limit Increase",
              link: `/${routePaths.bnpl}/${routePaths.bnplLimitIncrease}`,
              isShowIndecater:
                indicatorData?.IncreaseBnplCredit?.is_indicator,
              disabled: false,
              isActive: true,
            },
            {
              title: "Close Order and Replenish",
              link: `/${routePaths.bnpl}/${routePaths.bnplCloseOrderAndReplenish}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Resale Certificate",
          link: "",
          isShowIndecater: indicatorData?.ReSaleCertificate?.is_indicator,
          disabled: false,
          isActive: true,
          subMenus: [
            {
              title: "Approve/ Reject",
              link: `/${routePaths.resaleCertificate}/${routePaths.resaleCertificateApproveReject}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Products",
          link: `/${routePaths.referecneData}`,
          isShowIndecater: false,
          disabled: true,
          isActive: true,
          subMenus: [],
        },
        {
          title: "ACH Credit",
          link: "",
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [
            {
              title: "Orders",
              link: `/${routePaths.achCredit}/${routePaths.achCreditOrders}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Payment",
              link: `/${routePaths.achCredit}/${routePaths.achCreditPayment}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Close Order",
              link: `/${routePaths.achCredit}/${routePaths.achCreditCloseOrder}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Send Invoice Email",
          link: routePaths.sendInvoiceEmail,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Generate Email",
          link: routePaths.generateEmail,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Broadcast Notifications",
          link: routePaths.broadcastNotifications,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Deposit Setting",
          link: "",
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [
            {
              title: "Global Deposit",
              link: `/${routePaths.depositSettings}/${routePaths.globalSetting}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Custom Deposit",
              link: `/${routePaths.depositSettings}/${routePaths.customSetting}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Setting",
          link: ``,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [
            {
              title: "Order Cancellation",
              link: `/${routePaths.setting}/${routePaths.orderCancellation}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Reference Setting",
              link: `/${routePaths.setting}/${routePaths.referenceSetting}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Cancel / Close Order",
          link: `${routePaths.cancelOrder}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Dyspatch Template",
          link: `${routePaths.dyspatchTemplate}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
        },
        {
          title: "Bryzos Pay",
          link: "",
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [
            {
              title: "Make A Payment",
              link: `/${routePaths.bryzosPay}/${routePaths.makeApayment}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "CASS Transaction Map",
              link: `/${routePaths.bryzosPay}/${routePaths.cassMappingTransactionWithPo}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "CASS Make A Payment Status",
              link: `/${routePaths.bryzosPay}/${routePaths.cassTransactionSubmissionStatus}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Buyer Payments",
              link: `/${routePaths.bryzosPay}/${routePaths.buyerPayments}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Sales Tax",
          link: "",
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [
            {
              title: "Remove Sales Tax",
              link: `/${routePaths.salesTax}/${routePaths.removeSalesTax}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
              subMenus: [],
            },
            {
              title: "View Nexus Threshold",
              link: `/${routePaths.salesTax}/${routePaths.viewNexusThreshold}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
              subMenus: [],
            },
          ],
        },
        {
          title: "Convert Ach To Bnpl",
          link: `/${routePaths.convertAchToBnpl}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Safe Uploads",
          link: `/${routePaths.safeUploads}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Safe Imgix/ImageKit",
          link: `/${routePaths.safeImgixImageKit}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Terms n Conditions",
          link: `/${routePaths.tnc}`,
          isShowIndecater: false,
          disabled: false,
          isActive: false,
          subMenus: [],
        },
        {
          title: "Video Library",
          link: "",
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [
            {
              title: "Manage Video Library",
              link: `/${routePaths.videoLibrary}/${routePaths.videoUploads}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
            {
              title: "Video Tags",
              link: `/${routePaths.videoLibrary}/${routePaths.videoTag}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Automatic Seller Invoice \nProcessing (Experimental)",
          link: `/${routePaths.invoiceEmailAttachments}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Holiday List",
          link: `/${routePaths.holidayList}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Chats",
          link: `/${routePaths.chats}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Fetch Users Log",
          link: `/${routePaths.fetchUserLogs}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "External API Key-Endpoint Permissions",
          link: `/${routePaths.externalApisAccess}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        }, {
          title: "Admin Video Tooltip",
          link: `/${routePaths.adminVideoTooltipScreens}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        }
      ];
    }

  const closeSidebarOnClickMobileView = () => {
    if (!sidebarDocked) {
      toggleSidebar();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  const checkLink = (link:string)=>{
    if(link.charAt(0) !== '/'){
      link = '/' + link;
    }
    return (link === location.pathname)
  }

  // Filtered array based on the searchText
  const filteredNavigations = navigations
    .map((navigation) => {
      // Check if the parent title matches
      const parentMatch = navigation.title.toLowerCase().includes(searchText.toLowerCase());
      //if parent matches return the whole object. 
      if(parentMatch)
        return navigation;
      
      // If the navigation item has subMenu, filter subMenu based on title match
      const filteredSubMenu = navigation.subMenus?.filter((subMenuItem) =>
        subMenuItem.title.toLowerCase().includes(searchText.toLowerCase())
      );

      // If the parent matches or any subMenu item matches, return a new object with filtered subMenu
      if ((filteredSubMenu && filteredSubMenu.length > 0)) {
        return {
          ...navigation,
          subMenus: filteredSubMenu, // This will be undefined if no subMenu items match
        };
      }

      // Otherwise, return null (don't include this navigation item)
      return null;
    })
    .filter((navigation) => navigation !== null); // Remove nulls from the array

  return (
    <div className={styles.sideNavigationbar}>
      <input
        className={styles.searchText}
        type="text"
        value={searchText}
        onChange={handleInputChange}
        placeholder="Search navigation..."
      />
      <div className={styles.sideNavMain}>
      {filteredNavigations?.map((naviagtion, i) => (
        <div key={i}>
          {naviagtion.isActive &&
            (naviagtion.subMenus?.length ? (
              <div>
                <div className={styles.sidebarTitle}>
                  {naviagtion.isShowIndecater && (
                    <span className={styles.redExlametry}>!</span>
                  )}
                  <div className={styles.naviagtionTitle}>
                    {naviagtion.title}
                  </div>
                </div>

                {naviagtion.subMenus.map(
                  (subMenu, j) =>
                    subMenu.isActive && (
                      <div className={styles.mainMenu} key={j}>
                        {subMenu.disabled ? (
                          <button
                            className={styles.subMenuLink}
                            disabled={true}
                          >
                            {subMenu.title}
                          </button>
                        ) : (
                          <Link
                            className={clsx(styles.subMenuLink,subMenu.link === location.pathname?styles.activeLink:"") }
                            to={subMenu.link}
                            onClick={closeSidebarOnClickMobileView}
                          >
                            {subMenu.title}
                          </Link>
                        )}

                        {subMenu.isShowIndecater && (
                          <span className={styles.redExlametry}>!</span>
                        )}
                      </div>
                    )
                )}
              </div>
            ) : (
              <div className={styles.naviagtionTitleRef}>
                {naviagtion.disabled ? (
                  <button 
                    className={styles.naviagtionTitle} 
                    disabled={true}>
                    {naviagtion.title}
                  </button>
                ) : (
                  <Link
                    to={!naviagtion.disabled ? naviagtion.link : ""}
                    onClick={closeSidebarOnClickMobileView}
                     className={clsx((checkLink(naviagtion.link))?styles.activeLink:'')}
                  >
                    {naviagtion.title}
                  </Link>
                )}
                {naviagtion.isShowIndecater && (
                  <span className={styles.redExlametry}>!</span>
                )}
              </div>
            ))}
        </div>
      ))}
      </div>
    
    </div>
  );
};

export default SideNavigation;

import { BRYZOS_SEND_INVOICE_GROUP, routePaths } from "../../utils/constant";
import { Link, useLocation } from "react-router-dom";
import useGetIndicator from "../../hooks/useGetIndicator";
import React, { useEffect, useContext, useState } from "react";
import styles from "./SideNavigation.module.scss";
import { useImmer } from "use-immer";
import { UserRoleCtx } from "../../pages/AppContainer";
import clsx from "clsx";
import appLinks from "../../utils/appLinks.json";

type Navigation = {
  title: string;
  link: string;
  isShowIndecater: boolean;
  disabled: boolean;
  isActive: boolean;
  subMenus?: Navigation[];
};

type Props = {
  sidebarDocked: boolean;
  toggleSidebar: () => void;
};

const SideNavigation: React.FC<Props> = ({ sidebarDocked, toggleSidebar }) => {
  const { data: indicatorData, isLoading: isIndicatorDataLoading } = useGetIndicator();
  const [searchText, setSearchText] = useState<string>('');
  const userRole = useContext(UserRoleCtx);
  const location = useLocation();

  let navigations;

    if (userRole === BRYZOS_SEND_INVOICE_GROUP) {
      navigations = [
        {
          title: "Send Invoice Email",
          link: routePaths.sendInvoiceEmail,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
        },
      ];
    } else {
      navigations = [
        {
          title: "User",
          link: "",
          isShowIndecater: false,
          disabled: appLinks.user.isDisabled,
          isActive: appLinks.user.isActive,
          subMenus: [
            {
              title: "Create User",
              link: `/${routePaths.user}/${routePaths.create}`,
              isShowIndecater: false,
              disabled: appLinks.create?.isDisabled,
              isActive: appLinks.create?.isActive,
            },
            {
              title: "List Users",
              link: `/${routePaths.user}/${routePaths.list}`,
              isShowIndecater: false,
              disabled: appLinks.list?.isDisabled,
              isActive: appLinks.list?.isActive,
            },
            {
              title: "Pre-Approved Email Ids",
              link: `/${routePaths.user}/${routePaths.preApproved}`,
              isShowIndecater: false,
              disabled: appLinks["pre-approved-emails"].isDisabled,
              isActive: appLinks["pre-approved-emails"].isActive,
            },
            {
              title: "Pending Users",
              link: `/${routePaths.user}/${routePaths.pending}`,
              isShowIndecater: false,
              disabled: appLinks.pending?.isDisabled,
              isActive: appLinks.pending?.isActive,
            },
            {
              title: "Companies",
              link: `/${routePaths.user}/${routePaths.pendingCompanyList}`,
              isShowIndecater: false,
              disabled: appLinks["pending-company-list"]?.isDisabled,
              isActive: appLinks["pending-company-list"]?.isActive,
            },
            {
              title: "Cohorts",
              link: `/${routePaths.user}/${routePaths.cohortList}`,
              isShowIndecater: false,
              disabled: appLinks["cohort-list"]?.isDisabled,
              isActive: appLinks["cohort-list"]?.isActive,
            },
            {
              title: "Spread Users",
              link: `/${routePaths.user}/${routePaths.discountedUsers}`,
              isShowIndecater: false,
              disabled: appLinks["spread-users"]?.isDisabled,
              isActive: appLinks["spread-users"]?.isActive,
            },
          ],
        },
        {
          title: "Bnpl",
          link: "",
          isShowIndecater: indicatorData?.BNPL?.is_indicator,
          disabled: appLinks.bnpl?.isDisabled,
          isActive: appLinks.bnpl?.isActive,
          subMenus: [
            {
              title: "Approve",
              link: `/${routePaths.bnpl}/${routePaths.bnplApproveReject}`,
              isShowIndecater: false,
              disabled: appLinks["bnpl-approve-reject"]?.isDisabled,
              isActive: appLinks["bnpl-approve-reject"]?.isActive,
            },
            {
              title: "Limit Increase",
              link: `/${routePaths.bnpl}/${routePaths.bnplLimitIncrease}`,
              isShowIndecater:
                indicatorData?.IncreaseBnplCredit?.is_indicator,
              disabled: appLinks["bnpl-limit-increase"]?.isDisabled,
              isActive: appLinks["bnpl-limit-increase"]?.isActive,
            },
            {
              title: "Close Order and Replenish",
              link: `/${routePaths.bnpl}/${routePaths.bnplCloseOrderAndReplenish}`,
              isShowIndecater: false,
              disabled: appLinks["close-order-and-replenish"]?.isDisabled,
              isActive: appLinks["close-order-and-replenish"]?.isActive,
            },
            {
              title: "Credit Replenishment",
              link: `/${routePaths.bnpl}/${routePaths.creditReplenishment}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Resale Certificate",
          link: "",
          isShowIndecater: indicatorData?.ReSaleCertificate?.is_indicator,
          disabled: appLinks["resale-certificate-approve-reject"]?.isDisabled,
          isActive: appLinks["resale-certificate-approve-reject"]?.isActive,
          subMenus: [
            {
              title: "Approve/ Reject",
              link: `/${routePaths.resaleCertificate}/${routePaths.resaleCertificateApproveReject}`,
              isShowIndecater: false,
              disabled: appLinks["resale-certificate-approve-reject"]?.isDisabled,
              isActive: appLinks["resale-certificate-approve-reject"]?.isActive,
            },
          ],
        },
        {
          title: "Products",
          link: `/${routePaths.referecneData}`,
          isShowIndecater: false,
          disabled: appLinks["referenceData"]?.isDisabled,
          isActive: appLinks["referenceData"]?.isActive,
          subMenus: [],
        },
        {
          title: "ACH Credit",
          link: "",
          isShowIndecater: false,
          disabled: appLinks["ach-credit"]?.isDisabled,
          isActive: appLinks["ach-credit"]?.isActive,
          subMenus: [
            {
              title: "Orders",
              link: `/${routePaths.achCredit}/${routePaths.achCreditOrders}`,
              isShowIndecater: false,
              disabled: appLinks["orders"]?.isDisabled,
              isActive: appLinks["orders"]?.isActive,
            },
            {
              title: "Payment",
              link: `/${routePaths.achCredit}/${routePaths.achCreditPayment}`,
              isShowIndecater: false,
              disabled: appLinks["payment"]?.isDisabled,
              isActive: appLinks["payment"]?.isActive,
            },
            {
              title: "Close Order",
              link: `/${routePaths.achCredit}/${routePaths.achCreditCloseOrder}`,
              isShowIndecater: false,
              disabled: appLinks["close-order"]?.isDisabled,
              isActive: appLinks["close-order"]?.isActive,
            },
          ],
        },
        {
          title: "Send Invoice Email",
          link: routePaths.sendInvoiceEmail,
          isShowIndecater: false,
          disabled: appLinks["send-invoice-email"]?.isDisabled,
          isActive: appLinks["send-invoice-email"]?.isActive,
          subMenus: [],
        },
        {
          title: "Generate Email",
          link: routePaths.generateEmail,
          isShowIndecater: false,
          disabled: appLinks["generate-email"]?.isDisabled,
          isActive: appLinks["generate-email"]?.isActive,
          subMenus: [],
        },
        {
          title: "Broadcast Notifications",
          link: routePaths.broadcastNotifications,
          isShowIndecater: false,
          disabled: appLinks["broadcast-notifications"]?.isDisabled,
          isActive: appLinks["broadcast-notifications"]?.isActive,
          subMenus: [],
        },
        {
          title: "Deposit Setting",
          link: "",
          isShowIndecater: false,
          disabled: appLinks["deposit"]?.isDisabled,
          isActive: appLinks["deposit"]?.isActive,
          subMenus: [
            {
              title: "Global Deposit",
              link: `/${routePaths.depositSettings}/${routePaths.globalSetting}`,
              isShowIndecater: false,
              disabled: appLinks["global-setting"]?.isDisabled,
              isActive: appLinks["global-setting"]?.isActive,
            },
            {
              title: "Custom Deposit",
              link: `/${routePaths.depositSettings}/${routePaths.customSetting}`,
              isShowIndecater: false,
              disabled: appLinks["custom-setting"]?.isDisabled,
              isActive: appLinks["custom-setting"]?.isActive,
            },
          ],
        },
        {
          title: "Setting",
          link: ``,
          isShowIndecater: false,
          disabled: appLinks["setting"]?.isDisabled,
          isActive: appLinks["setting"]?.isActive,
          subMenus: [
            {
              title: "Order Cancellation",
              link: `/${routePaths.setting}/${routePaths.orderCancellation}`,
              isShowIndecater: false,
              disabled: appLinks["order-cancellation"]?.isDisabled,
              isActive: appLinks["order-cancellation"]?.isActive,
            },
            {
              title: "Reference Setting",
              link: `/${routePaths.setting}/${routePaths.referenceSetting}`,
              isShowIndecater: false,
              disabled: appLinks["reference-setting"]?.isDisabled,
              isActive: appLinks["reference-setting"]?.isActive,
            },
          ],
        },
        {
          title: "Cancel / Close Order",
          link: `${routePaths.cancelOrder}`,
          isShowIndecater: false,
          disabled: appLinks["cancel-order"]?.isDisabled,
          isActive: appLinks["cancel-order"]?.isActive,
          subMenus: [],
        },
        {
          title: "Dyspatch Template",
          link: `${routePaths.dyspatchTemplate}`,
          isShowIndecater: false,
          disabled: appLinks["dyspatch-template"]?.isDisabled,
          isActive: appLinks["dyspatch-template"]?.isActive,
        },
        {
          title: `${import.meta.env.VITE_CLIENT_NAME} Pay`,
          link: "",
          isShowIndecater: false,
          disabled: appLinks["bryzos-pay"]?.isDisabled,
          isActive: appLinks["bryzos-pay"]?.isActive,
          subMenus: [
            {
              title: "Make A Payment",
              link: `/${routePaths.bryzosPay}/${routePaths.makeApayment}`,
              isShowIndecater: false,
              disabled: appLinks["make-a-payment"]?.isDisabled,
              isActive: appLinks["make-a-payment"]?.isActive,
            },
            {
              title: "CASS Transaction Map",
              link: `/${routePaths.bryzosPay}/${routePaths.cassMappingTransactionWithPo}`,
              isShowIndecater: false,
              disabled: appLinks["cass-mapping-transaction-with-po"]?.isDisabled,
              isActive: appLinks["cass-mapping-transaction-with-po"]?.isActive,
            },
            {
              title: "CASS Make A Payment Status",
              link: `/${routePaths.bryzosPay}/${routePaths.cassTransactionSubmissionStatus}`,
              isShowIndecater: false,
              disabled: appLinks["cass-make-a-payment-status"]?.isDisabled,
              isActive: appLinks["cass-make-a-payment-status"]?.isActive,
            },
            {
              title: "Buyer Payments",
              link: `/${routePaths.bryzosPay}/${routePaths.buyerPayments}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
            },
          ],
        },
        {
          title: "Sales Tax",
          link: "",
          isShowIndecater: false,
          disabled: appLinks["remove-sales-tax"]?.isDisabled,
          isActive: appLinks["remove-sales-tax"]?.isActive,
          subMenus: [
            {
              title: "Remove Sales Tax",
              link: `/${routePaths.salesTax}/${routePaths.removeSalesTax}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
              subMenus: [],
            },
            {
              title: "View Nexus Threshold",
              link: `/${routePaths.salesTax}/${routePaths.viewNexusThreshold}`,
              isShowIndecater: false,
              disabled: false,
              isActive: true,
              subMenus: [],
            },
          ],
        },
        {
          title: "Convert Ach To Bnpl",
          link: `/${routePaths.convertAchToBnpl}`,
          isShowIndecater: false,
          disabled: appLinks["convert-ach-to-bnpl"]?.isDisabled,
          isActive: appLinks["convert-ach-to-bnpl"]?.isActive,
          subMenus: [],
        },
        {
          title: "Safe Uploads",
          link: `/${routePaths.safeUploads}`,
          isShowIndecater: false,
          disabled: appLinks["safe-uploads"]?.isDisabled,
          isActive: appLinks["safe-uploads"]?.isActive,
          subMenus: [],
        },
        {
          title: "Safe Imgix/ImageKit",
          link: `/${routePaths.safeImgixImageKit}`,
          isShowIndecater: false,
          disabled: appLinks["safe-imgix-image-kit"]?.isDisabled,
          isActive: appLinks["safe-imgix-image-kit"]?.isActive,
          subMenus: [],
        },
        {
          title: "Terms n Conditions",
          link: `/${routePaths.tnc}`,
          isShowIndecater: false,
          disabled: appLinks["tnc"]?.isDisabled,
          isActive: appLinks["tnc"]?.isActive,
          subMenus: [],
        },
        {
          title: "Video Library",
          link: "",
          isShowIndecater: false,
          disabled: appLinks["video-library"]?.isDisabled,
          isActive: appLinks["video-library"]?.isActive,
          subMenus: [
            {
              title: "Manage Video Library",
              link: `/${routePaths.videoLibrary}/${routePaths.videoUploads}`,
              isShowIndecater: false,
              disabled: appLinks["video-uploads"]?.isDisabled,
              isActive: appLinks["video-uploads"]?.isActive,
            },
            {
              title: "Video Tags",
              link: `/${routePaths.videoLibrary}/${routePaths.videoTag}`,
              isShowIndecater: false,
              disabled: appLinks["video-tags"]?.isDisabled,
              isActive: appLinks["video-tags"]?.isActive,
            },
          ],
        },
        {
          title: "Automatic Seller Invoice \nProcessing (Experimental)",
          link: `/${routePaths.invoiceEmailAttachments}`,
          isShowIndecater: false,
          disabled: appLinks["invoice-email-attachments"]?.isDisabled,
          isActive: appLinks["invoice-email-attachments"]?.isActive,
          subMenus: [],
        },
        {
          title: "Holiday List",
          link: `/${routePaths.holidayList}`,
          isShowIndecater: false,
          disabled: appLinks["holiday-list"]?.isDisabled,
          isActive: appLinks["holiday-list"]?.isActive,
          subMenus: [],
        },
        {
          title: "BOM Upload (Experimental)",
          link: `/${routePaths.bomList}`,
          isShowIndecater: false,
          disabled: appLinks["chats"]?.isDisabled,
          isActive: appLinks["chats"]?.isActive,
          subMenus: [],
        },
        {
          title: "Fetch Users Log",
          link: `/${routePaths.fetchUserLogs}`,
          isShowIndecater: false,
          disabled: appLinks["fetch-users-log"]?.isDisabled,
          isActive: appLinks["fetch-users-log"]?.isActive,
          subMenus: [],
        },
        {
          title: "External API Key-Endpoint Permissions",
          link: `/${routePaths.externalApisAccess}`,
          isShowIndecater: false,
          disabled: appLinks["external-api-key-endpoint-permissions"]?.isDisabled,
          isActive: appLinks["external-api-key-endpoint-permissions"]?.isActive,
          subMenus: [],
        },
        {
          title: "Game Settings",
          link: `/${routePaths.gameSettings}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        },
        {
          title: "Chats",
          link: `/${routePaths.chats}`,
          isShowIndecater: false,
          disabled: false,
          isActive: true,
          subMenus: [],
        }, {
          title: "Admin Video Tooltip",
          link: `/${routePaths.adminVideoTooltipScreens}`,
          isShowIndecater: false,
          disabled: appLinks["admin-video-tooltip-screens"]?.isDisabled,
          isActive: appLinks["admin-video-tooltip-screens"]?.isActive,
          subMenus: [],
        }
      ];
    }

  const closeSidebarOnClickMobileView = () => {
    if (!sidebarDocked) {
      toggleSidebar();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  const checkLink = (link:string)=>{
    if(link.charAt(0) !== '/'){
      link = '/' + link;
    }
    return (link === location.pathname)
  }

  // Filtered array based on the searchText
  const filteredNavigations = navigations
    .map((navigation) => {
      // Check if the parent title matches
      const parentMatch = navigation.title.toLowerCase().includes(searchText.toLowerCase());
      //if parent matches return the whole object. 
      if(parentMatch)
        return navigation;
      
      // If the navigation item has subMenu, filter subMenu based on title match
      const filteredSubMenu = navigation.subMenus?.filter((subMenuItem) =>
        subMenuItem.title.toLowerCase().includes(searchText.toLowerCase())
      );

      // If the parent matches or any subMenu item matches, return a new object with filtered subMenu
      if ((filteredSubMenu && filteredSubMenu.length > 0)) {
        return {
          ...navigation,
          subMenus: filteredSubMenu, // This will be undefined if no subMenu items match
        };
      }

      // Otherwise, return null (don't include this navigation item)
      return null;
    })
    .filter((navigation) => navigation !== null); // Remove nulls from the array

  return (
    <div className={styles.sideNavigationbar}>
      <input
        className={styles.searchText}
        type="text"
        value={searchText}
        onChange={handleInputChange}
        placeholder="Search navigation..."
      />
      <div className={styles.sideNavMain}>
      {filteredNavigations?.map((naviagtion, i) => (
        <div key={i}>
          {naviagtion.isActive &&
            (naviagtion.subMenus?.length ? (
              <div>
                <div className={styles.sidebarTitle}>
                  {naviagtion.isShowIndecater && (
                    <span className={styles.redExlametry}>!</span>
                  )}
                  <div className={styles.naviagtionTitle}>
                    {naviagtion.title}
                  </div>
                </div>

                {naviagtion.subMenus.map(
                  (subMenu, j) =>
                    subMenu.isActive && (
                      <div className={styles.mainMenu} key={j}>
                        {subMenu.disabled ? (
                          <button
                            className={styles.subMenuLink}
                            disabled={true}
                          >
                            {subMenu.title}
                          </button>
                        ) : (
                          <Link
                            className={clsx(styles.subMenuLink,subMenu.link === location.pathname?styles.activeLink:"") }
                            to={subMenu.link}
                            onClick={closeSidebarOnClickMobileView}
                          >
                            {subMenu.title}
                          </Link>
                        )}

                        {subMenu.isShowIndecater && (
                          <span className={styles.redExlametry}>!</span>
                        )}
                      </div>
                    )
                )}
              </div>
            ) : (
              <div className={styles.naviagtionTitleRef}>
                {naviagtion.disabled ? (
                  <button 
                    className={styles.naviagtionTitle} 
                    disabled={true}>
                    {naviagtion.title}
                  </button>
                ) : (
                  <Link
                    to={!naviagtion.disabled ? naviagtion.link : ""}
                    onClick={closeSidebarOnClickMobileView}
                     className={clsx((checkLink(naviagtion.link))?styles.activeLink:'')}
                  >
                    {naviagtion.title}
                  </Link>
                )}
                {naviagtion.isShowIndecater && (
                  <span className={styles.redExlametry}>!</span>
                )}
              </div>
            ))}
        </div>
      ))}
      </div>
    
    </div>
  );
};

export default SideNavigation;

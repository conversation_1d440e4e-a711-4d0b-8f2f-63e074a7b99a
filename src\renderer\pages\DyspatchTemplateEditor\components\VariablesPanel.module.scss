.variablesPanel {
  width: 320px;
  background: white;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.panelTabs {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
  padding: 12px 12px 0 12px;
}

.tabBtn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;

  &:hover {
    color: #333;
    background: #f5f5f5;
  }

  &.active {
    color: #007bff;
    border-bottom-color: #007bff;
  }
}

.panelContent {
  flex: 1;
  overflow-y: auto;
  padding: 0 12px 12px 12px;
  min-height: 0;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;

    &:hover {
      background: #555;
    }
  }
}

.addVariable {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;

  input {
    flex: 1;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }

  button {
    padding: 8px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
}

.variablesList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variableItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  width: 100%;
  box-sizing: border-box;
}

.variableName {
  min-width: 100px;
  font-family: monospace;
  font-size: 13px;
  color: #007bff;
  flex-shrink: 0;
}

.variableValue {
  flex: 1;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  background: #f8f8f8;
  box-sizing: border-box;
  width: 100%;
  min-width: 0;
  
  &:focus {
    background: white;
    outline: none;
    border-color: #007bff;
  }
}

.deleteBtn {
  padding: 4px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  color: #dc3545;
}

.emptyVariables {
  text-align: center;
  color: #999;
  padding: 20px;

  p {
    margin-bottom: 8px;
  }

  .hint {
    font-size: 13px;
    font-style: italic;
  }
}

.collectionsList {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.collectionItem {
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #f9f9f9;

  h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
  }
}

.collectionJson {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  resize: vertical;
  min-height: 120px;

  &:focus {
    outline: none;
    border-color: #007bff;
  }
}

.collectionHint {
  margin: 8px 0 0 0;
  font-size: 11px;
  color: #666;
  font-family: monospace;
}

.emptyCollections {
  text-align: center;
  padding: 30px;
  color: #666;

  .hint {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    font-family: monospace;
  }
}

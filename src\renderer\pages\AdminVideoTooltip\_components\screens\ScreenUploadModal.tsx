import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControlLabel,
  Checkbox,
  Typography,
  Alert,
  LinearProgress,
  Paper,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
} from '@mui/icons-material';
import { useNotification } from '../../../../contexts/NotificationContext';
import { uploadFileAndGetS3Url } from '@bryzos/giss-ui-library';
import { usePostScreen } from '../../../../hooks/admin-video-tooltip/useUploadScreen';

interface ScreenUploadModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  file: File | null;
  is_active: 0 | 1;
}

const ScreenUploadModal = ({ open, onClose, onSuccess }: ScreenUploadModalProps) => {
  const { showNotification } = useNotification() as { showNotification: (message: string, severity?: string) => void };
  const [formData, setFormData] = useState<FormData>({
    name: '',
    file: null,
    is_active: 1,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const { mutateAsync: uploadScreen } = usePostScreen();
  
  // Check is_active once and reuse
  const isActive = formData.is_active === 1;

  const handleClose = () => {
    if (!loading) {
      setFormData({ name: '', file: null, is_active: 1 });
      setError(null);
      onClose();
    }
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(null);
  };

  const handleFileSelect = (file: File | null) => {
    if (file) {
      // Validate file type
      const validTypes = ['image/png', 'image/jpeg', 'image/jpg'];
      if (!validTypes.includes(file.type)) {
        setError('Please select a PNG or JPEG image file.');
        return;
      }

      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        setError('File size must be less than 10MB.');
        return;
      }

      setFormData(prev => ({ ...prev, file }));
      setError(null);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('Screen name is required.');
      return false;
    }

    if (!formData.file) {
      setError('Please select an image file.');
      return false; 
    }

    return true;
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  };

  const getImageDimensions = (imageUrl: string): Promise<{ natural_width: number; natural_height: number }> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          natural_width: img.naturalWidth,
          natural_height: img.naturalHeight
        });
      };
      img.onerror = () => {
        // Default dimensions if image can't be loaded
        resolve({
          natural_width: 1920,
          natural_height: 1080
        });
      };
      img.src = imageUrl;
    });
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError(null);

      let imageUrl = '';
      let dimensions = { natural_width: 1920, natural_height: 1080 };

      // Step 1: Upload file to S3 and get the URL
      if (formData.file) {
        try {
          imageUrl = await uploadFileAndGetS3Url(
            formData.file, 
            import.meta.env.VITE_S3_UPLOAD_ADMIN_TOOLTIP_SCREENS_BUCKET, 
            '/staging-video-tooltips/screens/', 
            import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', 
            'screen', 
            import.meta.env.VITE_ENVIRONMENT
          );
          
          // Get image dimensions from the S3 URL
          try {
            dimensions = await getImageDimensions(imageUrl);
          } catch (error) {
            console.log('Could not get image dimensions, using defaults');
          }
        } catch (s3Error) {
          console.error('S3 upload failed:', s3Error);
          throw new Error('Failed to upload file to S3. Please try again.');
        }
      } else {
        // This case should not happen as we only handle file uploads now
        throw new Error('No file selected for upload');
      }


      const submitData = {
        name: formData.name.trim(),
        slug: generateSlug(formData.name),
        image_url: imageUrl,
        natural_width: dimensions.natural_width,
        natural_height: dimensions.natural_height,
        is_active: formData.is_active,
        created_by: 'admin_user'
      };

      await uploadScreen({ data: submitData });

      showNotification('Screen uploaded successfully!', 'success');
      onSuccess();
    } catch (err: any) {
      console.log('Upload failed:', err);
      setError(err.message || 'Upload failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle component='h4'>Upload Screen</DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Screen Name */}
        <TextField
          fullWidth
          label="Screen Name"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          margin="normal"
          disabled={loading}
          placeholder="e.g., Dashboard Overview"
        />

        {/* File Upload */}
          <Paper
            sx={{
              p: 3,
              border: dragOver ? '2px dashed #415a77' : '2px dashed #ccc',
              backgroundColor: dragOver ? 'rgba(65, 90, 119, 0.05)' : '#fafafa',
              textAlign: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
            }}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => document.getElementById('file-input')?.click()}
          >
            <input
              id="file-input"
              type="file"
              accept="image/png,image/jpeg,image/jpg"
              style={{ display: 'none' }}
              onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}
              disabled={loading}
            />
            <CloudUploadIcon sx={{ fontSize: 48, color: '#778da9', mb: 1 }} />
            <Typography variant="h6" sx={{ mb: 1 }}>
              {formData.file ? formData.file.name : 'Drop image here or click to browse'}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              PNG or JPEG files, max 10MB
            </Typography>
          </Paper>
      

        {/* Active Checkbox */}
        <FormControlLabel
          control={
            <Checkbox
              checked={isActive}
              onChange={(e) => handleInputChange('is_active', e.target.checked ? 1 : 0)}
              disabled={loading}
            />
          }
          label="Active (screen will be available for hotspot creation)"
          sx={{ mt: 2 }}
        />

        {loading && <LinearProgress sx={{ mt: 2 }} />}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
        >
          {loading ? 'Uploading...' : 'Upload Screen'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ScreenUploadModal;

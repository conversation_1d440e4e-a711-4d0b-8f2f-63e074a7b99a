import { BlockPalette } from './BlockPalette';
import { Canvas } from './Canvas';
import { PropertiesPanel } from './PropertiesPanel';
import { useState } from 'react';
import styles from './BlockEditor.module.scss';

export function BlockEditor() {
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);

  return (
    <div className={styles.blockEditor}>
      <BlockPalette />
      <Canvas
        selectedBlockId={selectedBlockId}
        onSelectBlock={setSelectedBlockId}
      />
      <PropertiesPanel
        selectedBlockId={selectedBlockId}
        onClose={() => setSelectedBlockId(null)}
      />
    </div>
  );
}

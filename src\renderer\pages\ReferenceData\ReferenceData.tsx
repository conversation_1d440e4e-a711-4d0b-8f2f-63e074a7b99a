import { useCallback, useEffect, useRef, useState } from "react";
import { rest, camelCase, startCase } from "lodash-es";
import MatPopup from "../../components/common/MatPopup";
import useGetAllReferenceDataProduct from "../../hooks/useGetAllReferenceDataProduct";
import useUploadXlsxFile from "../../hooks/useUploadXlsxFile";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import useUpdateReferenceProduct from "../../hooks/useUpdateReferenceProduct";
import Loader from "../../components/common/Loader";
import styles from "./ReferenceData.module.scss";
import * as XLSX from "xlsx";
import { useImmer } from "use-immer";
import useUpdateReferenceData from "../../hooks/useUpdateReferenceData";
import { AgGridReact } from 'ag-grid-react';

import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { CellClassRules, CellClickedEvent } from "ag-grid-community";
import { referenceDataVersionStatus, uploadColumnError } from "../../utils/constant";
import SucessErrorPopup from "../../components/common/SucessErrorPopup";
import useGetRevertReferenceData from "../../hooks/useGetRevertReferenceData";
import clsx from "clsx";
import useGetReferenceDataVersions from "../../hooks/useGetReferenceDataVersions";
import { Tooltip } from "@mui/material";

const ReferenceData = () => {
  const [filteredReferenceProducts, setFilteredReferenceProducts] = useImmer<any>(
    []
  );
  const [uplodedReferenceProducts, setUplodedReferenceProducts] = useImmer([]);
  const [rowData, setRowData] = useImmer<any[]>([]);
  const [columnData, setColumnData] = useImmer<any[]>([]);
  const [popupRowData, setPopupRowData] = useImmer<any[]>([]);
  const [popupColumnData, setpopupColumnData] = useImmer<any[]>([]);

  const [editProductData, setEditProductData] = useState<any>(null);
  const [apiResponseMessage, setApiResponseMessage] = useState<string | null>(
    null
  );
  const [errorResponseMessage, setErrorResponseMessage] = useState<any | null>(
    null
  );
  const [successErrorPopupMessage, setSucessErrorPopupMessage] = useImmer<any>(null);
  const [versionErrorMessage, setVersionErrorMessage] = useImmer<any>(null);
  const [userExcelData, setuserExcelData] = useImmer<any>(null);
  const [userWorksheetData, setuserWorksheetData] = useImmer<any>(null);
  const [dragData, setDragData] = useImmer<any>(null);
  const [finalColumnMap , setFinalColumnMap] = useImmer<any>({});
  const [userExcelColumnMap , setUserExcelColumnMap] = useImmer<any>({});
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [showConfirmationReferenceDataPopup, setShowConfirmationReferenceDataPopup] = useImmer(false);

  const uplodedReferenceRef = useRef<AgGridReact>(null);

  const [schema, setSchema] = useState<yup.ObjectSchema<any>>();
  const [uploadFileData, setUploadFileData] = useState<any>("");
  const [productDataVersion, setProductDataVersion] = useState<any>("");
  const [currentProductVersion, setCurrentProductVersion] = useState<any>(null);
  const [prevProductVersion, setPrevProductVersion] = useState<any>(null);
  const [highlightedCellList, setHighlightedCellList] = useState<Set<number>[]>([]);
  let templateColumns: any[];

  const {
    mutate: revertReferenceData,
    data: getResponseData,
    isLoading: isGetRevertReferenceDataLoading,
  } = useGetRevertReferenceData();


  const {
    isLoading: isAllReferenceProductDataLoading,
    data: allReferenceProductData,
    isFetching: isAllReferenceProductDataFetching
  } = useGetAllReferenceDataProduct();
  const {
    isLoading: isReferenceDataVersionsLoading,
    data: referenceDataVersions,
  } = useGetReferenceDataVersions();

  if(allReferenceProductData?.[0]){
    templateColumns = Object.keys(allReferenceProductData[0]).slice(1, -4);
  }

  const {
    mutate: sendXlsxFileToServer,
    data: xlsxFileToServerData,
    isLoading: isSendXlsxFileToServerLoading,
  } = useUploadXlsxFile();
  
  const {
    mutate: updateReferenceData,
    isLoading: isUpdateReferenceDataLoading,
    data: referenceData,
  } = useUpdateReferenceData();


  const {
    mutate: updateReferenceProduct,
    isLoading: isUpdateReferenceProductLoading,
  } = useUpdateReferenceProduct();

  const useYupValidationResolver = (
    validationSchema: yup.ObjectSchema<any> | undefined
  ) =>
    useCallback(
      async (data: any) => {
        if (!validationSchema)
          return {
            values: {},
            errors: {},
          };
        try {
          const values = await validationSchema.validate(data, {
            abortEarly: false,
          });
          return {
            values,
            errors: {},
          };
        } catch (errors: any) {
          return {
            values: {},
            errors: errors.inner.reduce(
              (allErrors: any, currentError: any) => ({
                ...allErrors,
                [currentError.path]: {
                  type: currentError.type ?? "validation",
                  message: currentError.message,
                },
              }),
              {}
            ),
          };
        }
      },
      [validationSchema]
    );

  const resolver = useYupValidationResolver(schema);

  const { control, reset, watch } = useForm({
    resolver: resolver,
  });

  useEffect(() => {
    if (
      allReferenceProductData &&
      !isAllReferenceProductDataLoading &&
      !isAllReferenceProductDataFetching
    ) {
      setFilteredReferenceProducts(allReferenceProductData);
      setHighlightedCellList([]);
    }
  }, [
    allReferenceProductData,
    isAllReferenceProductDataLoading,
    isAllReferenceProductDataFetching,
  ]);

  useEffect(() => {
      if(referenceData?.version_error){
        setVersionErrorMessage(referenceData.version_error)
      }else if(referenceData && !isUpdateReferenceDataLoading){
        setApiResponseMessage(referenceData);
        setUplodedReferenceProducts([]);
        setProductDataVersion("")
        setVersionErrorMessage(null)
    }
  }, [referenceData, isUpdateReferenceDataLoading]);
  
  
  useEffect(() => {
    if (getResponseData) {
      setApiResponseMessage(getResponseData);
    }
  }, [getResponseData]);

  useEffect(() => {
    if (!isSendXlsxFileToServerLoading && xlsxFileToServerData?.productData?.length) {
      setUplodedReferenceProducts(xlsxFileToServerData.productData);
    }
    if(!isSendXlsxFileToServerLoading && xlsxFileToServerData?.excel_error_message){
      let columnRowMap: any = xlsxFileToServerData?.excel_error_message;
        let userColumnRowMap: any = {};
        for (let key in columnRowMap) {
          userColumnRowMap[finalColumnMap[key]] = columnRowMap[key].length < 10? columnRowMap[key].join(", ") + " rows" : columnRowMap[key].slice(0,9).join(", ") + ",..., Too many rows.";
        }
  
        const errorMessage = (
          <>
            {Object.keys(userColumnRowMap)?.map((item: string, index: number) => {
              return (
                <li key={index}>
                  {item} : {userColumnRowMap[item]}
                </li>
              );
            })}
          </>
        );
        setErrorResponseMessage(errorMessage);
    }
  }, [isSendXlsxFileToServerLoading, xlsxFileToServerData]);

  useEffect(() => {
    if (
      !isSendXlsxFileToServerLoading &&
      xlsxFileToServerData?.errorExcel?.length
    ) {
      if (uplodedReferenceRef?.current) {
        const firstError = xlsxFileToServerData.errorExcel[0];
        const firstErrorIndex = Number(Object.keys(firstError)[0]);
        const firstErrorCell =
          xlsxFileToServerData.errorExcel[0][firstErrorIndex][0];
        uplodedReferenceRef.current?.api?.ensureIndexVisible(
          firstErrorIndex - 1,
          "top"
        );
        uplodedReferenceRef.current?.api?.ensureColumnVisible(
          firstErrorCell,
          "start"
        );
      }
    }
  }, [
    uplodedReferenceRef.current,
    isSendXlsxFileToServerLoading,
    xlsxFileToServerData,
  ]);

  useEffect(() => {
    const _popupRowData: any[] = [];

    if (uplodedReferenceProducts?.length) {
      const _popupRowData: any[] = [];
      const _columns: any[] = [];

      uplodedReferenceProducts.map((row: any, index: number) => {
        const _popupColumnData: any = {};
        Object.keys(row).map((column: any) => {
          if (index === 0) {
            _columns.push({ field: column, cellClassRules: ragCellClassRules, suppressMovable: true });
          }

          _popupColumnData[column] = row[column];
        });

        _popupRowData.push(_popupColumnData);
      });

      setPopupRowData(_popupRowData);
      setpopupColumnData(_columns);
    } else {
      setPopupRowData([]);
      setpopupColumnData([]);
    }



  }, [uplodedReferenceProducts]);

  useEffect(() => {
    if (filteredReferenceProducts?.length) {
      const _rowData: any[] = [];
      const _columns: any[] = [];

      filteredReferenceProducts.map((row: any, index: number) => {
        const _columnData: any = {};
        Object.keys(row).map((column, i) => {
          if (index === 0 && column !== 'is_safe_product_code') {
            _columns.push({ field: column, cellClass:(highlightedCellList.length > 0 && highlightedCellList[index].has(i)) && styles.highlightedCellList, cellClassRules: ragCellClassRules, suppressMovable: true,cellRenderer: (props:any) => {
              return (
                <>
                  <Tooltip title={(column === 'UI_Description') && props?.value}>
                    <div className={clsx((highlightedCellList.length > 0 && highlightedCellList[props.rowIndex].has(i)) && styles.markHighlighted)}>
                      {props?.value?.toString()}
                    </div>
                  </Tooltip>
                </>
            );
            } });
          }
          _columnData[column] = row[column];
        });

        _rowData.push(_columnData);
      });

      setRowData(_rowData);
      setColumnData(_columns);
    } else {
      setRowData([]);
      setColumnData([]);
    }


  }, [filteredReferenceProducts]);

  useEffect(() => {
    if(referenceDataVersions){
      let versionList: any = {
        currentVersion: null,
        previousVersion: null
      }
      referenceDataVersions.forEach((referenceDataVersion: any) => {
        if(referenceDataVersion.status === referenceDataVersionStatus.active){
          versionList.currentVersion = referenceDataVersion.version_name;
        }
      });
      referenceDataVersions.forEach((referenceDataVersion: any) => {
        if(referenceDataVersion.status === referenceDataVersionStatus.previous){
          versionList.previousVersion = referenceDataVersion.version_name
        }
      });
      setCurrentProductVersion(versionList.currentVersion)
      setPrevProductVersion(versionList.previousVersion)
    }
  },[referenceDataVersions])

  const ragCellClassRules: CellClassRules = {
    'red-border': (params) => {
      if (xlsxFileToServerData?.errorExcel?.length) {
        return xlsxFileToServerData.errorExcel.some((obj: any) => {
          const rowNumber = Object.keys(obj).findIndex(key => +key === (params.rowIndex + 1));
          if (rowNumber > -1) {
            return obj[params.rowIndex + 1].some((columnName: any) => columnName === params.colDef.field);
          } else {
            return false;
          }
        });
      } else {
        return false;
      }
    },
  };


  const uploadXlsxFile = (file: any) => {
    setUserExcelColumnMap({});
    setFinalColumnMap({});
    const files = file.target.files[0];
    let index = files?.name.length - 1;
    for (; index >= 0; index--) {
      if (files?.name.charAt(index) === ".") {
        break;
      }
    }
    let fileName = files?.name.substring(index + 1, files?.name.length);
    if (fileName === "xlsx") {
      const reader = new FileReader();

      reader.onload = (event) => {
        if (
          typeof event.target?.result !== "string" &&
          typeof event.target?.result !== "undefined" &&
          event.target?.result
        ) {
          const check: ArrayBufferLike = event.target?.result;
          const data = new Uint8Array(check);
          const workbook = XLSX.read(data, { type: "array" });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          setuserWorksheetData(worksheet);
          setuserExcelData(excelData);

          if (excelData.length !== 0) {
            const excelColumns: any = excelData[0];
            templateColumns.forEach((templateColumn: string) => {
              setFinalColumnMap((data: any) => {
                data[templateColumn] = null;
                return data;
              });
            });
            excelColumns.forEach((excelColumn: string) => {
              setUserExcelColumnMap((data: any) => {
                data[excelColumn] = null;
                return data;
              });
            });
            excelColumns.forEach((excelColumn: string) => {
              templateColumns.forEach((templateColumn: string) => {
                if (
                  templateColumn.replace(/[\s_/$()#[\]\-.]+/g, '').toLowerCase() ===
                  excelColumn.replace(/[\s_/$()#[\]\-.]+/g, '').toLowerCase()
                ) {
                  setFinalColumnMap((data: any) => {
                    data[templateColumn] = excelColumn;
                    return data;
                  });
                  setUserExcelColumnMap((data: any) => {
                    data[excelColumn] = templateColumn;
                    return data;
                  });
                }
              });
            });
          } else {
            setSucessErrorPopupMessage("Invalid excel file");
            file.target.value = "";
          }
        }
      };
      reader.readAsArrayBuffer(files);
    } else {
      setSucessErrorPopupMessage(
        "Please make sure your file is in .xlsx format"
      );
      file.target.value = "";
    }
  };

  const setSelectedReferenceProduct = (product: any) => {
    const yupObj: any = {};
    const formDefaultValue: any = {};
    Object.keys(product).forEach((key) => {
      formDefaultValue[key] = product[key];
      yupObj[key] = yup.string().default(null).nullable();
    });
    setSchema(yup.object(yupObj));
    reset(formDefaultValue);

    setEditProductData(product);
  };

  const cancelEditProductData = () => {
    setEditProductData(null);
    setUserExcelColumnMap({})
    setFinalColumnMap({})
  };

  const saveProductEditData = () => {
    const requstObj = watch();
    requstObj["Shape_ID"] = +requstObj["Shape_ID"];
    requstObj["Size_Group_ID"] = +requstObj["Size_Group_ID"];
    requstObj["Product_ID"] = +requstObj["Product_ID"];

    updateReferenceProduct({ data: requstObj });
    setEditProductData(null);
  };

  const downloadxlsxFileTemplate = () => {

    const header = Object.keys(allReferenceProductData[0]).slice(1, -3)

    const workbook = XLSX.utils.book_new();
    const sheet = XLSX.utils.json_to_sheet([], {
      header,
    });
    XLSX.utils.json_to_sheet(allReferenceProductData);
    XLSX.utils.book_append_sheet(workbook, sheet);
    XLSX.writeFile(workbook, "XLSX File Template.xlsx");
  };

  const search = (event: any) => {
    let searchValue = event.target.value.trim();
    const highlightedList: Set<number>[] = [];
    if (searchValue) {
      searchValue = searchValue.toLocaleLowerCase();
      const searchResult = allReferenceProductData.filter((refProduct: any) => {
        let isFound = false;
        const _set:Set<number> = new Set();
        Object.keys(refProduct).forEach((key, i) => {
          let value = "";
          if (refProduct[key] !== null && refProduct[key] !== undefined) {
            value = refProduct[key].toString().toLocaleLowerCase();
          }
          if (value.includes(searchValue)) {
            _set.add(i);
            isFound = true;
          }
        });
        if(_set.size > 0){
          highlightedList.push(_set);
        }
        return isFound;
      });
      setFilteredReferenceProducts(searchResult);
    } else {
      setFilteredReferenceProducts(allReferenceProductData);
    }
    if(searchValue.length >= 3)
    setHighlightedCellList(highlightedList);
    else
    setHighlightedCellList([]);
  };

  const saveUplodedProductsData = () => {
    setShowConfirmationReferenceDataPopup(true)
  };
  
  const cancelUplodedProductsData = () => {
    updateReferenceData({
      action: false,
      productDataVersion: null
    });
    setUserExcelColumnMap({})
    setFinalColumnMap({})
    setProductDataVersion("")
  };

  const onDragLeave = (e: any) => {
    e.preventDefault();
    let element = e.currentTarget;
    element.classList.remove("dragged-over");
  }

  const onDragEnter = (e: any) => {
    e.preventDefault();
    let element = e.currentTarget;
    element.classList.add("dragged-over");
    e.dataTransfer.dropEffect = "move";
  }

  const onDragEnd = (e: any) => {
    e.currentTarget.classList.remove("dragged");
  }

  const onDragOver = (e: any) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  }

  const onDrop = (e: any, columnName: any, columnData: any,  status: string) => {
    e.preventDefault();
    e.currentTarget.classList.remove("dragged-over");
    if(status === 'excel'){
      setFinalColumnMap((data: any) => {
        Object.keys(data).forEach((column: string) => {
          if(dragData === data[column]){
            data[column] = null
          }
        })
        return data;
      })
      setUserExcelColumnMap((data: any) => {
        Object.keys(data).forEach((column: string) => {
          if(dragData === column){
            data[column] = null
          }
        })
        return data;
      })
    }
    if(status === 'bryzos'){
      setFinalColumnMap((data: any) => {
        Object.keys(data).forEach((column: any) => {
          if(dragData === data[column]){
            data[column] = null
          }
        })
        data[columnName] = dragData;
        return data;
      })
      setUserExcelColumnMap((data: any) => {
        if(columnData){
          data[columnData] = null;
        } 
        data[dragData] = columnName;
        return data;
      })
    }
  }

  const onDragStart = (e: any, data: any) => {
    let element = e.currentTarget;
    element.classList.add("dragged");
    e.dataTransfer.setData("text/plain", e.currentTarget.id);
    e.dataTransfer.effectAllowed = "move";
    setDragData(data);
  }

  const verifyFinalColumnMap = () => {
    let finalColumnValueCount = 0 ;
    Object.keys(finalColumnMap).forEach((finalColumnKey: any) => {
      if(finalColumnMap[finalColumnKey] !== null){
        finalColumnValueCount += 1;
      }
    })
    if(Object.keys(finalColumnMap).length === finalColumnValueCount){
      showVerifyColumnList();
    }
  }
  
  const toColumnName = (columnNumber: any)=>{
    let columnName = "";
    while (columnNumber > 0) {
      let remainder = (columnNumber - 1) % 26;
      columnName = String.fromCharCode(65 + remainder) + columnName; 
      columnNumber = Math.floor((columnNumber - 1) / 26);
    }
    return columnName;
  }

  const updateRowData = ( headerToIndexMap:any[], rowNumber: number)=>{
    let retObj:any = {};
    headerToIndexMap.forEach((header:any, index:any)=>{
      const cellName = toColumnName(header+1) + (rowNumber+1);
      retObj[templateColumns[index]] = userWorksheetData[cellName];
    });

    return retObj
  }

  const showVerifyColumnList = () => {
    let userExcelFirstColumn: any[] = [];
    let checkData: any;
    let templateHeaderToUserExcelIndexArr: any[] = [];
    let checkForExcelTemplate: any[] = [];
    userExcelFirstColumn[0] = Object.keys(finalColumnMap);
    templateColumns.forEach((templateHeader) => {
      templateHeaderToUserExcelIndexArr.push(
        userExcelData[0].findIndex(
          (item: any) => item === finalColumnMap[templateHeader]
        )
      );
    });
    userExcelData.forEach((rowData: any[], index: any) => {
      if (index > 0) {
        checkForExcelTemplate.push(
          updateRowData(templateHeaderToUserExcelIndexArr, index)
        );
      }
    });
    checkData = checkForExcelTemplate;
    const payload = {
      data: checkData,
    };
    sendXlsxFileToServer(payload);
  };

  const resetExcelData = () => {
    setUserExcelColumnMap({})
    setFinalColumnMap({})
    setUploadFileData("")
  }

  const handleDisableVerifyBtn = () => {
    let finalColumnValueCount = 0 ;
    Object.keys(finalColumnMap).forEach((finalColumnKey: any) => {
      if(finalColumnMap[finalColumnKey] !== null){
        finalColumnValueCount += 1;
      }
    })
    if(Object.keys(finalColumnMap).length !== finalColumnValueCount) return true;
    return false;
  }

  const confirmationPopupYes = () => {
    revertReferenceData({})
    confirmationPopupClose();
  }
  const confirmationReferenceDataPopupYes = () => {
    updateReferenceData({
      action: true,
      productDataVersion: productDataVersion
    });
    setUserExcelColumnMap({})
    setFinalColumnMap({})
    confirmationPopupClose();
  }

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false)
    setShowConfirmationReferenceDataPopup(false)
  }

  return (
    <div className={styles.refDataMain}>
      {isAllReferenceProductDataLoading ||
        isSendXlsxFileToServerLoading ||
        isUpdateReferenceProductLoading ||
        isGetRevertReferenceDataLoading ||
        isReferenceDataVersionsLoading ||
        isAllReferenceProductDataFetching ? (
        <div className={styles.loaderImg}>
          <Loader />
          {isSendXlsxFileToServerLoading && 
            <div className={styles.loaderText}> We are translating your data... Please hold on. </div> 
          }
        </div>
      ) : (
        <div>
          <div className={styles.DownloadAnduploadbox}>
            <div className={styles.uploadFiletxt}>
              Upload Xlsx File For Reference Data
              <input
                className={styles.uploadFileInput}
                type="file"
                onChange={(e) => uploadXlsxFile(e)}
                value={uploadFileData}
              />
            </div>
            <div>
              <button className={styles.exportExcelbtn} onClick={()=>{setShowConfirmationPopup(true)}} disabled={!prevProductVersion}>
                Revert Product Data {prevProductVersion && <>to {prevProductVersion}</>}
              </button>
            </div>
          </div>
          {(Object.keys(userExcelColumnMap).length !== 0 && Object.keys(finalColumnMap).length !== 0) ?
            <div className={styles.uploadRefernceData}>
              <div className={styles.uploadRefernceDataHeader}
              onDragLeave={(e) => onDragLeave(e)}
              onDragEnter={(e) => onDragEnter(e)}
              onDragEnd={(e) => onDragEnd(e)}
              onDragOver={(e) => onDragOver(e)}
              
              >
                <div className={styles.title}>Your Header</div>
                <span className={styles.dragBtnGrid}>
                {Object.keys(userExcelColumnMap)?.map((userExcelColumn: any)=>{
                  return <button className={styles.dragBtn} key={userExcelColumn}
                  draggable = {userExcelColumnMap[userExcelColumn] === null}
                      onDragStart={(e) => onDragStart(e, userExcelColumn)}
                      onDragEnd={(e) => onDragEnd(e)}
                      onDrop={(e) => onDrop(e, userExcelColumn, userExcelColumnMap[userExcelColumn],'excel')}
                  > 
                    {userExcelColumn} 
                  </button>
                })}
                </span>
               
              </div>
              <div
              onDragLeave={(e) => onDragLeave(e)}
              onDragEnter={(e) => onDragEnter(e)}
              onDragEnd={(e) => onDragEnd(e)}
              onDragOver={(e) => onDragOver(e)}
              
              >
                <div className={styles.title}>{import.meta.env.VITE_CLIENT_NAME} Header </div>
                {Object.keys(finalColumnMap)?.map((finalColumn: string, index: number) => {

                  return <div className={styles.dragColumn}
                  key={index}
                  onDrop={(e) => onDrop(e, finalColumn,  finalColumnMap[finalColumn], 'bryzos')}
                  >
                    <span className={styles.dragColumnlbl}>{finalColumn}</span>
                    <button
                      draggable = {finalColumnMap[finalColumn] !== null}
                     onDragStart={(e) => onDragStart(e, finalColumnMap[finalColumn])}
                     onDragEnd={(e) => onDragEnd(e)}
                     
                    >{finalColumnMap[finalColumn] ?? finalColumn}</button>
                    </div>
                })}
              </div>
              <div className={styles.yesAndnoBtn}>
                <button className={styles.okBtn} onClick={verifyFinalColumnMap} disabled={handleDisableVerifyBtn()}> Verify </button>
                <button className={styles.okBtn} onClick={resetExcelData}> Cancel </button>
              </div>
            </div>
            :
            <>
              <div className={styles.prodUploadSearchSection}>
                <input className={styles.searchInput} type="text" onChange={search} placeholder="Search" />
                {currentProductVersion &&
                 <span className={styles.currentVesionText}>Current Version: {currentProductVersion}</span>
                }
              </div>
              <div className={styles.agGridTable}>
                {filteredReferenceProducts.length > 0 && (
                  <div className="ag-theme-alpine agGridAdmin" style={{ height: 'calc(100vh - 250px)', width: '100%', minHeight:'400px' }}>
                    <AgGridReact headerHeight={35} rowHeight={35} rowData={rowData} columnDefs={columnData}></AgGridReact>
                  </div>
                )}
                <MatPopup
                  open={!!editProductData}
                  dialogTitle={"Update product line"}
                >
                  {editProductData && (
                    <div className={styles.editRefPop}>
                      <div className={styles.editRefDate}>
                        <div className={styles.titleFiled}>
                          {Object.keys(editProductData).map(
                            (key, index) =>
                              key !== "id" && (
                                <div key={editProductData.id + index}>
                                  {startCase(key)}
                                  <span>:</span>
                                </div>
                              )
                          )}
                        </div>
                        <div className={styles.inputFiled}>
                          {Object.keys(editProductData).map(
                            (key, index) =>
                              key !== "id" && (
                                <div key={editProductData.id + index}>
                                  <Controller
                                    control={control}
                                    name={key}
                                    rules={{ required: true }}
                                    render={({ field: { value, ...field } }) => (
                                      <>
                                        {key === "UI_Description" ? (
                                          <textarea
                                            className={styles.uiDescription}
                                            value={value ?? ""}
                                            {...field}
                                            {...rest}
                                          />
                                        ) : (
                                          <input
                                            value={value ?? ""}
                                            type={
                                              key === "Shape_ID" ||
                                                key === "Size_Group_ID" ||
                                                key === "Product_ID"
                                                ? "number"
                                                : "text"
                                            }
                                            {...field}
                                            {...rest}
                                          />
                                        )}
                                      </>
                                    )}
                                  />
                                </div>
                              )
                          )}
                        </div>
                      </div>
                      <div className={styles.btnFiled}>
                        <button onClick={saveProductEditData}>Save</button>
                        <button onClick={cancelEditProductData}>Cancel</button>
                      </div>
                    </div>
                  )}
                </MatPopup>
              </div>
            </>
          }
        </div>
      )}
      {isUpdateReferenceDataLoading ? (
        <MatPopup open={uplodedReferenceProducts?.length > 0}>
          <div className={styles.loaderClass}>
            <Loader />
          </div>
        </MatPopup>

      ) : (
        uplodedReferenceProducts?.length > 0 && (
          <MatPopup className="refPopup" open={uplodedReferenceProducts?.length > 0}>
            <div className={styles.refDataPop}>
              <div className={styles.productDataV}>
                <label >Product Data Version : </label>
                <Tooltip
              title={versionErrorMessage}
              placement="right-end"
              arrow
              classes={{
                popper: styles.errorStyle,
                tooltip: styles.tooltip,
                arrow:styles.arrowTooltip,
              }}
            >
                <input className={clsx(styles.searchInput, versionErrorMessage ? styles.errorInputVersion : '')} type="text" value={productDataVersion} onChange={(e) => setProductDataVersion(e.target.value)} />
            </Tooltip>
              </div>
              <div className="ag-theme-alpine agGridAdmin" style={{ height: 'calc(100vh - 250px)', width: '100%', minHeight:'400px'  }}>
                <AgGridReact ref={uplodedReferenceRef} headerHeight={35} rowHeight={35} rowData={popupRowData} columnDefs={popupColumnData}></AgGridReact>
              </div>
              <div className={styles.btnFiled}>
                <button onClick={saveUplodedProductsData} disabled={(xlsxFileToServerData?.errorExcel?.length !== 0 || productDataVersion.length === 0)}>Save</button>
                <button onClick={cancelUplodedProductsData}>Cancel</button>
              </div>
            </div>
          </MatPopup>
        )
      )}

      <MatPopup className={styles.approveRejectPopup} open={!!apiResponseMessage}>
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button className={styles.okBtn} onClick={() => setApiResponseMessage(null)}> Ok </button>
        </div>
      </MatPopup>
      <SucessErrorPopup 
        open={!!successErrorPopupMessage}
        messageText={successErrorPopupMessage}
        onPopupClose={() => {
          setSucessErrorPopupMessage("");
        }}
        />
        <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>
            Are you sure you want to revert to {prevProductVersion}?
            We cannot undo this. This will revert <b>GISS app & Homepage</b>.
          </p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
        <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationReferenceDataPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>
            Are you sure you want to update products? This will update the products on <b>GISS app & Homepage</b>.
          </p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationReferenceDataPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
      <MatPopup className={clsx(styles.errorResponseMessagePopup, 'errorResponseMessagePopup')} open={!!errorResponseMessage}>
        <div className={styles.errorResponseMessage}>
          <h2>We found issues with following fields:</h2>
          <div className={styles.errorResponseMessageText}>{errorResponseMessage}</div>
          <button className={styles.okBtn} onClick={() => setErrorResponseMessage(null)}> Ok </button>
        </div>
      </MatPopup>
    </div>
  );
};

export default ReferenceData;

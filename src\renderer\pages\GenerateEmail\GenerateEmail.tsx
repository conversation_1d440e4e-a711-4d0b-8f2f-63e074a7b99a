import { useEffect, useState } from "react";
import { useImmer } from "use-immer";
import Loader from "../../components/common/Loader";
import MatPopup from "../../components/common/MatPopup";
import styles from "./GenerateEmail.module.scss";
import InputField from "../../components/common/InputField";
import MatSelect from "../../components/common/MatSelect";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import useGetPoDetails from "../../hooks/useGetPoDetails";
import useGetReferenceData from "../../hooks/useGetReferenceData";
import usePostSendGenerateEmail from "../../hooks/usePostSendGenerateEmail";

const isEmail = (email: string) => {
  const emailPattern = new RegExp('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$');
  return emailPattern.test(email);
}
const GenerateEmailSchema = yup.object().shape({
  poNumber: yup.string().trim().default(""),
  emailType: yup.number().required("Required"),
  toEmail: yup.string().trim()
    .test('valid-emails', 'Enter valid email', value => {
      if (!value) return true; // empty field is allowed
      const emails = value.split(';');
      const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
      return isValid;
    })
    .default("")
    .required("Required"),
  ccEmail: yup.string().trim()
    .test('valid-emails', 'Enter valid email', value => {
      if (!value) return true; // empty field is allowed
      const emails = value.split(';');
      const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
      return isValid;
    })
    .default(""),
  bccEmail: yup.string().trim()
    .test('valid-emails', 'Enter valid email', value => {
      if (!value) return true; // empty field is allowed
      const emails = value.split(';');
      const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
      return isValid;
    })
    .default(""),
  checkAllPoLines: yup.boolean(),
    poLines: yup.array()
    .required("PO Lines are required"),
});

type GenerateEmailSchemaType = yup.InferType<typeof GenerateEmailSchema>;

const GenerateEmail = () => {
  const [apiResponseMessage, setApiResponseMessage] = useState("");
  const [emailTypesData, setEmailTypesData] = useImmer<any[]>([]);
  const [poDetailsData, setPoDetailsData] = useImmer<any[]>([]);
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [sendEmailData, setSendEmailData] = useImmer<any>(null);
  
  const { data: getReferenceData, isLoading: isGetReferenceDataLoading } = useGetReferenceData();
  const {
    mutate: getPoDetails,
    data: getPoDetailsData,
    isLoading: isGetPoDetailsLoading,
  } = useGetPoDetails();
  const {
    mutate: sendGenerateEmail,
    data: sendGenerateEmailData,
    isLoading: isSendGenerateEmailLoading,
  } = usePostSendGenerateEmail();

  const {
    register,
    control,
    handleSubmit,
    setError,
    clearErrors,
    reset,
    watch,
    setValue,
    getValues,
    formState: { errors, isValid },
  } = useForm<GenerateEmailSchemaType>({
    defaultValues: {
      poNumber:"",
      toEmail: "",
      ccEmail: "",
      bccEmail: "",
      checkAllPoLines: false,
    },
    resolver: yupResolver(GenerateEmailSchema),
  });

  useEffect(() => {
    if(getReferenceData?.reference_email_events){
      setEmailTypesData(getReferenceData.reference_email_events)
    }
  },[getReferenceData])

  useEffect(() => {
    if (getPoDetailsData) {
      setPoDetailsData(getPoDetailsData);
    }else{
      setPoDetailsData([]);
    }
  }, [getPoDetailsData]);

  useEffect(() => {
    if (sendGenerateEmailData) {
      setApiResponseMessage(sendGenerateEmailData);
    }
  }, [sendGenerateEmailData]);

  useEffect(() => {
    checkPoLineSelected()
  },[watch('poLines')])

  const confirmationPopupYes = () => {
    if (sendEmailData) {
      sendGenerateEmail(sendEmailData);
      reset({
        poNumber: "",
        toEmail: "",
        ccEmail: "",
        bccEmail: "",
        checkAllPoLines: false,
        poLines: []
      });
      setPoDetailsData([]);
    }

    confirmationPopupClose();
  };
  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setSendEmailData(null);
  };
  const generateEmailSubmitHandler = async (data: GenerateEmailSchemaType) => {
    const filteredData = poDetailsData.filter(item => data.poLines?.includes(String(item.po_line)));
    let selectedPoLine = {};
    filteredData.forEach((poDetail)=>{
      selectedPoLine ={...selectedPoLine,[poDetail.purchase_order_line_id]: poDetail.po_line} 
    })
    const payload = {
      "data": {
        "po_number": data.poNumber,
        "email_type": data.emailType,
        "to_email": data.toEmail,
        "cc_email": data.ccEmail,
        "bcc_email": data.bccEmail,
        "po_lines": selectedPoLine,
      }
    }
    setSendEmailData(payload);
    setShowConfirmationPopup(true);

  };

  const getPoDetailHandler = (poNumber: any) => {+
    reset({
      poNumber: poNumber?.trim(),
      toEmail: "",
      ccEmail: "",
      bccEmail: "",
      checkAllPoLines: false,
    });
    setPoDetailsData([]);
    getPoDetails({
      poNumber: poNumber?.trim()
    })
  }
  const poLinesHandler = (e: any) => {
    let poLines: any = getValues("poLines");
    if (e.target.checked) {
      poDetailsData.forEach((item) => {
        const { po_line } = item;
        if (po_line >= 1 && po_line <= poLines.length) {
          poLines[po_line] = String(po_line);
        }
      });
      setValue("poLines", poLines);
    } else {
      poDetailsData.forEach((item) => {
        const { po_line } = item;
        if (po_line >= 1 && po_line <= poLines.length) {
          poLines[po_line] = false;
        }
      });
      setValue("poLines", poLines);
    }
    
  };

  const allPoLinesHandler = () => {
    let poLines: any = getValues("poLines");
    const allPoLine = poLines.every((poLine: any) => poLine !== false);
    setValue('checkAllPoLines', allPoLine)
  }

  const checkPoLineSelected = () => {
    let poLines: any = watch('poLines');
    const allPoLine = poLines?.every((poLine: any) => poLine === false);
    if(allPoLine ?? true){
      setError('poLines',{message: 'Select one PO Line'})
    }else{
      clearErrors('poLines')
    }
  }

  return (
    <div className="contentMain">
      {isGetReferenceDataLoading || isGetPoDetailsLoading || isSendGenerateEmailLoading ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <input
              className={styles.searchInput}
              type="text"
              {...register("poNumber")}
              placeholder="Enter PO Number"
            />
            <button className={styles.generateEmailBtn} onClick={() => getPoDetailHandler(getValues('poNumber'))} disabled={watch('poNumber')?.trim()?.length === 0}>Get PO Detail</button>
          </div>
          <div className={styles.generateEmailPage}>
              <div className={styles.inputFiledLoginPass}>
                <div className={styles.emailText}>Email Type</div>

                <MatSelect
                  className={styles.InputFieldcss}
                  fieldName={register("emailType").name}
                  control={control}
                  placeHolderText="Choose One"
                  options={emailTypesData?.map(x => ({ title: x.email_name, value: x.id }))}
                />
              </div>
              <div className={styles.inputFiledLoginPass}>
                <div className={styles.emailText}>To email</div>
                <InputField
                  className={styles.InputFieldcss}
                  control={control}
                  fieldName={register("toEmail").name}
                  label=""
                  placeholder="To email (multiple separate with a semicolon)"
                />
              </div>
              <div className={styles.inputFiledLoginPass}>
                <div className={styles.emailText}>Cc email</div>
                <InputField
                  className={styles.InputFieldcss}
                  control={control}
                  fieldName={register("ccEmail").name}
                  label=""
                  placeholder="Cc email (multiple separate with a semicolon)"
                />
              </div>
              <div className={styles.inputFiledLoginPass}>
                <div className={styles.emailText}>Bcc email</div>
                <InputField
                  className={styles.InputFieldcss}
                  control={control}
                  fieldName={register("bccEmail").name}
                  label=""
                  placeholder="Bcc email (multiple separate with a semicolon)"
                />
              </div>
              <div className={styles.inputFiledLoginPass}>
                {poDetailsData?.length > 0 &&
                  <>
                <label htmlFor="">
                  <input
                  className={styles.checkBox}
                    type="checkbox"
                    {...register("checkAllPoLines")}
                    onChange={(e) => {
                      register("checkAllPoLines").onChange(e)
                      poLinesHandler(e)
                    }
                    }
                    id=""
                  />
                  Check all PO lines
                </label><br />
                {poDetailsData.map((poDetail: any, index: number) => (
                <label htmlFor="" key={index}>
                  <input
                  className={styles.checkBox}
                    type="checkbox"
                    {...register(`poLines.${poDetail.po_line}`)}
                    value={poDetail.po_line}
                    onChange={(e) => {
                      register(`poLines.${poDetail.po_line}`).onChange(e)
                      allPoLinesHandler()
                      checkPoLineSelected()
                    }
                    }
                    id=""
                  />
                  PO line {poDetail.po_line}
                </label>
                ))}
                  </>

                }
                
              </div>
              

              <button className={styles.loginBtn} onClick={handleSubmit(generateEmailSubmitHandler)} type="submit" disabled={!(isValid && Object.keys(errors).length === 0)}>
                Send Email
              </button>
          </div>
        </div>
      )}
      <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>Do you want to continue ?</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div
            className={styles.successfullytext}
            dangerouslySetInnerHTML={{ __html: apiResponseMessage }}
          ></div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
    </div>
  );
};

export default GenerateEmail;

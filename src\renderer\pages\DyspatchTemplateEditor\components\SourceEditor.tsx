import Editor, { type OnMount } from '@monaco-editor/react';
import { useTemplateStore } from '../stores/templateStore';
import { SnippetLibrary } from './SnippetLibrary';
import { VariablesPanel } from './VariablesPanel';
import { Toast } from './Toast';
import { generateEmailHtml } from '../utils/emailGenerator';
import { validateHtml } from '../utils/htmlValidator';
import { useCallback, useEffect, useRef, useState } from 'react';
import styles from './SourceEditor.module.scss';

export function SourceEditor() {
  const { sourceHtml, setSourceHtml, blocks, settings, variables, editorMode, setSettings, setHtmlErrorCount } = useTemplateStore();
  const [toastVisible, setToastVisible] = useState(false);
  const monacoRef = useRef<any>(null);
  const editorRef = useRef<any>(null);
  const validationTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const tooltipObserverRef = useRef<MutationObserver | null>(null);
  const tooltipIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  useEffect(() => {
    if (editorMode === 'source' && !sourceHtml && blocks.length > 0) {
      const generatedHtml = generateEmailHtml(blocks, settings, variables, {}, false);
      setSourceHtml(generatedHtml);
    }
  }, [editorMode, sourceHtml, blocks, settings, variables, setSourceHtml]);

  // Reset error count when unmounting (e.g. switching to block mode)
  useEffect(() => {
    return () => {
      setHtmlErrorCount(0);
      if (validationTimerRef.current) clearTimeout(validationTimerRef.current);
      // Cleanup tooltip observers and intervals
      if (tooltipObserverRef.current) {
        tooltipObserverRef.current.disconnect();
        tooltipObserverRef.current = null;
      }
      if (tooltipIntervalRef.current) {
        clearInterval(tooltipIntervalRef.current);
        tooltipIntervalRef.current = null;
      }
    };
  }, [setHtmlErrorCount]);

  const runValidation = useCallback((html: string) => {
    // Debounce validation to avoid running on every keystroke
    if (validationTimerRef.current) clearTimeout(validationTimerRef.current);
    validationTimerRef.current = setTimeout(() => {
      const monaco = monacoRef.current;
      const editor = editorRef.current;
      if (!monaco || !editor) return;

      const model = editor.getModel();
      if (!model) return;

      const htmlErrors = validateHtml(html);

      // Convert our errors into Monaco markers
      const markers = htmlErrors.map((err) => ({
        severity: monaco.MarkerSeverity.Error,
        startLineNumber: err.line,
        startColumn: err.column,
        endLineNumber: err.endLine,
        endColumn: err.endColumn,
        message: err.message,
        source: 'HTML Validator',
      }));

      // Set markers on the model — this shows red squiggles + red on scrollbar
      monaco.editor.setModelMarkers(model, 'html-validator', markers);

      setHtmlErrorCount(markers.length);
    }, 500);
  }, [setHtmlErrorCount]);

  const handleEditorMount: OnMount = (editor, monaco) => {
    monacoRef.current = monaco;
    editorRef.current = editor;

    // Enable HTML formatting options
    monaco.languages.html.htmlDefaults.setOptions({
      format: {
        tabSize: 2,
        insertSpaces: true,
        wrapLineLength: 120,
        wrapAttributes: 'auto',
      },
    });

    // Disable ALL Monaco editor tooltips globally to prevent them from blocking interactions
    setTimeout(() => {
      const editorDom = (editor as any)._domElement;
      if (editorDom) {
        const disableAllTooltips = () => {
          // Remove title and aria-label attributes from all buttons to prevent tooltips
          const allButtons = editorDom.querySelectorAll('.monaco-editor .button, .monaco-editor .find-widget .button, .monaco-editor .monaco-button');
          allButtons.forEach((btn: HTMLElement) => {
            // Keep buttons visible
            btn.style.display = '';
            btn.style.visibility = 'visible';
            btn.style.opacity = '1';
            btn.style.pointerEvents = 'auto';
            btn.style.cursor = 'pointer';
            // Remove tooltip attributes
            btn.removeAttribute('title');
            btn.removeAttribute('aria-label');
          });
          
          // Hide only standalone tooltip overlays (not elements that are part of buttons)
          const allTooltips = editorDom.querySelectorAll(
            '.monaco-hover, .monaco-tooltip, .monaco-hover-content'
          );
          allTooltips.forEach((tooltip: HTMLElement) => {
            // Only hide if it's not a button or inside a button
            const isButton = tooltip.closest('.button, .monaco-button');
            if (!isButton && !tooltip.classList.contains('button') && !tooltip.classList.contains('monaco-button')) {
              tooltip.style.display = 'none';
              tooltip.style.visibility = 'hidden';
              tooltip.style.opacity = '0';
              tooltip.style.pointerEvents = 'none';
            }
          });
          
          // Prevent tooltips from showing on hover for all buttons
          allButtons.forEach((btn: HTMLElement) => {
            // Remove existing listeners and add new ones that prevent tooltips
            const preventTooltip = (e: Event) => {
              e.stopPropagation();
              // Immediately hide any tooltip that appears
              const tooltips = editorDom.querySelectorAll('.monaco-hover, .monaco-tooltip');
              tooltips.forEach((tooltip: HTMLElement) => {
                tooltip.style.display = 'none';
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
                tooltip.style.pointerEvents = 'none';
              });
            };
            
            btn.addEventListener('mouseenter', preventTooltip, true);
            btn.addEventListener('mouseover', preventTooltip, true);
            btn.addEventListener('hover', preventTooltip, true);
          });
        };
        
        // Disable tooltips when DOM changes (e.g., find widget opens)
        tooltipObserverRef.current = new MutationObserver(() => {
          setTimeout(disableAllTooltips, 10);
        });
        tooltipObserverRef.current.observe(editorDom, { childList: true, subtree: true });
        
        // Initial disable
        disableAllTooltips();
        
        // Also disable tooltips periodically as a fallback
        tooltipIntervalRef.current = setInterval(() => {
          disableAllTooltips();
        }, 500);
      }
    }, 100);

    // Run initial validation on the current content
    const model = editor.getModel();
    if (model) {
      runValidation(model.getValue());
    }
  };

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      setSourceHtml(value);

      // Run custom HTML validation
      runValidation(value);

      // Sync <title> back to subject line
      const titleMatch = value.match(/<title>([^<]*)<\/title>/i);
      if (titleMatch && titleMatch[1] !== settings.subjectLine) {
        setSettings({ subjectLine: titleMatch[1] });
      }
    }
  };

  const insertSnippet = (snippet: string) => {
    setSourceHtml(sourceHtml + '\n' + snippet);
    setToastVisible(true);
  };

  const handleToastClose = useCallback(() => setToastVisible(false), []);

  return (
    <div className={styles.sourceEditor}>
      <SnippetLibrary onInsert={insertSnippet} />

      <div className={styles.editorContainer}>
        <Editor
          height="100%"
          defaultLanguage="html"
          value={sourceHtml}
          onChange={handleEditorChange}
          onMount={handleEditorMount}
          theme="vs-light"
          options={{
            minimap: { enabled: false },
            overviewRulerLanes: 3,
            overviewRulerBorder: false,
            wordWrap: 'on',
            fontSize: 14,
            lineNumbers: 'on',
            folding: true,
            automaticLayout: true,
            formatOnPaste: true,
            formatOnType: true,

            // Auto-closing & linked editing
            autoClosingTags: true,
            linkedEditing: true,

            // Color decorators (inline swatches for hex/rgb)
            colorDecorators: true,
            colorDecoratorsActivatedOn: 'clickAndHover',

            // Bracket pair colorization & guides
            bracketPairColorization: { enabled: true },
            guides: {
              bracketPairs: true,
              indentation: true,
              highlightActiveIndentation: true,
            },

            // Sticky scroll (parent tags pinned at top)
            stickyScroll: { enabled: true },

            // Smooth scrolling
            smoothScrolling: true,

            // Quick suggestions (autocomplete as you type)
            quickSuggestions: true,
            suggestOnTriggerCharacters: true,
          }}
        />
      </div>

      <VariablesPanel />

      <Toast
        message="Snippet added to the end of your template"
        visible={toastVisible}
        onClose={handleToastClose}
      />
    </div>
  );
}

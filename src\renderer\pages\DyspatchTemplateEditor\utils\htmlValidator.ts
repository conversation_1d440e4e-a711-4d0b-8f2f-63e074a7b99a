export interface HtmlError {
  line: number;
  column: number;
  endLine: number;
  endColumn: number;
  message: string;
}

// Standard HTML void (self-closing) elements that don't need a closing tag
const VOID_ELEMENTS = new Set([
  'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input',
  'link', 'meta', 'param', 'source', 'track', 'wbr',
]);

// Tags that can optionally self-close or are typically not strictly paired in email HTML
const OPTIONAL_CLOSE_ELEMENTS = new Set([
  'li', 'dt', 'dd', 'p', 'option', 'thead', 'tbody', 'tfoot',
  'th', 'tr', 'td', 'colgroup',
]);

/**
 * Validates HTML for common structural errors:
 * - Unclosed tags (opening tag without matching closing tag)
 * - Extra closing tags (closing tag without matching opening tag)
 * - Mismatched tags (closing tag doesn't match the last opened tag)
 *
 * Tolerant of:
 * - Void/self-closing elements (br, img, meta, etc.)
 * - Conditional comments (<!--[if ...]> ... <![endif]-->)
 * - XML processing instructions (<?xml ...?>)
 * - Custom/unknown tag names (common in email HTML)
 */
export function validateHtml(html: string): HtmlError[] {
  const errors: HtmlError[] = [];
  const lines = html.split('\n');

  // Stack to track open tags: { tag, line, column }
  const stack: { tag: string; line: number; column: number }[] = [];

  // Strip conditional comments and CDATA sections to avoid false positives
  // e.g. <!--[if mso]> ... <![endif]--> are NOT real HTML tags
  const stripped = html
    .replace(/<!--\[if[^\]]*\]>[\s\S]*?<!\[endif\]-->/gi, (match) => {
      // Replace with same-length whitespace preserving newlines
      return match.replace(/[^\n]/g, ' ');
    })
    .replace(/<!\[if[^\]]*\]>[\s\S]*?<!\[endif\]>/gi, (match) => {
      return match.replace(/[^\n]/g, ' ');
    });

  // Regex to find all HTML tags (opening, closing, self-closing)
  const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9:-]*)\b[^>]*\/?>/g;
  let match: RegExpExecArray | null;

  while ((match = tagRegex.exec(stripped)) !== null) {
    const fullMatch = match[0];
    const tagName = match[1].toLowerCase();
    const offset = match.index;

    // Calculate line and column from offset
    const before = stripped.substring(0, offset);
    const line = (before.match(/\n/g) || []).length + 1;
    const lastNewline = before.lastIndexOf('\n');
    const column = offset - lastNewline; // 1-based

    const isSelfClosing = fullMatch.endsWith('/>');
    const isClosingTag = fullMatch.startsWith('</');

    // Skip void elements — they never need a closing tag
    if (VOID_ELEMENTS.has(tagName)) continue;

    // Skip self-closing tags
    if (isSelfClosing) continue;

    // Skip tags inside XML namespaces that are typically self-describing
    // e.g. <o:OfficeDocumentSettings>, <v:shape>, etc.
    if (tagName.includes(':')) continue;

    if (isClosingTag) {
      // Find the matching opening tag on the stack
      let found = false;
      for (let i = stack.length - 1; i >= 0; i--) {
        if (stack[i].tag === tagName) {
          // Pop everything above it — those are unclosed tags
          // But only report non-optional-close elements
          for (let j = stack.length - 1; j > i; j--) {
            const unclosed = stack[j];
            if (!OPTIONAL_CLOSE_ELEMENTS.has(unclosed.tag)) {
              errors.push({
                line: unclosed.line,
                column: unclosed.column,
                endLine: unclosed.line,
                endColumn: unclosed.column + unclosed.tag.length + 2,
                message: `Unclosed tag <${unclosed.tag}>`,
              });
            }
          }
          stack.splice(i); // Remove from index i onwards
          found = true;
          break;
        }
      }

      if (!found) {
        // Closing tag without a matching opening tag
        errors.push({
          line,
          column,
          endLine: line,
          endColumn: column + fullMatch.length,
          message: `Unexpected closing tag </${tagName}> — no matching opening tag found`,
        });
      }
    } else {
      // Opening tag — push to stack
      stack.push({ tag: tagName, line, column });
    }
  }

  // Any remaining tags on the stack are unclosed
  for (const unclosed of stack) {
    if (!OPTIONAL_CLOSE_ELEMENTS.has(unclosed.tag)) {
      errors.push({
        line: unclosed.line,
        column: unclosed.column,
        endLine: unclosed.line,
        endColumn: unclosed.column + unclosed.tag.length + 2,
        message: `Unclosed tag <${unclosed.tag}> — missing </${unclosed.tag}>`,
      });
    }
  }

  // --- CSS validation inside <style> blocks ---
  errors.push(...validateCssInStyleBlocks(html));

  return errors;
}

/**
 * Validates CSS inside <style>...</style> blocks for common errors:
 * - Unmatched braces (missing { or })
 * - Empty rule blocks (selector with no declarations)
 * - Declarations outside of braces (e.g. missing opening brace)
 * - Unclosed strings/quotes
 * - Invalid property syntax (missing colons, semicolons)
 * - Unclosed parentheses in functions
 * - Invalid @-rules
 */
function validateCssInStyleBlocks(html: string): HtmlError[] {
  const errors: HtmlError[] = [];
  const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
  let styleMatch: RegExpExecArray | null;

  while ((styleMatch = styleRegex.exec(html)) !== null) {
    const cssContent = styleMatch[1];
    const styleOffset = styleMatch.index + styleMatch[0].indexOf(cssContent);

    // Calculate the starting line of this CSS block in the overall HTML
    const beforeStyle = html.substring(0, styleOffset);
    const baseLineNumber = (beforeStyle.match(/\n/g) || []).length + 1;

    const cssErrors = validateCss(cssContent, baseLineNumber, beforeStyle);
    errors.push(...cssErrors);
  }

  return errors;
}

function validateCss(css: string, baseLineNumber: number, beforeStyle: string): HtmlError[] {
  const errors: HtmlError[] = [];
  const lines = css.split('\n');

  // Track positions for better error reporting
  let currentOffset = 0;

  // 1. Check for unclosed strings/quotes
  const stringErrors = validateCssStrings(css, baseLineNumber);
  errors.push(...stringErrors);

  // Strip CSS comments to avoid false positives
  const stripped = css.replace(/\/\*[\s\S]*?\*\//g, (match) =>
    match.replace(/[^\n]/g, ' ')
  );

  // Strip content inside strings to avoid false positives with braces in strings
  const noStrings = stripped
    .replace(/"(?:[^"\\]|\\.)*"/g, (m) => '"' + ' '.repeat(m.length - 2) + '"')
    .replace(/'(?:[^'\\]|\\.)*'/g, (m) => "'" + ' '.repeat(m.length - 2) + "'");

  const processedLines = noStrings.split('\n');
  let braceDepth = 0;
  const openBraceStack: { line: number; column: number }[] = [];
  const openParenStack: { line: number; column: number }[] = [];

  for (let i = 0; i < processedLines.length; i++) {
    const line = processedLines[i];
    const originalLine = lines[i];
    const absoluteLine = baseLineNumber + i;

    // Track parentheses for function calls like calc(), rgba(), etc.
    for (let col = 0; col < line.length; col++) {
      const ch = line[col];

      if (ch === '{') {
        braceDepth++;
        openBraceStack.push({ line: absoluteLine, column: col + 1 });
      } else if (ch === '}') {
        if (braceDepth <= 0) {
          errors.push({
            line: absoluteLine,
            column: col + 1,
            endLine: absoluteLine,
            endColumn: col + 2,
            message: 'CSS: Unexpected closing brace "}" — no matching opening brace',
          });
        } else {
          braceDepth--;
          openBraceStack.pop();
        }
      } else if (ch === '(') {
        openParenStack.push({ line: absoluteLine, column: col + 1 });
      } else if (ch === ')') {
        if (openParenStack.length === 0) {
          errors.push({
            line: absoluteLine,
            column: col + 1,
            endLine: absoluteLine,
            endColumn: col + 2,
            message: 'CSS: Unexpected closing parenthesis ")" — no matching opening parenthesis',
          });
        } else {
          openParenStack.pop();
        }
      }
    }

    // Check for property declarations outside of braces
    if (braceDepth === 0) {
      const trimmed = line.trim();
      if (
        trimmed.length > 0 &&
        !trimmed.startsWith('@') &&
        !trimmed.startsWith('/*') &&
        !trimmed.startsWith('*') &&
        !trimmed.endsWith('{') &&
        !trimmed.endsWith(',') &&
        !trimmed.endsWith('}') &&
        /^[^{}]+:[^{}]+;/.test(trimmed)
      ) {
        errors.push({
          line: absoluteLine,
          column: 1,
          endLine: absoluteLine,
          endColumn: line.length + 1,
          message: 'CSS: Declaration outside of a rule block — missing opening brace "{"?',
        });
      }
    }

    // Check for invalid property syntax (missing colon or semicolon in declarations)
    if (braceDepth > 0) {
      const trimmed = originalLine.trim();
      // Check for lines that look like properties but are malformed
      if (trimmed.length > 0 && !trimmed.startsWith('/*') && !trimmed.startsWith('*')) {
        // Skip selectors (lines ending with {, }, or ,) and @-rules
        const isSelector = trimmed.endsWith('{') || trimmed.endsWith(',') || trimmed.startsWith('@');
        // Skip pseudo-selectors and combinators (lines with :, >, +, ~ but no property pattern)
        const isPseudoSelector = /^[^{}]*[:>+~]\s*[^{}]*$/.test(trimmed) && !trimmed.match(/^\s*[a-zA-Z-]+\s*:/);
        
        if (!isSelector && !isPseudoSelector) {
          // Check for property without colon: "margin 10px" instead of "margin: 10px"
          // Only flag if it looks like a property declaration (word followed by value-like content)
          if (/^[a-zA-Z-]+\s+[^:;{}]+$/.test(trimmed) && !trimmed.includes(':')) {
            errors.push({
              line: absoluteLine,
              column: 1,
              endLine: absoluteLine,
              endColumn: trimmed.length + 1,
              message: 'CSS: Property declaration missing colon ":"',
            });
          }
          
          // Check for property without semicolon - only for actual property declarations
          // Pattern: property: value (not ending with ;, }, or {)
          if (trimmed.includes(':') && !trimmed.endsWith(';') && !trimmed.endsWith('}') && !trimmed.endsWith('{')) {
            // Check if this looks like a property declaration (property: value pattern)
            const propertyPattern = /^\s*[a-zA-Z-]+\s*:\s*[^;{}]+$/;
            if (propertyPattern.test(trimmed)) {
              const nextLine = processedLines[i + 1]?.trim() || '';
              // Only error if next line doesn't start a new rule, close the block, or continue the value
              // Allow continuation if next line doesn't look like a new property/selector
              if (nextLine && !nextLine.startsWith('}') && !nextLine.match(/^[a-zA-Z-]+\s*:/) && !nextLine.match(/^[a-zA-Z-@]/)) {
                // Check if it's a multi-line value (next line doesn't look like a new declaration)
                const looksLikeValueContinuation = /^[^a-zA-Z@]/.test(nextLine) || nextLine.includes('!important');
                if (!looksLikeValueContinuation) {
                  errors.push({
                    line: absoluteLine,
                    column: trimmed.length,
                    endLine: absoluteLine,
                    endColumn: trimmed.length + 1,
                    message: 'CSS: Property declaration missing semicolon ";"',
                  });
                }
              }
            }
          }
        }
      }
    }

    // Check for empty rule blocks (but skip @-rules like @media, @keyframes, etc.)
    if (braceDepth > 0) {
      const trimmedForEmptyCheck = originalLine.trim();
      // Skip @-rules - they can have empty blocks or nested rules
      const isAtRule = trimmedForEmptyCheck.startsWith('@');
      if (trimmedForEmptyCheck.endsWith('{') && !isAtRule) {
        // Check if the next non-empty line closes the brace
        let foundContent = false;
        for (let j = i + 1; j < processedLines.length && j <= i + 3; j++) {
          const nextLine = processedLines[j]?.trim() || '';
          if (nextLine === '}') {
            if (!foundContent) {
              errors.push({
                line: absoluteLine,
                column: trimmedForEmptyCheck.length - 1,
                endLine: absoluteLine,
                endColumn: trimmedForEmptyCheck.length,
                message: 'CSS: Empty rule block — no declarations found',
              });
            }
            break;
          } else if (nextLine.length > 0 && !nextLine.startsWith('/*')) {
            foundContent = true;
            break;
          }
        }
      }
    }
  }

  // Report any remaining unclosed braces
  for (const open of openBraceStack) {
    errors.push({
      line: open.line,
      column: open.column,
      endLine: open.line,
      endColumn: open.column + 1,
      message: 'CSS: Unclosed brace "{" — missing closing "}"',
    });
  }

  // Report any remaining unclosed parentheses
  for (const open of openParenStack) {
    errors.push({
      line: open.line,
      column: open.column,
      endLine: open.line,
      endColumn: open.column + 1,
      message: 'CSS: Unclosed parenthesis "(" — missing closing ")"',
    });
  }

  return errors;
}

/**
 * Validates CSS strings/quotes for unclosed strings
 */
function validateCssStrings(css: string, baseLineNumber: number): HtmlError[] {
  const errors: HtmlError[] = [];
  const lines = css.split('\n');
  let inDoubleQuote = false;
  let inSingleQuote = false;
  let quoteStartLine = 0;
  let quoteStartCol = 0;
  let escapeNext = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const absoluteLine = baseLineNumber + i;

    for (let col = 0; col < line.length; col++) {
      const ch = line[col];

      if (escapeNext) {
        escapeNext = false;
        continue;
      }

      if (ch === '\\') {
        escapeNext = true;
        continue;
      }

      if (ch === '"' && !inSingleQuote) {
        if (inDoubleQuote) {
          inDoubleQuote = false;
        } else {
          inDoubleQuote = true;
          quoteStartLine = absoluteLine;
          quoteStartCol = col + 1;
        }
      } else if (ch === "'" && !inDoubleQuote) {
        if (inSingleQuote) {
          inSingleQuote = false;
        } else {
          inSingleQuote = true;
          quoteStartLine = absoluteLine;
          quoteStartCol = col + 1;
        }
      }
    }

    // Check if string continues to next line (this is valid in CSS)
    // But if we reach the end of the CSS and string is still open, that's an error
    if (i === lines.length - 1) {
      if (inDoubleQuote || inSingleQuote) {
        errors.push({
          line: quoteStartLine,
          column: quoteStartCol,
          endLine: absoluteLine,
          endColumn: line.length + 1,
          message: `CSS: Unclosed ${inDoubleQuote ? 'double' : 'single'} quote — missing closing quote`,
        });
      }
    }
  }

  return errors;
}

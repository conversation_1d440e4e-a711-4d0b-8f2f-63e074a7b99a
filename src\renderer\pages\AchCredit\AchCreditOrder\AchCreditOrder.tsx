import { useEffect, useState } from "react";
import { useImmer } from "use-immer";
import Loader from "../../../components/common/Loader";
import MatPopup from "../../../components/common/MatPopup";
import useApproveRejectAchCreditOrder from "../../../hooks/useApproveRejectAchCreditOrder";
import useGetAchCreditOrder from "../../../hooks/useGetAchCreditOrder";
import { filterArrray, format2DecimalPlaces } from "../../../utils/helper";
import styles from "./AchCreditOrder.module.scss";
import ReactPaginate from "react-paginate";
import { Select, MenuItem } from "@mui/material";
import SearchBar from "../../../components/common/SearchBox/SearchBox";

const AchCreditOrder = () => {
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [modifiedAchCreditOrders, setModifiedAchCreditOrders] = useImmer([]);
  const [filteredAchCreditOrders, setFilteredAchCreditOrders] = useImmer([]);
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [selectedOrderData, setSelectedOrderData] = useImmer<{
    isApproved: 0 | 1 | null;
    achCrdit: any;
  }>({ isApproved: null, achCrdit: null });

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredAchCreditOrders.length / itemsPerPage);

  const { data: achCreditOrderData, isLoading: isAchCreditOrderLoading } =
    useGetAchCreditOrder();
  const {
    mutate: approveRejectAchCreditOrder,
    isLoading: isApproveRejectAchCreditOrderLoading,
    data: approveRejectAchCreditOrderData,
    error: approveRejectAchCreditOrderError,
  } = useApproveRejectAchCreditOrder();

  useEffect(() => {
    if (achCreditOrderData) {
      const _achCreditOrderData = achCreditOrderData.map((achCrdit: any) => {
        let status = "";

        if (achCrdit.is_active === 1) {
          if (achCrdit.is_ach_po_approved === null) {
            status = "Pending";
          } else if (achCrdit.is_ach_po_approved === 0) {
            status = "Rejected";
          } else if (achCrdit.is_ach_po_approved === 1) {
            status = "Approved";
          }
        } else if (
          achCrdit.is_active === 0 &&
          achCrdit.is_ach_po_approved === 0
        ) {
          status = "Rejected";
        }
        return { ...achCrdit, status };
      });
      if (_achCreditOrderData?.length > 0 && inputSearchValue.length !== 0) {
        search(inputSearchValue, _achCreditOrderData)
      }else{
        setModifiedAchCreditOrders(
          _achCreditOrderData ? _achCreditOrderData : []
        );
        setFilteredAchCreditOrders(
          _achCreditOrderData ? _achCreditOrderData : []
        );
      }
    } else {
      setModifiedAchCreditOrders([]);
      setFilteredAchCreditOrders([]);
    }
  }, [achCreditOrderData, inputSearchValue]);

  useEffect(() => {
    setCurrentPage(0);
    setItemOffset(0);
  }, [itemsPerPage]);

  useEffect(() => {
    if (
      approveRejectAchCreditOrderData &&
      !isApproveRejectAchCreditOrderLoading
    ) {
      if (!approveRejectAchCreditOrderError) {
        setApiResponseMessage(approveRejectAchCreditOrderData);
      }
    }
  }, [
    approveRejectAchCreditOrderData,
    isApproveRejectAchCreditOrderLoading,
    approveRejectAchCreditOrderError,
  ]);

  const search = (searchString: string, searchData:any = modifiedAchCreditOrders) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(searchString);
    if (searchString) {
      const _filterArrray = filterArrray(
        searchData,
        searchString.trim(),
        [
          "delivery_date",
          "freight_term",
          "buyer_internal_po",
          "buyer_po_number",
          "buyer_po_price",
          "sales_tax",
          "status",
        ]
      );
      if (_filterArrray?.length) {
        setFilteredAchCreditOrders(_filterArrray);
      } else {
        setFilteredAchCreditOrders([]);
      }
    } else {
      setFilteredAchCreditOrders(searchData);
    }
  };

  const onClickApproveRejectHandler = (isApproved: 0 | 1, achCrdit: any) => {
    setShowConfirmationPopup(true);
    setSelectedOrderData({ isApproved, achCrdit });
  };

  const confirmationPopupYes = () => {
    if (selectedOrderData) {
      approveRejectAchCreditOrder({
        data: {
          user_id: selectedOrderData?.achCrdit?.buyer_id ?? null,
          po_number: selectedOrderData?.achCrdit.buyer_po_number ?? null,
          is_ach_po_approved: selectedOrderData?.isApproved ? true : false,
        },
      });
    }

    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setSelectedOrderData({ isApproved: null, achCrdit: null });
  };

  const handlePageClick = (event: any) => {
    const newOffset =
      (event.selected * itemsPerPage) % filteredAchCreditOrders.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  return (
    <div className="contentMain">
      {isAchCreditOrderLoading || isApproveRejectAchCreditOrderLoading ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>
            <SearchBar 
              placeholder={"Search"} 
              value={inputSearchValue} 
              onChange={(event)=>search(event.target.value)}              
              onClear={()=>{setInputSearchValue('')}}            
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>Delivery Date</th>
                  <th>Freight Term</th>
                  <th>Internal PO Number</th>
                  <th>PO Number</th>
                  <th>Price</th>
                  <th>Sales Tax</th>
                  <th>Status</th>
                  <th colSpan={2}></th>
                </tr>
              </thead>
              <tbody>
                {filteredAchCreditOrders?.length ? (
                  filteredAchCreditOrders
                    .slice(itemOffset, endOffset)
                    .map((achCrdit: any, index: number) => (
                      <tr key={achCrdit.buyer_id + index}>
                        <td>{achCrdit.delivery_date}</td>
                        <td>{achCrdit.freight_term}</td>
                        <td>{achCrdit.buyer_internal_po}</td>
                        <td>{achCrdit.buyer_po_number}</td>
                        <td>{format2DecimalPlaces(achCrdit.buyer_po_price)}</td>
                        <td>{format2DecimalPlaces(achCrdit.sales_tax)}</td>
                        <td>{achCrdit.status}</td>
                        <td>
                          {achCrdit.is_ach_po_approved === null && (
                            <button
                              className={styles.approvalBtn}
                              onClick={() =>
                                onClickApproveRejectHandler(1, achCrdit)
                              }
                            >
                              Approve
                            </button>
                          )}
                        </td>
                        <td>
                          {achCrdit.is_ach_po_approved === null && (
                            <button
                              className={styles.rejectBtn}
                              onClick={() =>
                                onClickApproveRejectHandler(0, achCrdit)
                              }
                            >
                              Reject
                            </button>
                          )}
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={9} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
        </div>
      )}
      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>Do you want to continue ?</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
    </div>
  );
};

export default AchCreditOrder;

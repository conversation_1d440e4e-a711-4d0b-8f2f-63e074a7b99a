export type EditorMode = 'block' | 'source';
export type ViewMode = 'edit' | 'preview' | 'html';
export type DeviceSize = 'desktop' | 'mobile';

export interface Variable {
  name: string;
  defaultValue: string;
  type?: 'text' | 'collection';
}

export interface BlockContent {
  id: string;
  type: BlockType;
  content: string;
  styles?: BlockStyles;
  attributes?: Record<string, any>;
}

export type BlockType =
  | 'heading1'
  | 'heading2'
  | 'heading3'
  | 'paragraph'
  | 'button'
  | 'image'
  | 'spacer'
  | 'divider'
  | 'columns'
  | 'html';

export interface BlockStyles {
  padding?: string;
  margin?: string;
  backgroundColor?: string;
  color?: string;
  fontSize?: string;
  fontFamily?: string;
  textAlign?: 'left' | 'center' | 'right';
  borderRadius?: string;
  border?: string;
  width?: string;
  height?: string;
}

export interface EmailSettings {
  templateName: string;
  subjectLine: string;
  emailBackgroundColor: string;
  contentBackgroundColor: string;
  fontFamily: 'Arial' | 'Georgia' | 'Verdana';
  maxWidth: number;
}

export interface TemplateItem {
  templateid: string | number;
  templateName: string;
  templateDisplayName: string;
  subject: string;
  html_template: string;
}

export interface TemplateState {
  editorMode: EditorMode;
  viewMode: ViewMode;
  deviceSize: DeviceSize;
  blocks: BlockContent[];
  sourceHtml: string;
  variables: Variable[];
  collections: Record<string, any[]>;
  settings: EmailSettings;
  isDirty: boolean;
  isTemplateNameEditable: boolean;
  templateId: string;
  templateEvent: string; // Original event name from API (for existing templates)
  htmlErrorCount: number;
}

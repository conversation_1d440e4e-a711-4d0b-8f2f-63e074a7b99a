.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;

  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }
}

.closeBtn {
  padding: 4px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #666;
}

.modalContent {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.settingsForm {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  input,
  select {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
  }
}

.colorInput {
  display: flex;
  gap: 10px;

  input[type="color"] {
    width: 50px;
    height: 38px;
    padding: 2px;
    cursor: pointer;
  }

  input[type="text"] {
    flex: 1;
  }
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
}

.btnPrimary {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  background: #007bff;
  color: white;

  &:hover {
    background: #0056b3;
  }
}

import React from 'react';
import {
  Box,
  ToggleButtonGroup,
  ToggleButton,
  IconButton,
  Divider,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  CropFree as RectangleIcon,
  NearMe as SelectIcon,
  Delete as DeleteIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  CenterFocusStrong as ResetViewIcon,
  PanTool as PanIcon,
} from '@mui/icons-material';

const HotspotToolbar = ({ 
  selectedTool, 
  onToolChange, 
  onZoomIn, 
  onZoomOut, 
  onResetView,
  onDeleteSelected,
  hasSelection,
  disabled = false 
}) => {
  const tools = [
    { value: 'select', icon: <SelectIcon />, label: 'Select Tool', tooltip: 'Select and resize hotspots' },
    { value: 'rectangle', icon: <RectangleIcon />, label: 'Rectangle Tool', tooltip: 'Draw new rectangular hotspots' },
    { value: 'pan', icon: <PanIcon />, label: 'Pan Tool', tooltip: 'Pan around the canvas' },
  ];

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        p: 2,
        backgroundColor: 'white',
        border: '1px solid #e0e0e0',
        borderRadius: 1,
        mb: 2,
      }}
    >
      {/* Tool Selection */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography variant="body2" sx={{ color: '#666', minWidth: 'fit-content' }}>
          Tools:
        </Typography>
        <ToggleButtonGroup
          value={selectedTool}
          exclusive
          onChange={(e, value) => value && onToolChange(value)}
          size="small"
          disabled={disabled}
        >
          {tools.map((tool) => (
            <ToggleButton
              key={tool.value}
              value={tool.value}
              sx={{
                '&.Mui-selected': {
                  backgroundColor: '#415a77',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#1b263b',
                  },
                },
              }}
            >
              <Tooltip title={tool.tooltip} placement="top">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {tool.icon}
                  <Typography variant="caption" sx={{ display: { xs: 'none', sm: 'block' } }}>
                    {tool.label.replace(' Tool', '')}
                  </Typography>
                </Box>
              </Tooltip>
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      </Box>

      <Divider orientation="vertical" flexItem />

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography variant="body2" sx={{ color: '#666', minWidth: 'fit-content' }}>
          Actions:
        </Typography>
        
        <Tooltip title="Delete selected hotspot">
          <span>
            <IconButton
              onClick={onDeleteSelected}
              disabled={disabled || !hasSelection}
              size="small"
              sx={{
                color: hasSelection ? '#f44336' : '#ccc',
                '&:hover': {
                  backgroundColor: hasSelection ? 'rgba(244, 67, 54, 0.1)' : 'transparent',
                },
              }}
            >
              <DeleteIcon />
            </IconButton>
          </span>
        </Tooltip>
      </Box>

      <Divider orientation="vertical" flexItem />

      {/* View Controls */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography variant="body2" sx={{ color: '#666', minWidth: 'fit-content' }}>
          View:
        </Typography>
        
        <Tooltip title="Zoom in">
          <IconButton
            onClick={onZoomIn}
            disabled={disabled}
            size="small"
            sx={{ color: '#415a77' }}
          >
            <ZoomInIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Zoom out">
          <IconButton
            onClick={onZoomOut}
            disabled={disabled}
            size="small"
            sx={{ color: '#415a77' }}
          >
            <ZoomOutIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Reset view">
          <IconButton
            onClick={onResetView}
            disabled={disabled}
            size="small"
            sx={{ color: '#415a77' }}
          >
            <ResetViewIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Instructions */}
      <Box sx={{ ml: 'auto', display: { xs: 'none', md: 'block' } }}>
        <Typography variant="caption" sx={{ color: '#666' }}>
          {selectedTool === 'rectangle' && 'Click and drag to draw a hotspot'}
          {selectedTool === 'select' && 'Click hotspots to select and resize them'}
          {selectedTool === 'pan' && 'Drag to pan around the canvas'}
        </Typography>
      </Box>
    </Box>
  );
};

export default HotspotToolbar;

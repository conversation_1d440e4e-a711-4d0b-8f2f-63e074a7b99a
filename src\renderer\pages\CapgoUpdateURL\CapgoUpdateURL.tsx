import clsx from "clsx";
import Loader from "../../components/common/Loader";
import useCapgoUpdateURL from "../../hooks/useCapgoUpdateURL";
import styles from "./CapgoUpdateURL.module.scss";
import { useContext, useEffect, useState } from "react";
import { MenuItem, Select } from "@mui/material";
import { CommonCtx } from "../AppContainer";
import { confirmationPopupKeys} from "../../utils/constant";
import useDialogStore from "../../components/common/DialogPopup/DialogStore";

const CapgoUpdateURL = () => {
    const showPopupFormAnyComponent = useContext(CommonCtx);
    const [selectedOption, setSelectedOption] = useState('');
    const {
        data,
        error,
        isLoading,
        isError,
        mutationIsLoading,
        mutationData,
        mutate
    } = useCapgoUpdateURL();

    const {showCommonDialog, resetDialogStore } = useDialogStore();

    const handleSelectChange = (event: any) => {
        setSelectedOption(event.target.value);
    }


    useEffect(() => {
        if (data && !isLoading && !isError) {
            data.data.forEach((item: any) => {
                if (item.is_selected) {
                    setSelectedOption(item.id);
                }
            });
        }
    }, [data, isLoading, isError]);

    useEffect(() => {
        if(mutationData){
            showPopupFormAnyComponent(mutationData.data);
        }
    },[mutationData])

    function handleSubmit(): void {
        mutate({
            data: {
                id: Number(selectedOption)
            }
        });
    }

    const showCapgoUpdateUrlPopup = () => {
        showCommonDialog(null, confirmationPopupKeys.confirmationContent, null, resetDialogStore, 
          [{name: confirmationPopupKeys.confirmation.yes, action: ()=>{capgoUpdateUrlPopup()}},{name: confirmationPopupKeys.confirmation.no, action: resetDialogStore}])
       }
    
      const capgoUpdateUrlPopup = () => {
        handleSubmit();
        resetDialogStore()
      };

    return (
        <div className={styles.container}>
            {(isLoading || mutationIsLoading)? (
                <div className="loaderImg">
                    <Loader />
                </div>
            ) : (
                <>
                    <div className={styles.capGoUrlMain}>
                        <div className={styles.title}>Set Mobile Update Source</div>
                        <div className={styles.selectContainer}>
                            <Select
                               className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
                                value={selectedOption}
                                onChange={handleSelectChange}
                            >
                                 {data.data.map((item: any) => (
                                    <MenuItem key={item.id} value={item.id}>
                                        {item.key}
                                    </MenuItem>
                                    
                                ))}
                               
                            </Select>
                           
                            <button className={styles.btnSave} onClick={showCapgoUpdateUrlPopup}>Save</button>
                        </div>
                    </div>
                </>
            )}
        </div>
    )
};

export default CapgoUpdateURL;

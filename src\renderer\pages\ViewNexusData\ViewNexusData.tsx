import { useEffect, useRef, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import clsx from "clsx";
import "ag-grid-community/styles/ag-grid.css";
import Loader from "../../components/common/Loader";
import useGetSalesTaxNexusData from "../../hooks/useGetSalesTaxNexusData";
import styles from "./ViewNexusData.module.scss";
import { ReactComponent as ClearIcon } from '../../../assests/images/Close.svg';

const ViewNexusData = () => {
  const [rowData, setRowData] = useState<any[]>([]);
  const [filteredRowData, setFilteredRowData] = useState<any[]>([]);
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [isColDefReady, setIsColDefReady] = useState(false);
  const tableRef = useRef(null);
  const nexusCompartor = () => 0;

  const {
    data: salesTaxNexusData,
    isLoading: isSalesTaxNexusDataLoading,
    error: salesTaxNexusDataError,
  } = useGetSalesTaxNexusData();

  const [colDefs] = useState([
    {
      field: "state_name",
      headerName: "State Name",
      minWidth: 150,
      flex: 1,
    },
    {
      field: "economic_nexus_threshold",
      headerName: "Economic Nexus Threshold",
      minWidth: 200,
      flex: 1,
      cellRenderer: (props: any) => {
        return props?.data?.economic_nexus_threshold
          ? `$${props.data.economic_nexus_threshold.toLocaleString()}`
          : "-";
      },
    },
    {
      field: "economic_nexus_threshold_number_transactions",
      headerName: "Economic Nexus Threshold (Number of Transactions)",
      minWidth: 250,
      flex: 1,
      cellRenderer: (props: any) => {
        return props?.data?.economic_nexus_threshold_number_transactions
          ? props.data.economic_nexus_threshold_number_transactions
          : "-";
      },
    },
    {
      field: "total_closed_orders",
      headerName: "Total Closed Orders",
      minWidth: 180,
      flex: 1,
      cellRenderer: (props: any) => {
        return props?.data?.total_closed_orders
          ? props.data.total_closed_orders.toLocaleString()
          : "0";
      },
    },
    {
      field: "total_closed_orders_amount",
      headerName: "Total Closed Orders Amount",
      minWidth: 220,
      flex: 1,
      cellRenderer: (props: any) => {
        return props?.data?.total_closed_orders_amount
          ? `$${props.data.total_closed_orders_amount.toLocaleString()}`
          : "$0";
      },
    },
    {
      field: "nexus_threshold_exceeded",
      headerName: "Nexus Threshold Exceeded",
      minWidth: 200,
      flex: 1,
      cellRenderer: (props: any) => {
        return props?.data?.nexus_threshold_exceeded ? "Yes" : "No";
      },
    },
  ]);

  useEffect(() => {
    if (isSalesTaxNexusDataLoading) {
      return;
    }

    if (salesTaxNexusDataError) {
      setRowData([]);
      setFilteredRowData([]);
      return;
    }

    if (salesTaxNexusData && Array.isArray(salesTaxNexusData)) {
      setRowData(salesTaxNexusData);
      setFilteredRowData(salesTaxNexusData);
    } else {
      setRowData([]);
      setFilteredRowData([]);
    }
  }, [salesTaxNexusData, isSalesTaxNexusDataLoading, salesTaxNexusDataError]);

  useEffect(() => {
    if (!inputSearchValue.trim()) {
      setFilteredRowData(rowData);
      return;
    }

    const searchTerm = inputSearchValue.toLowerCase().trim();
    const filtered = rowData.filter((item) => {
      // Search across all fields using startsWith logic
      return (
        (item.state_name?.toString().toLowerCase().startsWith(searchTerm)) ||
        (item.economic_nexus_threshold?.toString().toLowerCase().startsWith(searchTerm)) ||
        (item.economic_nexus_threshold_number_transactions?.toString().toLowerCase().startsWith(searchTerm)) ||
        (item.total_closed_orders?.toString().toLowerCase().startsWith(searchTerm)) ||
        (item.total_closed_orders_amount?.toString().toLowerCase().startsWith(searchTerm)) ||
        (item.nexus_threshold_exceeded?.toString().toLowerCase().startsWith(searchTerm))
      );
    });
    setFilteredRowData(filtered);
  }, [inputSearchValue, rowData]);

  useEffect(() => {
    updatePinnedData();
  }, [tableRef?.current]);

  const updatePinnedData = () => {
    if (tableRef?.current) {
      if (isColDefReady) {
        return;
      }
      setIsColDefReady(true);
    }
  };

  const defaultColDef = {
    sortable: true,
    lockVisible: true,
    unSortIcon: true,
    cellStyle: { flex: 1 },
    headerClass: clsx(styles.header_initial),
    cellClass: styles.cell_default,
    wrapHeaderText: true,
    autoHeaderHeight: true,
    lockPinned: true,
  };

  const onGridReady = (event: any) => {
    event.api.sizeColumnsToFit();
  };

  const gridOptions = {
    icons: {
      sortAscending: '<span class="custom-sort-asc sorted"></span>',
      sortDescending: '<span class="custom-sort-desc sorted"></span>',
      sortUnSort: '<span class="custom-sort-none"></span>',
    },
  };

  const search = (searchValue: string) => {
    setInputSearchValue(searchValue);
  };

  const clearInput = () => {
    setInputSearchValue("");
  };

  return (
    <div className={styles.nexusDataContainer}>
      {isSalesTaxNexusDataLoading ? (
        <div className={styles.noDataFound}>
          <Loader />
        </div>
      ) : (
        <>
          <div className={styles.searchBox}>
            <div className={styles.searchContainer}>
              <input
                className={styles.searchInput}
                type="text"
                onChange={(e) => search(e.target.value)}
                placeholder="Search"
                value={inputSearchValue}
              />
              {inputSearchValue && (
                <button className={styles.clearInputIcon} onClick={clearInput}>
                  <ClearIcon />
                </button>
              )}
            </div>
          </div>
          <div
            ref={tableRef}
            className={clsx(styles.ag_theme_quartz, styles.agGridAdmin)}
            style={{
              height: "calc(100vh - 300px)",
              width: "100%",
              minHeight: "400px",
              flex: 1,
            }}
          >
            {isColDefReady && (
              <AgGridReact
                rowData={filteredRowData}
                columnDefs={colDefs}
                sideBar={true}
                suppressCellFocus={true}
                rowHeight={50}
                headerHeight={32}
                enableCellTextSelection={true}
                ensureDomOrder={true}
                defaultColDef={defaultColDef}
                embedFullWidthRows={true}
                onGridReady={onGridReady}
                gridOptions={gridOptions}
              />
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default ViewNexusData;

import { useEffect, useState, useRef } from "react";
import useGetChats from "../../hooks/useGetChats";
import useGetAllChats from "../../hooks/useGetAllChats";
import Loader from "../../components/common/Loader";
import { Select, MenuItem, SelectChangeEvent, IconButton, Tooltip, Menu, ListItemIcon, ListItemText, Autocomplete, TextField } from "@mui/material";
import styles from "./Chats.module.scss";
import usePostStartChat from "../../hooks/usePostStartChat";
import { normalizeMessage, cleanContentEditableHtml, formatDate, isScrollAtBottom, scrollToBottom } from "./chatUtils";
import AttachmentBubble from "./AttachmentBubble/AttachmentBubble";
import MessageBubble from "./MessageBubble/MessageBubble";
import leoProfanity from 'leo-profanity';
import { v4 as uuidv4 } from 'uuid';
import clsx from "clsx";

// Add these imports for the formatting icons
import FormatBoldIcon from '@mui/icons-material/FormatBold';
import FormatItalicIcon from '@mui/icons-material/FormatItalic';
import FormatUnderlinedIcon from '@mui/icons-material/FormatUnderlined';
import usePostSendMessage from "../../hooks/usePostSendMessage";
import useGetMessages from "../../hooks/useGetMessages";

// Add these imports for the message menu
import MoreVertIcon from '@mui/icons-material/MoreVert';
import BlockIcon from '@mui/icons-material/Block';
import DeleteIcon from '@mui/icons-material/Delete';
import usePostDeleteMessage from "../../hooks/usePostDeleteMessage";
import usePostBanUser from "../../hooks/usePostBanUser";
import { useChatWithVendorStore } from "@bryzos/giss-ui-library";

const ATTACHMENT = 'attachment';

const Chats = () => {
    const { data: chatsData, isLoading: isChatsLoading } = useGetAllChats();
    const [allChats, setAllChats] = useState<any[]>([]);
    const [selectedChat, setSelectedChat] = useState<string>("");
    const [chatMessages, setChatMessages] = useState<any[]>([]);
    const { mutateAsync: sendAdminMessage } = usePostSendMessage();
    const { mutateAsync: deleteMessage } = usePostDeleteMessage();
    const { mutateAsync: banUser } = usePostBanUser();
    const { mutateAsync: getMessages } = useGetMessages();
    const [showLoadeer, setShowLoadeer] = useState(false);

    // New state for message input
    const [messageText, setMessageText] = useState("");
    const contentEditableRef = useRef<HTMLDivElement>(null);

    // State for message menu
    const [selectedMessage, setSelectedMessage] = useState<any>(null);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const messageContainerRef = useRef<HTMLDivElement>(null);
    const [wasAtBottom, setWasAtBottom] = useState(true);

    const { newMessages, setNewMessages } = useChatWithVendorStore();

    useEffect(() => {
        if (chatsData) {
            const mappedChats = chatsData.map(item => ({
                poNumber: item.reference_id
            }));
            setAllChats(mappedChats);
        }
    }, [chatsData]);

    useEffect(() => {
        if (selectedChat) {
            setWasAtBottom(true);
            getChatDetails(selectedChat);
        }
    }, [selectedChat]);

    useEffect(() => {
        if (wasAtBottom) {
            setTimeout(() => {
                scrollToBottom(messageContainerRef.current);
            }, 100);
        }
    }, [chatMessages]);

    useEffect(() => {
        if (newMessages && newMessages.length > 0) {
            const newMessagesForThisChat = newMessages.filter(message => message.reference_id === selectedChat);
            if (newMessagesForThisChat.length === 0) return;
            if (newMessagesForThisChat.length > 0) {
                const mappedMessages = newMessagesForThisChat.map(message => normalizeMessage(message, 0, 'moderator'));

                mappedMessages.forEach(item => {
                    if (item.isMyMessage) {
                        const index = chatMessages.findIndex(mesg => mesg.id === item.id);
                        if (index >= 0) {
                            chatMessages[index].status = 'sent';
                            chatMessages[index].formattedTimestamp = item.formattedTimestamp;
                        } else {
                            chatMessages.push(item);
                        }

                    }
                    else {
                        chatMessages.push(item);
                    }

                })
                setChatMessages([...chatMessages]);
            }
            const leftOverMessages = newMessages.filter(message => message.reference_id !== selectedChat);
            setNewMessages([...leftOverMessages])
        }
    }, [newMessages]);

    // Handle scroll events to track if we're at the bottom
    const handleScroll = () => {
        const isBottom = isScrollAtBottom(messageContainerRef.current, 30);
        setWasAtBottom(isBottom);

        // // Hide indicator when scrolled to bottom
        // if (isBottom && showNewMessageIndicator) {
        //     setShowNewMessageIndicator(false);
        //     setNewMessageCount(0);
        // }
    };

    const getChatDetails = async (poNumber: string) => {
        setShowLoadeer(true);
        const rawMessages = await getMessages(poNumber);
        if (rawMessages && rawMessages.length > 0) {
            let messages = rawMessages.map(message => normalizeMessage(message, 0, 'moderator'));
            messages = messages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
            setChatMessages(messages);
        } else if (rawMessages?.length === 0) {
            setChatMessages([]);
        }
        setShowLoadeer(false);
    }

    const handleChatChange = (value: string | null) => {
        const selectedPoNumber = value;
        setSelectedChat(selectedPoNumber);
    };

    // New functions for text formatting
    const applyFormatting = (command: string) => {
        document.execCommand(command, false);
        contentEditableRef.current?.focus();
    };

    const handleContentChange = () => {
        if (contentEditableRef.current) {
            setMessageText(contentEditableRef.current.innerHTML);
        }
    };

    const handleSendMessage = async () => {
        if (!messageText.trim()) return;

        // Clean the HTML content
        const rawMessage = cleanContentEditableHtml(messageText);
        const messageContent = leoProfanity.clean(rawMessage);
        const timeStamp = formatDate(new Date());
        const id = uuidv4();

        setChatMessages(prevMessages => {
            return [...prevMessages, {
                id: id,
                text: messageContent,
                formattedTimestamp: timeStamp,
                status: 'sending',
                isMyMessage: true
            }]
        });

        // Here you would add code to send the message
        console.log('Sending message:', messageContent);

        // Clear the input field
        setMessageText("");
        if (contentEditableRef.current) {
            contentEditableRef.current.innerHTML = "";
        }

        const sendMessageData = await sendAdminMessage({
            id: id,
            reference_id: selectedChat,
            message: JSON.stringify({
                id: id,
                text: messageContent,
                formattedTimestamp: timeStamp
            })
        });
        console.log('send message data:', sendMessageData);
    }

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    // Message menu handlers
    const handleMenuClick = (event: React.MouseEvent<HTMLElement>, message: any) => {
        setAnchorEl(event.currentTarget);
        setSelectedMessage(message);
    };

    const handleMenuClose = () => {
        setAnchorEl(null);
        setSelectedMessage(null);
    };



    async function handleBanUser(event: MouseEvent<HTMLLIElement, MouseEvent>): void {
        console.log("ban sender of ", selectedMessage)
        const response = await banUser({
            user_id: selectedMessage.fromId
        })
        handleMenuClose();
        console.log('ban user response:', response);
    }

    async function handleDeleteMessage(event: MouseEvent<HTMLLIElement, MouseEvent>): void {
        const messageId = selectedMessage.messageId;
        const response = await deleteMessage({
            message_ids: [messageId],
            reference_id: selectedChat
        })
        handleMenuClose();
        setChatMessages(prev => {
            return prev.filter(message => message.messageId !== messageId)
        })
    }

    return (
        <div className="contentMain">
            <h1>Chats</h1>
            {(isChatsLoading || showLoadeer) ? (
                <div className="loaderImg">
                    <Loader />
                </div>
            ) : (
                <div>
                    <p>Select a PO Number to open chat:</p>

                    <Autocomplete
                        options={allChats.map((chat) => chat.poNumber)}
                        value={selectedChat || null}
                        onChange={(event, newValue) => handleChatChange(newValue)}
                        renderInput={(params) => (
                            <TextField {...params} label="Select a PO Number" variant="outlined" />
                        )}
                        sx={{ minWidth: 240 }}
                    />

                </div>
            )}
            {selectedChat && chatMessages?.length > 0 ? (
                <div>
                    <h2>Chat with {selectedChat}</h2>
                    <div
                        onScroll={handleScroll}
                        className={styles.messagesContainer}
                        ref={messageContainerRef}>
                        {chatMessages.map((message) => (
                            <div key={message.id} className={clsx(
                                styles.messageWrapper,
                                message.isMyMessage ? styles.myMessageWrapper : styles.othersMessageWrapper
                            )}>
                                {/* Message Header */}
                                <div className={clsx(styles.messageHeader, message.role === 'buyer' ? styles.buyerMessageHeader : message.role === 'seller' ? styles.sellerMessageHeader : styles.adminMessageHeader)}>
                                    <div className={styles.messageTimestamp}>
                                        {message.formattedTimestamp} <b>{message.fromName}</b> ({
                                            message.role === 'buyer' ? 'Buyer' : message.role === 'seller' ? 'Seller' : 'Admin'
                                        })
                                    </div>
                                    <div className={styles.messageActions}>
                                        <IconButton
                                            size="small"
                                            onClick={(e) => handleMenuClick(e, message)}
                                            className={styles.menuButton}
                                        >
                                            <MoreVertIcon fontSize="small" />
                                        </IconButton>
                                    </div>
                                </div>

                                {/* Message Content */}
                                <div className={clsx(
                                    styles.messageContent,
                                    message.isMyMessage ? styles.myMessageContent : styles.othersMessageContent
                                )}>
                                    {message.type === ATTACHMENT ? (
                                        <div className={styles.attachmentWrapper}>
                                            <AttachmentBubble
                                                name={message.name}
                                                extension={message.extension}
                                                url={message.url}
                                                isMyMessage={message.isMyMessage}
                                                isImgix={false}
                                            />
                                        </div>
                                    ) : (
                                        <div className={styles.textMessageWrapper}>
                                            <MessageBubble
                                                text={message.text}
                                                isMyMessage={message.isMyMessage}
                                            />
                                        </div>
                                    )}
                                </div>

                            </div>
                        ))}
                    </div>

                    {/* Message actions menu */}
                    <Menu
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleMenuClose}
                        anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                        }}
                        transformOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                        }}
                    >
                        <MenuItem onClick={handleBanUser} disabled={selectedMessage?.isMyMessage}>
                            <ListItemIcon>
                                <BlockIcon fontSize="small" />
                            </ListItemIcon>
                            <ListItemText>Ban User</ListItemText>
                        </MenuItem>
                        <MenuItem onClick={handleDeleteMessage}>
                            <ListItemIcon>
                                <DeleteIcon fontSize="small" />
                            </ListItemIcon>
                            <ListItemText>Delete Message</ListItemText>
                        </MenuItem>
                    </Menu>

                    {/* New message input area with formatting options */}
                    <div className={styles.messageInputContainer}>
                        <div className={styles.formattingToolbar}>
                            <Tooltip title="Bold">
                                <IconButton onClick={() => applyFormatting('bold')}>
                                    <FormatBoldIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title="Italic">
                                <IconButton onClick={() => applyFormatting('italic')}>
                                    <FormatItalicIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title="Underline">
                                <IconButton onClick={() => applyFormatting('underline')}>
                                    <FormatUnderlinedIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                        <div
                            ref={contentEditableRef}
                            className={styles.contentEditable}
                            contentEditable
                            onInput={handleContentChange}
                            onKeyDown={handleKeyDown}
                            placeholder="Type a message..."
                        />
                        <button
                            className={styles.sendButton}
                            onClick={handleSendMessage}
                            disabled={!messageText.trim()}
                        >
                            Send
                        </button>
                    </div>
                </div>
            ) : (selectedChat ? (<div><p>No Message to show.</p></div>) : (<div><p>Select a PO Number to open chat.</p></div>))}
        </div>
    );
};

export default Chats;
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import CheckIcon from '@mui/icons-material/Check';
import { useTemplateStore } from '../stores/templateStore';
import { generateEmailHtml } from '../utils/emailGenerator';
import { saveAs } from 'file-saver';
import { html_beautify } from 'js-beautify';
import { useState } from 'react';
import styles from './HtmlCodeView.module.scss';

export function HtmlCodeView() {
  const { blocks, sourceHtml, settings, variables, collections, editorMode } = useTemplateStore();
  const [copied, setCopied] = useState(false);

  const getHtml = () => {
    const html = editorMode === 'block'
      ? generateEmailHtml(blocks, settings, variables, collections, false)
      : sourceHtml;

    return html_beautify(html, {
      indent_size: 2,
      wrap_line_length: 120,
      preserve_newlines: true,
      max_preserve_newlines: 2,
    });
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(getHtml());
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleDownload = () => {
    const html = getHtml();
    const blob = new Blob([html], { type: 'text/html;charset=utf-8' });
    // Use templateDisplayName exactly as it is (stored in settings.templateName)
    // Only remove characters that are truly problematic for filenames: / \ : * ? " < > |
    const filename = settings.templateName
      .trim()
      .replace(/[/\\:*?"<>|]/g, '') // Remove only truly problematic characters
      .trim() || 'template';
    saveAs(blob, `${filename}.html`);
  };

  const checkCompatibility = () => {
    const html = getHtml();
    return {
      gmail: true,
      outlook: html.includes('[if mso]'),
      apple: html.includes('disable-message-reformatting'),
      responsive: html.includes('@media'),
      liquid: html.includes('{{') || html.includes('{%'),
      mso: html.includes('[if mso]'),
    };
  };

  const compatibility = checkCompatibility();

  return (
    <div className={styles.htmlCodeView}>
      <div className={styles.codeToolbar}>
        <div className={styles.compatibilityBadges}>
          {compatibility.gmail && <span className={styles.badge}>Gmail</span>}
          {compatibility.outlook && <span className={styles.badge}>Outlook</span>}
          {compatibility.apple && <span className={styles.badge}>Apple Mail</span>}
          {compatibility.responsive && <span className={styles.badge}>Responsive</span>}
          {compatibility.liquid && <span className={styles.badge}>Liquid</span>}
          {compatibility.mso && <span className={styles.badge}>MSO Conditionals</span>}
        </div>

        <div className={styles.codeActions}>
          <button
            className={styles.actionBtn}
            onClick={handleCopy}
            title="Copy to clipboard"
          >
            {copied ? <CheckIcon sx={{ fontSize: 18 }} /> : <ContentCopyIcon sx={{ fontSize: 18 }} />}
            {copied ? 'Copied!' : 'Copy'}
          </button>
          <button
            className={styles.actionBtn}
            onClick={handleDownload}
            title="Download HTML"
          >
            <DownloadIcon sx={{ fontSize: 18 }} />
            Download
          </button>
        </div>
      </div>

      <div className={styles.codeContainer}>
        <pre className={styles.codeDisplay}>
          <code>{getHtml()}</code>
        </pre>
      </div>
    </div>
  );
}

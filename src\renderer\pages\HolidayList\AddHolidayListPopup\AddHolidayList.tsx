import React, { useContext, useEffect, useRef, useState } from 'react';
import {Dialog, MenuItem, Select} from '@mui/material';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import styles from "../HolidayList.module.scss";
import { useGetPublicHolidays } from '../../../hooks/useGetPublicHolidays';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { addHolidayDatesFormSchema, addHolidayDatesFormSchemaType } from '../../../models/addHolidayDate.model';
import { yupResolver } from '@hookform/resolvers/yup';
import clsx from 'clsx';
import { UseMutateAsyncFunction } from '@tanstack/react-query';
import { ReactComponent as DelIcon } from '../../../../assests/images/close-icon.svg';
import ConfiramtionBox from '../../../components/common/ConfiramtionBox';
import Loader from '../../../components/common/Loader';
import { initial } from 'lodash-es';
import { CommonCtx } from '../../AppContainer';
import CustomDatePicker from '../../../components/common/CustomDatePicker';
import { dateTimeFormat } from '@bryzos/giss-ui-library';
import TimePickerComponent from '../../../components/common/TimePicker/TimePicker';
import CustomTimePicker from '../../../components/common/TimePicker/CustomTimePicker';

dayjs.extend(isSameOrAfter);

const MenuProps = {
    classes:{
      paper:'timerWrapper'
    }
  };
  
const defaultHolidaySchema = {
    holiday_date: '',
    description: null,
    daysToSkip:1,
    daysToSkipBefore:1,
    daysToSkipAfter:1,
    isThanksgiving: false,
    holidayStartTime: "00:00",
    dayBeforeHolidayStartTime: "00:00",
    dayAfterHolidayStartTime:'00:00',
    showDayBefore: true,
    removeDayBeforeHoliday: false,
    removeDayAfterHoliday: false
}

const holidayStatusOptions = [
    {
        title: 'Day before holiday',
        value: 0
    },
    {
        title: 'Holiday',
        value: 1
    },
    {
        title: 'Day after holiday',
        value: 2
    }
]

const AddHolidayListPopup = ({ saveData, addedHolidays }: { saveData: UseMutateAsyncFunction<any, unknown, any, unknown>,addedHolidays:any}) => {
const showPopupFormAnyComponent = useContext(CommonCtx)
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        getValues,
        trigger,
        reset,
        control,
        formState: { isValid, errors },
    } = useForm<addHolidayDatesFormSchemaType>({
        defaultValues: {
            data: [defaultHolidaySchema]
        },
        resolver: yupResolver(addHolidayDatesFormSchema),
        mode: "onBlur",
    });

    const { fields, append, remove } = useFieldArray({
        control,
        name: "data"
    });

    const [open, setOpen] = useState(false);
    const handleClickOpen = () => setOpen(true);
    const handleClose = () => {
        setDisabledDates([]);
        reset();
        setOpen(false)
    };
    const [disabledDates, setDisabledDates] = useState<any[]>([]);
    const [showConfirmationPopup, setShowConfirmationPopup] = useState(false)
    const [calendarInFocusIndex, setCalendarInFocusIndex] = useState(null)

    useEffect(() => {
        if (addedHolidays && addedHolidays.length > 0) {
            initialize();
        }
    }, [addedHolidays]);


    useEffect(()=>{
        if(open === true){
            if (addedHolidays && addedHolidays.length > 0) {
                initialize();
            }
        }
    },[open]);

    const initialize = ()=>{
        if (addedHolidays && addedHolidays.length > 0) {
            const today = dayjs().startOf('day');
            const updatedDisabledDates = addedHolidays
                .filter(holiday =>dayjs(holiday.holiday_date).isSameOrAfter(today))
                .map(holiday => dayjs(holiday.holiday_date).startOf('day'));
            
            
            setDisabledDates(updatedDisabledDates);
        }
    }

    const { mutateAsync, data, isLoading, isError } = useGetPublicHolidays();

    const confirmationPopupClose = () => {
        setShowConfirmationPopup(false);
    }

    const confirmationYes = async () => {
        setShowConfirmationPopup(false);
        getPublicHolidays();
    }

    const getHolidaysHandler = () => {

        if(getValues('data')?.length>1||getValues('data')[0]?.holiday_date){
            setShowConfirmationPopup(true);
        }else{
            getPublicHolidays();
        }
    }

    function isThanksgivingDay(dateString: string): boolean {
        const date = new Date(dateString);
        
        if (date.getMonth() !== 10) { 
            return false;
        }
    
        const year = date.getFullYear();
    
        const firstDayOfNovember = new Date(year, 10, 1);
        
        const firstDayWeekDay = firstDayOfNovember.getDay();
        
        const offsetToThursday = (4 - firstDayWeekDay + 7) % 7;
        const firstThursday = 1 + offsetToThursday;
        const fourthThursday = firstThursday + 21;

        return date.getDate() === fourthThursday;
    }

    const hasThanksgivingDay = ()=>{
        return getValues('data')?.some(holiday => holiday.isThanksgiving);
    }

    const getPublicHolidays = async () => {
        try {
            const holidayList = await mutateAsync();
    
            const today = dayjs().startOf('day').add(1, 'day');
            const addedHolidayDates = addedHolidays.map(holiday => holiday.holiday_date);
    
            const isWeekend = (date) => date.day() === 0 || date.day() === 6;
    
            const uniqueHolidayList = holidayList.reduce((acc, current) => {
                const existingHoliday = acc.find(holiday => holiday.date === current.date);
                
                if (existingHoliday) {
                    if (!existingHoliday.name.includes(current.name)) {
                        existingHoliday.name += `, ${current.name}`;
                    }
                } else {
                    acc.push(current);
                }
    
                return acc;
            }, []);
    
            const filterHolidayList = uniqueHolidayList
                .filter(holiday => {
                    const holidayDate = dayjs(holiday.date);
                    return (
                        holidayDate.isSameOrAfter(today) &&
                        !addedHolidayDates.some(addedDate => dayjs(addedDate).isSame(holidayDate, 'day')) &&
                        !isWeekend(holidayDate)  
                    );
                })
                .map(holiday => ({
                    ...defaultHolidaySchema,
                    holiday_date: holiday.date,
                    description: holiday.name
                }));
                if (filterHolidayList.length === 0) {
                    showPopupFormAnyComponent("No new holiday to add.");
                } else {
                    setValue('data', filterHolidayList);
                    filterHolidayList.forEach((holiday, i) => {
                        customDatePickerOnChange(dayjs(holiday.holiday_date), i)
                    });
                }
        } catch (error) {
            console.error("Error fetching holidays:", error);
        }
    }

    const addLine = () => {
        append(defaultHolidaySchema)
    }

    const removeLine = (i) => {
        const date = getValues('data')[i]?.holiday_date;
        setDisabledDates(checkDisableDateSameOrNot(date, i));
        remove(i);
        if(watch('data')?.length === 0){
            addLine();
        }
    }

    const getPayload = (data) => {
        const result: any[] = [];

        data.data.forEach(holiday => {
            const { daysToSkipAfter, daysToSkipBefore, daysToSkip, description, holiday_date, isThanksgiving, holidayStartTime, dayBeforeHolidayStartTime, dayAfterHolidayStartTime, holidayStatus, removeDayBeforeHoliday, removeDayAfterHoliday} = holiday;
            
            if(!removeDayBeforeHoliday)
                result.push({
                    days_to_skip: daysToSkipBefore,
                    description: `Day Before ${description?description:'Holiday'}`,
                    holiday_date: dayjs(holiday_date).subtract(1, 'day').format(dateTimeFormat.isoFormat),
                    is_day_before: holidayStatus[0] === 0 ? 1 : 0,
                    is_day_after: holidayStatus[0] === 2 ? 1 : 0,
                    holiday_start_time: dayBeforeHolidayStartTime  
                });

            result.push({
                days_to_skip: daysToSkip,
                description: `${description?description:'Holiday'}`,
                holiday_date: holiday_date,
                is_day_before: holidayStatus[1] === 0 ? 1 : 0,
                is_day_after: holidayStatus[1] === 2 ? 1 : 0,
                holiday_start_time: holidayStartTime 
            });

            if(isThanksgiving && !removeDayAfterHoliday){
                result.push({
                    days_to_skip: daysToSkipAfter,
                    description: `Day After ${description?description:'Holiday'}`,
                    holiday_date: dayjs(holiday_date).add(1, 'day').format(dateTimeFormat.isoFormat),
                    is_day_before: holidayStatus[2] === 0 ? 1 : 0,
                    is_day_after: holidayStatus[2] === 2 ? 1 : 0,
                    holiday_start_time: dayAfterHolidayStartTime  
                });
            }
        });

        return result;
    }

    const submit = (data) => {
        if(data?.data){
            const payload = getPayload(data);
            saveData(payload);
            handleClose();
        }
    }

    const disableDates = (date) => {
        if(disabledDates?.some(disabledDate => disabledDate?.isSame(date, 'day'))){
            return true;
        }
        const day = date.day();
        return day === 0 || day === 6;
    };

    const getPreviousDate = (date)=>{
        return dayjs(date).subtract(1, "day").format(dateTimeFormat.shortUSFormat);
    }

    const getNextDate = (date)=>{
        return dayjs(date).add(1, "day").format(dateTimeFormat.shortUSFormat);
    }

    const filterDisabledDates = (date) => {
        setDisabledDates(prev => 
            prev.filter(disabledDate => !disabledDate.isSame(dayjs(date), 'day'))
        );
    }

    const checkDisableDateSameOrNot = (targetDate, i) => {
        if(targetDate === null) return disabledDates;
        return disabledDates.filter(compareDate => {
            return !(compareDate.isSame(dayjs(targetDate), 'day') || (compareDate.isSame(dayjs(getPreviousDate(targetDate)), 'day') && !watch(`data.${i}.removeDayBeforeHoliday`)) || (compareDate.isSame(dayjs(getNextDate(targetDate)), 'day') && isThanksgivingDay(getNextDate(targetDate)) && !watch(`data.${i}.removeDayAfterHoliday`))); 
        })
    }

    const customDatePickerOnChange = (newValue, i, previouslySelectedValue: null | string = null) => {
        let isRemoveDayBefore = false;
        let isRemoveDayAfter = false;
        const previousDate = dayjs(getPreviousDate(newValue));
        const nextDate = dayjs(getNextDate(newValue));
        const formattedNewValue = newValue?.format(dateTimeFormat.isoFormat);
        const updateDisabledDates = checkDisableDateSameOrNot(previouslySelectedValue, i)
        updateDisabledDates.push(newValue);
        updateDisabledDates.forEach(date => {
            if(date.isSame(previousDate, 'day') || previousDate.day() === 0){
                isRemoveDayBefore = true;
            }
            if(date.isSame(nextDate, 'day') || nextDate.day() === 6){
                isRemoveDayAfter = true;
            }
        });
        if(!isRemoveDayBefore) updateDisabledDates.push(previousDate)
        if(!isRemoveDayAfter && isThanksgivingDay(formattedNewValue)) updateDisabledDates.push(nextDate)
        setValue(`data.${i}.removeDayBeforeHoliday`, isRemoveDayBefore)
        setValue(`data.${i}.removeDayAfterHoliday`, isRemoveDayAfter)
        setValue(`data.${i}.isThanksgiving`, isThanksgivingDay(formattedNewValue))
        setDisabledDates([...updateDisabledDates])
        setValue(`data.${i}.holidayStatus`, [0,1,2])
    }
    return (
        <div>
            <button className={styles.addPopupBtn} onClick={handleClickOpen}>
                Add Holiday List
            </button>
            <Dialog
                open={open}
                classes={{
                    paper: styles.addHolidayPopup,
                }}
                onClose={(event, reason) => {
                    if (reason !== "backdropClick") {
                       handleClose();
                     }
                  }}
              >
                {
                    isLoading?<div className={styles.noDataFound}><Loader /></div>:
                <>
                <div className={styles.buttonContainer}>
                    <button className={styles.addLines} onClick={addLine}>Add Lines</button>
                    <button className={styles.getHolidays} onClick={getHolidaysHandler}>Get Holidays Up Till {dayjs().add(1, 'year').year()}</button>
                </div>

                <div className={styles.inputSection}>
                <div className={clsx(styles.inputRow, styles.addHolidaysHeading)}>
                    <span>Description</span>
                    <span>Date</span>
                    <span>Days to Skip</span>
                    <span>Timer</span>
                    <span>Holiday Status</span>
                </div>
                    {fields.map((holiday, i) => (<div className={styles.inputGroup}  key={i}>
                        {(!!getValues(`data.${i}.holiday_date`) && !watch(`data.${i}.removeDayBeforeHoliday`)) &&
                        <div className={styles.inputRow}>
                            <span className={styles.addedHolidaysInput1}>Day Before {getValues(`data.${i}.description`)?getValues(`data.${i}.description`):"Holiday"}</span>
                            <span className={styles.addedHolidaysInput1}>{getPreviousDate(getValues(`data.${i}.holiday_date`))}</span>
                            <input
                                {...register(`data.${i}.daysToSkipBefore`)}
                                value={watch(`data.${i}.daysToSkipBefore`) ?? ''}
                                onChange={(e)=>{
                                    e.target.value =  e.target.value.replace(/\D/g, '').replace(/^0+/, '');
                                    register(`data.${i}.daysToSkipBefore`).onChange(e)
                                }}
                                className={styles.InputField}
                                placeholder="Days To Skip"
                            />
                            <Controller
                                control={control}
                                rules={{
                                    required: true,
                                }}
                                name={register(`data.${i}.dayBeforeHolidayStartTime`).name}
                                render={({ field: { value, onChange }, fieldState: { error } }) => {
                                    const customTimePickerOnChange = (newValue:string) => {
                                        onChange(newValue); 
                                    }
                                    return (
                                        <CustomTimePicker
                                            showMinutes={false}
                                            value="00:00"
                                            onChange={customTimePickerOnChange} ></CustomTimePicker>
                                    )
                                }}
                            />
                            
                            
                                <Select
                                    value={watch(`data.${i}.holidayStatus.${0}`)}
                                    onChange={(event) => {
                                        setValue(`data.${i}.holidayStatus.${0}`, event.target.value)
                                    }}
                                    className='customeTimerSelect'
                                    MenuProps={MenuProps}
                                    defaultValue={0}
                                    disabled={true}
                                >
                                    {holidayStatusOptions.map((item, index) => (
                                        <MenuItem
                                            key={item.title + index}
                                            value={item.value}
                                        >
                                            <span dangerouslySetInnerHTML={{ __html: item.title }}></span>
                                        </MenuItem>
                                    ))}
                                </Select>
                            <button className={styles.delRow} onClick={() => { setValue(`data.${i}.removeDayBeforeHoliday`, true); filterDisabledDates(dayjs(getPreviousDate(getValues(`data.${i}.holiday_date`))))  }}><DelIcon/></button>
                        </div>
                        }
                        <div className={styles.inputRow}>
                            <input
                                {...register(`data.${i}.description`)}
                                value={watch(`data.${i}.description`) ?? ''}
                                className={styles.InputField}
                                placeholder="Description"
                                onKeyDown={(e)=>{
                                    if (e.key === 'Tab') {
                                        setCalendarInFocusIndex(i)
                                    }
                                }}
                            />
                            <Controller
                                control={control}
                                rules={{
                                    required: true,
                                }}
                                name={register(`data.${i}.holiday_date`).name}
                                render={({ field: { value, onChange }, fieldState: { error } }) => {
                                    const datePickerValue = watch(`data.${i}.holiday_date`) ? dayjs(watch(`data.${i}.holiday_date`)) : null;
                                    return (
                                        <CustomDatePicker 
                                            disableDates={disableDates} 
                                            value={datePickerValue} 
                                            onChange={(newValue)=>{
                                                customDatePickerOnChange(newValue, i, watch(`data.${i}.holiday_date`)); 
                                                onChange(newValue?.format(dateTimeFormat.isoFormat)); 
                                            }} 
                                            format={dateTimeFormat.shortUSFormat} 
                                            openOnFocus={i===calendarInFocusIndex} 
                                            onClose={()=>{ setCalendarInFocusIndex(null)}} 
                                            minDate={dayjs()} /> 
                                    )
                                }}
                            />
                            <input
                                {...register(`data.${i}.daysToSkip`)}
                                value={watch(`data.${i}.daysToSkip`) ?? ''}
                                className={styles.InputField}
                                placeholder="Days To Skip"
                                onChange={(e)=>{
                                    e.target.value =  e.target.value.replace(/\D/g, '').replace(/^0+/, '');
                                    register(`data.${i}.daysToSkip`).onChange(e)
                                }}
                            />
                            <Controller
                                control={control}
                                rules={{
                                    required: true,
                                }}
                                name={register(`data.${i}.holidayStartTime`).name}
                                render={({ field: { value, onChange }, fieldState: { error } }) => {
                                    const customTimePickerOnChange = (newValue:string) => {
                                        onChange(newValue); 
                                    }
                                    return (
                                        <CustomTimePicker
                                            showMinutes={false}
                                            value="00:00"
                                            onChange={customTimePickerOnChange} ></CustomTimePicker>
                                    )
                                }}
                            />
                               
                               <Select
                                    value={watch(`data.${i}.holidayStatus.${1}`)}
                                    onChange={(event) => {
                                        setValue(`data.${i}.holidayStatus.${1}`, event.target.value)
                                    }}
                                    className='customeTimerSelect'
                                    MenuProps={MenuProps}
                                    defaultValue={1}
                                >
                                    {holidayStatusOptions.map((item, index) => (
                                        <MenuItem
                                            key={item.title + index}
                                            value={item.value}
                                        >
                                            <span dangerouslySetInnerHTML={{ __html: item.title }}></span>
                                        </MenuItem>
                                    ))}
                                </Select>

                            {<button className={styles.delRow} onClick={() => removeLine(i)}><DelIcon/></button>}
                        </div>
                        {(getValues(`data.${i}.holiday_date`) && isThanksgivingDay(getValues(`data.${i}.holiday_date`)) && !watch(`data.${i}.removeDayAfterHoliday`)) &&
                        <div className={styles.inputRow}>
                            <span className={styles.addedHolidaysInput1}>Day After {getValues(`data.${i}.description`)?getValues(`data.${i}.description`):"Holiday"}</span>
                            <span className={styles.addedHolidaysInput1}>{getNextDate(getValues(`data.${i}.holiday_date`))}</span>
                            <input
                                {...register(`data.${i}.daysToSkipAfter`)}
                                value={watch(`data.${i}.daysToSkipAfter`) ?? ''}
                                className={styles.InputField}
                                placeholder="Days To Skip"
                                onChange={(e)=>{
                                    e.target.value =  e.target.value.replace(/\D/g, '').replace(/^0+/, '');
                                    register(`data.${i}.daysToSkipAfter`).onChange(e)
                                }}
                            />
                            
                            <Controller
                                control={control}
                                rules={{
                                    required: true,
                                }}
                                name={register(`data.${i}.dayAfterHolidayStartTime`).name}
                                render={({ field: { value, onChange }, fieldState: { error } }) => {
                                    const customTimePickerOnChange = (newValue:string) => {
                                        onChange(newValue); 
                                    }
                                    return (
                                        <CustomTimePicker
                                            showMinutes={false}
                                            value="00:00"
                                            onChange={customTimePickerOnChange} ></CustomTimePicker>
                                    )
                                }}
                            />
                            
                            <Select
                                    value={watch(`data.${i}.holidayStatus.${2}`)}
                                    onChange={(event) => {
                                        setValue(`data.${i}.holidayStatus.${2}`, event.target.value)
                                    }}
                                    className='customeTimerSelect'
                                    MenuProps={MenuProps}
                                    defaultValue={2}
                                    disabled={true}
                                >
                                    {holidayStatusOptions.map((item, index) => (
                                        <MenuItem
                                            key={item.title + index}
                                            value={item.value}
                                        >
                                            <span dangerouslySetInnerHTML={{ __html: item.title }}></span>
                                        </MenuItem>
                                    ))}
                                </Select>
                            <button className={styles.delRow} onClick={() => { setValue(`data.${i}.removeDayAfterHoliday`, true); filterDisabledDates(dayjs(getNextDate(getValues(`data.${i}.holiday_date`)))) }}><DelIcon/></button>
                        </div>}
                        </div>
                    ))}
                </div>

                <div className={styles.btnSection}>
                    <button onClick={handleClose}>Cancel</button>
                    <button onClick={handleSubmit(submit)} disabled={!isValid}>Add</button>
                </div>
                </>
                }

            </Dialog>
            <ConfiramtionBox
            openConfirmationPopup={showConfirmationPopup}
            confirmationYes={confirmationYes}
            confirmationNo={confirmationPopupClose}
            confirmationText={"This action will replace the existing dates in the form. Do you want to continue?"}
        />
        </div>
    );
};

export default AddHolidayListPopup;

import { useMutation } from "@tanstack/react-query";
import axios from "axios";

interface SendEmailPayload {
  email_id: string;
  html: string;
  subject: string;
}

const usePostSendTemplateEmail = () => {
  return useMutation(async (payload: SendEmailPayload) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/dyspatch/send/email`,
        {
          data: payload
        }
      );

      if (response.data?.data) {
        // Check for error_message in response
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      // Extract error_message from error response if available
      if (error.response?.data?.data?.error_message) {
        throw new Error(error.response.data.data.error_message);
      }
      throw new Error(error?.message || "Failed to send email");
    }
  });
};

export default usePostSendTemplateEmail;

import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";

interface SaveTemplatePayload {
  id: number | null;
  event: string;
  display_event: string;
  html_template: string;
  subject: string;
}

const usePostSaveTemplate = () => {
  const queryClient = useQueryClient();
  
  return useMutation(async (payload: SaveTemplatePayload) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/dyspatch/template`,
        {
          data: payload
        }
      );

      if (response.data?.data) {
        // Check for error_message in response
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          // Invalidate and refetch templates after successful save
          queryClient.invalidateQueries(["getEditorTemplates"]);
          return response.data.data;
        }
      } else {
        // Invalidate and refetch templates after successful save
        queryClient.invalidateQueries(["getEditorTemplates"]);
        return null;
      }
    } catch (error: any) {
      // Extract error_message from error response if available
      if (error.response?.data?.data?.error_message) {
        throw new Error(error.response.data.data.error_message);
      }
      throw new Error(error?.message || "Failed to save template");
    }
  });
};

export default usePostSaveTemplate;

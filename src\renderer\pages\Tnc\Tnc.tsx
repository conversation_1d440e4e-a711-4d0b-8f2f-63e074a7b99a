import { useContext, useEffect, useState } from "react";
import ConfiramtionBox from "../../components/common/ConfiramtionBox";
import Loader from "../../components/common/Loader";
import useUpdateTnc from "../../hooks/useUpdateTnc";
import { CommonCtx } from "../AppContainer";
import styles from "./Tnc.module.scss";
import MatPopup from "../../components/common/MatPopup";

const Tnc = () => {
    const [showConfirmationPopup, setShowConfirmationPopup] = useState(false);

    const showPopupFormAnyComponent = useContext(CommonCtx);

    const {
        mutateAsync: updateTnc,
        data: updateTncData,
        isLoading: isUpdateTncLoading,
    } = useUpdateTnc();

    useEffect(() => {
        if (isUpdateTncLoading) {
            return;
        }
        if (updateTncData) {
            showPopupFormAnyComponent(updateTncData);
        }
    }, [updateTncData, isUpdateTncLoading]);

    const confirmationYes = () => {
        updateTnc();
        confirmationPopupClose();
    };

    const confirmationPopupClose = () => {
        setShowConfirmationPopup(false);
    };

    return (
        isUpdateTncLoading ? (
            <Loader />
        ) : (
            <>
                <button className={styles.updateDatabase} onClick={() => setShowConfirmationPopup(true)}>Update T&C Version</button >
                <MatPopup
                    className={styles.orderContinuePopup}
                    open={showConfirmationPopup}
                >
                    <div className={styles.continuePopup}>
                        <p className={styles.continuetext}>Do you want to update the T&C version ?</p>
                        <div className={styles.yesAndnoBtn}>
                            <button className={styles.okBtn} onClick={confirmationYes}>
                                Yes
                            </button>
                            <button className={styles.okBtn} onClick={confirmationPopupClose}>
                                No
                            </button>
                        </div>
                    </div>
                </MatPopup>
            </>
        )
    );
};

export default Tnc;

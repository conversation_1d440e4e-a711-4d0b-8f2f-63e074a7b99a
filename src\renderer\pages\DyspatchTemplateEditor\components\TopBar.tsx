import SettingsIcon from '@mui/icons-material/Settings';
import DownloadIcon from '@mui/icons-material/Download';
import UploadIcon from '@mui/icons-material/Upload';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import SendIcon from '@mui/icons-material/Send';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import { Tooltip } from '@mui/material';
import { useTemplateStore } from '../stores/templateStore';
import { ImportModal } from './ImportModal';
import { SettingsModal } from './SettingsModal';
import { SendTestEmailModal } from './SendTestEmailModal';
import { useState } from 'react';
import { generateEmailHtml } from '../utils/emailGenerator';
import { saveAs } from 'file-saver';
import ConfiramtionBox from '../../../components/common/ConfiramtionBox/ConfiramtionBox';
import usePostSaveTemplate from '../../../hooks/usePostSaveTemplate';
import useDialogStore from '../../../components/common/DialogPopup/DialogStore';
import Loader from '../../../components/common/Loader/Loader';
import styles from './TopBar.module.scss';
import { ReactComponent as DuplicateIcon } from '../../../../assests/images/duplicate-icon.svg';

export function TopBar() {
  const {
    editorMode,
    viewMode,
    settings,
    blocks,
    sourceHtml,
    variables,
    collections,
    isDirty,
    isTemplateNameEditable,
    templateId,
    templateEvent,
    htmlErrorCount,
    setEditorMode,
    setViewMode,
    setSettings,
    setSourceHtml,
    setIsDirty,
    setTemplateNameEditable,
    setTemplateId,
    setTemplateEvent,
    resetTemplateState,
  } = useTemplateStore();

  const hasHtmlErrors = editorMode === 'source' && htmlErrorCount > 0;

  const [showImport, setShowImport] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showSendTest, setShowSendTest] = useState(false);
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false);

  const { mutate: saveTemplate, isLoading: isSaving } = usePostSaveTemplate();
  const dialogStore: any = useDialogStore();
  const { showCommonDialog, resetDialogStore } = dialogStore;

  const hasContent = blocks.length > 0 || sourceHtml.length > 0;
  const normalizedTemplateFileName = settings.templateName
    .trim()
    .replace(/\.[^.]+$/, '')
    .replace(/\s+/g, '_')
    .replace(/[^a-zA-Z0-9_]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_+|_+$/g, '')
    .toUpperCase() || 'NEW_TEMPLATE';

  const handleExport = () => {
    const html = editorMode === 'block'
      ? generateEmailHtml(blocks, settings, variables, collections, false)
      : sourceHtml;

    const blob = new Blob([html], { type: 'text/html;charset=utf-8' });
    // Use templateDisplayName exactly as it is (stored in settings.templateName)
    // Only remove characters that are truly problematic for filenames: / \ : * ? " < > |
    const filename = settings.templateName
      .trim()
      .replace(/[/\\:*?"<>|]/g, '') // Remove only truly problematic characters
      .trim() || 'template';
    saveAs(blob, `${filename}.html`);
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSubject = e.target.value;
    setSettings({ subjectLine: newSubject });

    // Keep <title> in sync when in source mode
    if (editorMode === 'source' && sourceHtml) {
      const updatedHtml = sourceHtml.replace(
        /<title>[^<]*<\/title>/i,
        `<title>${newSubject}</title>`
      );
      setSourceHtml(updatedHtml);
    }
  };

  const handleTemplateNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSettings({ templateName: e.target.value });
  };

  const getNextCopyName = (name: string) => {
    const baseName = name.trim() || 'New Template';
    const copyMatch = baseName.match(/^(.*)\sCopy(?:\s(\d+))?$/);

    if (!copyMatch) return `${baseName} Copy`;

    const originalName = copyMatch[1];
    const currentCopyIndex = copyMatch[2] ? Number(copyMatch[2]) : 1;
    return `${originalName} Copy ${currentCopyIndex + 1}`;
  };

  const handleDuplicate = () => {
    setSettings({ templateName: getNextCopyName(settings.templateName) });
    setTemplateNameEditable(true);
    setTemplateId('');
    setTemplateEvent(''); // Clear event for duplicated templates (new template)
    setIsDirty(true);
  };

  const performSave = () => {
    const html = editorMode === 'block'
      ? generateEmailHtml(blocks, settings, variables, collections, false)
      : sourceHtml;

    // Use original event if it exists (existing template), otherwise create normalized one (new template)
    const eventName = templateEvent || normalizedTemplateFileName;

    const payload = {
      id: templateId ? templateId : null,
      event: templateId ? eventName : null,
      display_event: settings.templateName,
      html_template: html,
      subject: settings.subjectLine,
    };

    saveTemplate(payload, {
      onSuccess: (response: any) => {
        setIsDirty(false);
        setShowSaveConfirmation(false);
        
        // Update templateId if it was a new template (id was null)
        if (!templateId && response?.id) {
          setTemplateId(String(response.id));
        }
        
        // Show success message if provided in response
        // Response can be a string like "Tag Updated Successfully" or an object
        const successMessage = typeof response === 'string' 
          ? response 
          : (response?.data || response || "Template saved successfully");
        
        showCommonDialog(
          null,
          successMessage,
          null,
         null,
          [{ name: "Ok", action: () => resetDialogStore() }]
        );
      }
      // onError removed - errors are handled by global axios interceptor in AppContainer.tsx
      // to prevent duplicate error popups
    });
  };

  const handleSave = () => {
    setShowSaveConfirmation(true);
  };

  if (!hasContent) {
    return (
      <>
        <div className={styles.topBar}>
          <div className={styles.topBarLeft} />
          <div className={styles.topBarRight}>
            <button
              className={styles.iconBtn}
              onClick={() => setShowImport(true)}
              title="Import HTML"
            >
              <UploadIcon sx={{ fontSize: 18 }} />
            </button>
          </div>
        </div>
        {showImport && <ImportModal onClose={() => setShowImport(false)} />}
      </>
    );
  }

  return (
    <>
      {isSaving && (
        <div className={styles.loaderOverlay}>
          <Loader />
        </div>
      )}
      <div className={styles.topBar}>
        <div className={styles.topBarLeft}>
          <button
            className={styles.backBtn}
            onClick={() => {
              resetTemplateState();
            }}
            title="Back to Templates"
          >
            <ArrowBackIcon sx={{ fontSize: 18 }} />
            Templates
          </button>

          <span className={styles.divider} />

          <div className={styles.templateField}>
            <label className={styles.templateLabel}>Template:</label>
            {isTemplateNameEditable ? (
              <input
                type="text"
                className={styles.templateNameInput}
                placeholder="Enter template name..."
                value={settings.templateName}
                onChange={handleTemplateNameChange}
                title={settings.templateName}
              />
            ) : (
              <Tooltip title={settings.templateName} arrow>
                <span className={styles.templateName}>{settings.templateName}</span>
              </Tooltip>
            )}
          </div>

          <div className={styles.modeToggle}>
            <button
              className={`${styles.modeBtn} ${editorMode === 'block' ? styles.active : ''}`}
              onClick={() => setEditorMode('block')}
              title="Visual drag-and-drop editor"
            >
              Block Mode
            </button>
            <button
              className={`${styles.modeBtn} ${editorMode === 'source' ? styles.active : ''}`}
              onClick={() => setEditorMode('source')}
              title="HTML code editor"
            >
              Source Mode
            </button>
          </div>

          <div className={styles.subjectField}>
            <label className={styles.subjectLabel}>Subject:</label>
            <input
              type="text"
              className={styles.subjectInput}
              placeholder="Enter email subject..."
              value={settings.subjectLine}
              onChange={handleSubjectChange}
            />
          </div>
        </div>

        <div className={styles.topBarRight}>
          <div className={styles.viewToggle}>
            <button
              className={`${styles.viewBtn} ${viewMode === 'edit' ? styles.active : ''}`}
              onClick={() => setViewMode('edit')}
            >
              Edit
            </button>
            <button
              className={`${styles.viewBtn} ${viewMode === 'preview' ? styles.active : ''}`}
              onClick={() => setViewMode('preview')}
            >
              Preview
            </button>
            <button
              className={`${styles.viewBtn} ${viewMode === 'html' ? styles.active : ''}`}
              onClick={() => setViewMode('html')}
            >
              HTML
            </button>
          </div>

          {editorMode === 'block' && (
            <button
              className={styles.iconBtn}
              onClick={() => setShowSettings(true)}
              title="Settings"
            >
              <SettingsIcon sx={{ fontSize: 18 }} />
            </button>
          )}


          {!isTemplateNameEditable && (
          <Tooltip title="Duplicate" arrow>
            <span>
              <button
                className={styles.duplicateBtn}
                onClick={handleDuplicate}
                title="Duplicate"
                type="button"
                disabled={false}
              >
                <DuplicateIcon />
              </button>
            </span>
          </Tooltip>
          )}

          <Tooltip 
            title={hasHtmlErrors ? `Fix ${htmlErrorCount} HTML error${htmlErrorCount > 1 ? 's' : ''} before saving` : isDirty ? (isSaving ? 'Saving...' : 'Save') : 'No changes to save'} 
            arrow
          >
            <span>
              <button
                className={styles.saveBtn}
                onClick={handleSave}
                disabled={!isDirty || hasHtmlErrors || isSaving}
              >
                <SaveIcon sx={{ fontSize: 18 }} />
              </button>
            </span>
          </Tooltip>

          <Tooltip 
            title={hasHtmlErrors ? `Fix ${htmlErrorCount} HTML error${htmlErrorCount > 1 ? 's' : ''} before sending` : 'Send Email'} 
            arrow
          >
            <span>
              <button
                className={styles.sendTestBtn}
                onClick={() => setShowSendTest(true)}
                disabled={hasHtmlErrors}
              >
                <SendIcon sx={{ fontSize: 18 }} />
              </button>
            </span>
          </Tooltip>

          <Tooltip title="Export" arrow>
            <button
              className={styles.exportBtn}
              onClick={handleExport}
            >
              <DownloadIcon sx={{ fontSize: 18 }} />
            </button>
          </Tooltip>

          {hasHtmlErrors && (
            <div className={styles.htmlErrorBadge} title={`${htmlErrorCount} HTML error${htmlErrorCount > 1 ? 's' : ''} found — check the editor for red markers`}>
              <WarningAmberIcon sx={{ fontSize: 16 }} />
              <span>{htmlErrorCount} error{htmlErrorCount > 1 ? 's' : ''}</span>
            </div>
          )}
        </div>
      </div>

      {showImport && <ImportModal onClose={() => setShowImport(false)} />}
      {showSettings && <SettingsModal onClose={() => setShowSettings(false)} />}
      {showSendTest && <SendTestEmailModal onClose={() => setShowSendTest(false)} />}
      <ConfiramtionBox
        openConfirmationPopup={showSaveConfirmation}
        confirmationYes={performSave}
        confirmationNo={() => setShowSaveConfirmation(false)}
        confirmationText="Are you sure you want to save?"
      />
    </>
  );
}

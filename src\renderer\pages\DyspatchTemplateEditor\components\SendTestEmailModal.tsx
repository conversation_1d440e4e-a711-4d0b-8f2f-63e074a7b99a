import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { useState } from 'react';
import { useTemplateStore } from '../stores/templateStore';
import { generateEmailHtml, processVariablesInString } from '../utils/emailGenerator';
import usePostSendTemplateEmail from '../../../hooks/usePostSendTemplateEmail';
import useDialogStore from '../../../components/common/DialogPopup/DialogStore';
import Loader from '../../../components/common/Loader/Loader';
import styles from './SendTestEmailModal.module.scss';

interface SendTestEmailModalProps {
  onClose: () => void;
}

const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

function validateEmails(input: string): { valid: string[]; invalid: string[] } {
  const emails = input
    .split(',')
    .map((e) => e.trim())
    .filter((e) => e.length > 0);

  const valid: string[] = [];
  const invalid: string[] = [];

  emails.forEach((email) => {
    if (EMAIL_REGEX.test(email)) {
      valid.push(email);
    } else {
      invalid.push(email);
    }
  });

  return { valid, invalid };
}

export function SendTestEmailModal({ onClose }: SendTestEmailModalProps) {
  const { editorMode, blocks, sourceHtml, settings, variables, collections, templateEvent } =
    useTemplateStore();

  const { mutate: sendEmail, isLoading: isSending } = usePostSendTemplateEmail();
  const dialogStore: any = useDialogStore();
  const { showCommonDialog, resetDialogStore } = dialogStore;

  const [emailInput, setEmailInput] = useState('');
  const [error, setError] = useState('');

  const handleSend = () => {
    const trimmed = emailInput.trim();

    if (!trimmed) {
      setError('Please enter at least one email address.');
      return;
    }

    const { valid, invalid } = validateEmails(trimmed);

    if (invalid.length > 0) {
      setError(`Invalid email${invalid.length > 1 ? 's' : ''}: ${invalid.join(', ')}`);
      return;
    }

    if (valid.length === 0) {
      setError('Please enter at least one valid email address.');
      return;
    }

    setError('');

    // Calculate normalized template file name (same logic as TopBar)
    const normalizedTemplateFileName = settings.templateName
      .trim()
      .replace(/\.[^.]+$/, '')
      .replace(/\s+/g, '_')
      .replace(/[^a-zA-Z0-9_]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_+|_+$/g, '')
      .toUpperCase() || 'NEW_TEMPLATE';

    // Use original event if it exists (existing template), otherwise create normalized one (new template)
    const eventName = templateEvent || settings.templateName;

    // When sending email, replace variables with their values (forPreview: true)
    // This ensures variables like {{buyerFirstName}} are replaced with actual values
    const html =
      editorMode === 'block'
        ? generateEmailHtml(blocks, settings, variables, collections, true)
        : (() => {
            // For source mode, we need to process variables manually
            // Use generateEmailHtml with a temporary block to process variables
            return generateEmailHtml(
              [{ id: 'temp', type: 'html', content: sourceHtml }],
              settings,
              variables,
              collections,
              true
            );
          })();

    // Process subject line to replace variables with their values
    const processedSubject = processVariablesInString(
      settings.subjectLine || '',
      variables,
      true // forPreview: true to replace variables
    );

    const payload = {
      email_id: valid.join(','),
      html: html,
      subject: processedSubject,
      event: eventName,
    };

    sendEmail(payload, {
      onSuccess: (response: any) => {
        // Show success message if provided in response
        // Response can be a string like "Tag Updated Successfully" or an object
        const successMessage = typeof response === 'string' 
          ? response 
          : (response?.data || response || "Email sent successfully");
        
        showCommonDialog(
          null,
          successMessage,
          null,
          () => {},
          [{ name: "Ok", action: () => {
            resetDialogStore();
            onClose();
          }}]
        );
      },
      // onError removed - errors are handled by global axios interceptor in AppContainer.tsx
      // to prevent duplicate error popups
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmailInput(e.target.value);
    if (error) setError('');
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <>
      {isSending && (
        <div className={styles.loaderOverlay}>
          <Loader />
        </div>
      )}
      <div className={styles.modalOverlay} onClick={onClose}>
        <div className={styles.modal} onClick={(e) => e.stopPropagation()}>

        <div className={styles.modalContent}>
          <div className={styles.formGroup}>
            <label className={styles.label}>Recipient(s)</label>
            <input
              type="text"
              className={`${styles.emailInput} ${error ? styles.inputError : ''}`}
              placeholder="e.g. <EMAIL>, <EMAIL>"
              value={emailInput}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              autoFocus
            />
            <p className={styles.hint}>
              Separate multiple email addresses with commas
            </p>
            {error && (
              <div className={styles.errorMessage}>
                <ErrorOutlineIcon sx={{ fontSize: 14 }} />
                <span>{error}</span>
              </div>
            )}
          </div>

          <div className={styles.previewInfo}>
            <div className={styles.previewRow}>
              <span className={styles.previewLabel}>Template:</span>
              <span className={styles.previewValue}>{settings.templateName}</span>
            </div>
            <div className={styles.previewRow}>
              <span className={styles.previewLabel}>Subject:</span>
              <span className={styles.previewValue}>
                {/* Show processed subject line with variables replaced */}
                {processVariablesInString(settings.subjectLine, variables, true) || '(no subject)'}
              </span>
            </div>
          </div>
        </div>

        <div className={styles.modalActions}>
          <button className={styles.btnSecondary} onClick={onClose}>
            Cancel
          </button>
          <button
            className={styles.btnPrimary}
            onClick={handleSend}
            disabled={isSending || !emailInput.trim()}
          >
            <SendIcon sx={{ fontSize: 16 }} />
            {isSending ? 'Sending...' : 'Send Email'}
          </button>
        </div>
      </div>
    </div>
    </>
  );
}

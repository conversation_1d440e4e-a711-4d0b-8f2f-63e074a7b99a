import React, { useEffect } from "react";
import { useState } from "react";
import { Outlet } from "react-router-dom";
import Sidebar from "react-sidebar";
import SideNavigation from "../components/SideNavigation";
import { Logout } from "../utils/helper";
import styles from "./Layout.module.scss";
import { ReactComponent as MenuIcon } from "./hamburger-menu-icon.svg";
import LogoNew from "../../assests/images/logo.png";

import { upperCase } from "lodash-es";

// Set document title from environment variable
if (import.meta.env.VITE_APP_TITLE) {
  document.title = import.meta.env.VITE_APP_TITLE;
}

const Layout = () => {
  const mql = window.matchMedia(`(min-width: 800px)`);

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarDocked, setSidebarDocked] = useState(mql.matches);
  // const [EnvType, setEnvType] = useState(false);

  // if ( window.env.environmentType === 'local') {
  //      setEnvType(true)
  //  } 

  const mediaQueryChanged = () => {
    setSidebarDocked(mql.matches);
    setSidebarOpen(false);
  };

  useEffect(() => {
    mql.addEventListener("change", mediaQueryChanged);

    return () => {
      mql.removeEventListener("change", mediaQueryChanged);
    };
  }, []);

  const toggleSidebar = () => {
    if (mql.matches) {
      setSidebarDocked((state) => !state);
      setSidebarOpen(false);
    } else {
      setSidebarDocked(false);
      setSidebarOpen((state) => !state);
    }
  };

  return (
    <div className={styles.dashboardMain}>
      <Sidebar sidebarClassName={styles.sideNavMain} 
        sidebar={
          <div className={styles.LeftCol}>
            <div className={styles.bryzosLogo}>
              <div>
                {/* <img
                  src="https://prod-bryzos-assets.imgix.net/img/Dashboard-Bryzos-Logo.svg"
                  alt=""
                /> */}
                <img className={styles.logoNew} src={LogoNew} alt="Logo" />
                {/* <Logo/> */}
                <div className={styles.extendedWidget}>
                  {import.meta.env.VITE_APP_TITLE || 'Extended Widget Admin'}
                  <br />
                  <span>{upperCase(import.meta.env.VITE_ENVIRONMENT)}</span>
                </div>
              </div>
              {sidebarOpen && (
                <div className={styles.crossBtn} onClick={toggleSidebar}>
                  X
                </div>
              )}
            </div>
            <div className={styles.bryzosLogos}>
              <SideNavigation
                sidebarDocked={sidebarDocked}
                toggleSidebar={toggleSidebar}
              />
            </div>
            <div className={styles.logoutBtn}>
              <button onClick={Logout}>Logout</button>
            </div>
          </div>
        }
        open={sidebarOpen}
        docked={sidebarDocked}
        onSetOpen={(open) => setSidebarOpen(open)}
      >
        <div className={styles.MainContent}>
          <div className={styles.dashhead}>
            <button className={styles.sidebarBtn} onClick={toggleSidebar}>
              <span>
                <MenuIcon />
              </span>
            </button>
            
            {import.meta.env.VITE_ENVIRONMENT === 'prod' && (
                    <div className={styles.prosuctionLiveText} >
                    <span>PRODUCTION / LIVE ADMIN</span>
                  </div>
            )}
         
          </div>

          <Outlet />
        </div>
      </Sidebar>
    </div>
  );
};

export default Layout;

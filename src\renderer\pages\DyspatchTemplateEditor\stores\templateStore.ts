import { create } from 'zustand';
import type {
  TemplateState,
  EditorMode,
  ViewMode,
  DeviceSize,
  BlockContent,
  Variable,
  EmailSettings
} from '../types';
import { parseHtmlToBlocks, generateEmailHtml } from '../utils/emailGenerator';

interface TemplateStore extends TemplateState {
  setEditorMode: (mode: EditorMode) => void;
  setViewMode: (mode: ViewMode) => void;
  setDeviceSize: (size: DeviceSize) => void;
  setBlocks: (blocks: BlockContent[]) => void;
  addBlock: (block: BlockContent, index?: number) => void;
  updateBlock: (id: string, updates: Partial<BlockContent>) => void;
  removeBlock: (id: string) => void;
  moveBlock: (fromIndex: number, toIndex: number) => void;
  setSourceHtml: (html: string) => void;
  setVariables: (variables: Variable[]) => void;
  addVariable: (variable: Variable) => void;
  updateVariable: (name: string, updates: Partial<Variable>) => void;
  removeVariable: (name: string) => void;
  setSettings: (settings: Partial<EmailSettings>) => void;
  setCollections: (collections: Record<string, any[]>) => void;
  updateCollection: (name: string, data: any[]) => void;
  extractVariables: (html: string) => void;
  setIsDirty: (dirty: boolean) => void;
  setTemplateNameEditable: (editable: boolean) => void;
  setTemplateId: (id: string) => void;
  setTemplateEvent: (event: string) => void;
  setHtmlErrorCount: (count: number) => void;
  resetTemplateState: () => void;
}

const defaultSettings: EmailSettings = {
  templateName: 'New Template',
  subjectLine: '',
  emailBackgroundColor: '#f4f4f4',
  contentBackgroundColor: '#ffffff',
  fontFamily: 'Arial',
  maxWidth: 600,
};

const initialTemplateState: TemplateState = {
  editorMode: 'block',
  viewMode: 'edit',
  deviceSize: 'desktop',
  blocks: [],
  sourceHtml: '',
  variables: [],
  collections: {},
  settings: { ...defaultSettings },
  isDirty: false,
  isTemplateNameEditable: false,
  templateId: '',
  templateEvent: '',
  htmlErrorCount: 0,
};

export const useTemplateStore = create<TemplateStore>((set, get) => ({
  ...initialTemplateState,

  setIsDirty: (dirty) => set({ isDirty: dirty }),
  setTemplateNameEditable: (editable) => set({ isTemplateNameEditable: editable }),
  setTemplateId: (id) => set({ templateId: id }),
  setTemplateEvent: (event) => set({ templateEvent: event }),
  setHtmlErrorCount: (count) => set({ htmlErrorCount: count }),
  resetTemplateState: () => set({ ...initialTemplateState, settings: { ...defaultSettings } }),

  setEditorMode: (mode) => {
    const state = get();

    if (mode === 'block' && state.editorMode === 'source' && state.sourceHtml) {
      const blocks = parseHtmlToBlocks(state.sourceHtml);
      set({ editorMode: mode, blocks });
    } else if (mode === 'source' && state.editorMode === 'block' && state.blocks.length > 0) {
      const html = generateEmailHtml(state.blocks, state.settings, state.variables, state.collections, false);
      set({ editorMode: mode, sourceHtml: html });
    } else {
      set({ editorMode: mode });
    }
  },
  setViewMode: (mode) => set({ viewMode: mode }),
  setDeviceSize: (size) => set({ deviceSize: size }),

  setBlocks: (blocks) => set({ blocks }),

  addBlock: (block, index) => set((state) => {
    const newBlocks = [...state.blocks];
    if (index !== undefined) {
      newBlocks.splice(index, 0, block);
    } else {
      newBlocks.push(block);
    }
    return { blocks: newBlocks, isDirty: true };
  }),

  updateBlock: (id, updates) => {
    const state = get();
    const newBlocks = state.blocks.map(block =>
      block.id === id ? { ...block, ...updates } : block
    );

    set({ blocks: newBlocks, isDirty: true });

    if (state.editorMode === 'source') {
      const html = generateEmailHtml(newBlocks, state.settings, state.variables, state.collections, false);
      set({ sourceHtml: html });
    }
  },

  removeBlock: (id) => set((state) => ({
    blocks: state.blocks.filter(block => block.id !== id),
    isDirty: true,
  })),

  moveBlock: (fromIndex, toIndex) => set((state) => {
    const newBlocks = [...state.blocks];
    const [movedBlock] = newBlocks.splice(fromIndex, 1);
    newBlocks.splice(toIndex, 0, movedBlock);
    return { blocks: newBlocks, isDirty: true };
  }),

  setSourceHtml: (html) => {
    set({ sourceHtml: html, isDirty: true });
    get().extractVariables(html);

    const state = get();
    if (state.editorMode === 'block') {
      const blocks = parseHtmlToBlocks(html);
      set({ blocks });
    }
  },

  setVariables: (variables) => set({ variables }),

  addVariable: (variable) => set((state) => {
    const exists = state.variables.some(v => v.name === variable.name);
    if (!exists) {
      return { variables: [...state.variables, variable] };
    }
    return state;
  }),

  updateVariable: (name, updates) => set((state) => ({
    variables: state.variables.map(v =>
      v.name === name ? { ...v, ...updates } : v
    ),
  })),

  removeVariable: (name) => set((state) => ({
    variables: state.variables.filter(v => v.name !== name),
  })),

  setSettings: (settings) => set((state) => ({
    settings: { ...state.settings, ...settings },
    isDirty: true,
  })),

  setCollections: (collections) => set({ collections }),

  updateCollection: (name, data) => set((state) => ({
    collections: { ...state.collections, [name]: data },
  })),

  extractVariables: (html) => {
    if (!html || typeof html !== 'string') {
      return;
    }

    const state = get();
    const existingVariables = state.variables;
    const existingCollections = state.collections;

    const regex = /\{\{([^}]+)\}\}/g;
    const loopRegex = /\{% for (\w+) in (\w+) %\}/g;
    const matches = html.matchAll(regex);
    const loopMatches = html.matchAll(loopRegex);
    const templateVariables: Variable[] = [];
    const templateCollections: Record<string, any[]> = { ...existingCollections };

    // Extract variables from {{variableName}} syntax
    for (const match of matches) {
      const varName = match[1].trim();
      if (!templateVariables.some(v => v.name === varName)) {
        // Check if variable already exists and preserve its defaultValue
        const existingVar = existingVariables.find(v => v.name === varName);
        templateVariables.push({
          name: varName,
          defaultValue: existingVar?.defaultValue || '',
          type: existingVar?.type || 'text',
        });
      }
    }

    // Extract variables from {% for ... %} loops
    for (const match of loopMatches) {
      const itemVar = match[1].trim();
      const collectionName = match[2].trim();

      // Preserve existing collection if it exists
      if (!templateCollections[collectionName]) {
        templateCollections[collectionName] = existingCollections[collectionName] || [
          { name: 'Sample Item 1', value: 'Sample Value 1' },
          { name: 'Sample Item 2', value: 'Sample Value 2' }
        ];
      }

      if (!templateVariables.some(v => v.name === itemVar)) {
        // Check if variable already exists and preserve its defaultValue
        const existingVar = existingVariables.find(v => v.name === itemVar);
        templateVariables.push({
          name: itemVar,
          defaultValue: existingVar?.defaultValue || '(loop item)',
          type: existingVar?.type || 'text',
        });
      }
    }

    set({
      variables: templateVariables,
      collections: templateCollections,
    });
  },
}));

import { useQuery } from "@tanstack/react-query";
import { reactQuery<PERSON>eys } from "../../utils/constant";
import axios from "axios";


const useGetScreen = (id: string) => {
  return useQuery([reactQueryKeys.getAdminVideoTooltipScreens, id], async () => {
    try {
      const response = await axios.get(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/screens/${id}`);
      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  }, {
    enabled: !!id
  });
};

export default useGetScreen;
import { useMutation } from "@tanstack/react-query";
import type { MappingCreateRequest, MappingResponse } from "../../types/api";
import axios from "axios";

export const useCreateMapping = () => {
  return useMutation(async (data: MappingCreateRequest): Promise<MappingResponse> => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/video-mappings`, data);

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data;
        }
      } else {
        return response.data;
      }
    } catch (error: any) {
      throw new Error(error?.message ?? "");
    }
  });
};

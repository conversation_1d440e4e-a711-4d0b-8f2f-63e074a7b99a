import { useState } from 'react';
import { useTemplateStore } from '../stores/templateStore';
import { extractSubjectFromHtml } from '../utils/emailGenerator';
import type { TemplateItem } from '../types';
import styles from './TemplateLibrary.module.scss';

interface TemplateLibraryProps {
  templates: TemplateItem[];
}

export function TemplateLibrary({ templates }: TemplateLibraryProps) {
  const { setSourceHtml, setSettings, setEditorMode, setViewMode, extractVariables, addBlock, setVariables, setCollections, setBlocks, setIsDirty, setTemplateNameEditable, setTemplateId, setTemplateEvent } = useTemplateStore();
  const [searchTerm, setSearchTerm] = useState('');

  const createBlankTemplate = (mode: 'block' | 'source') => {
    // Clear existing content and variables
    setSourceHtml('');
    setBlocks([]);
    setVariables([]);
    setCollections({});

    if (mode === 'block') {
      addBlock({
        id: `block-${Date.now()}`,
        type: 'heading1',
        content: 'Welcome to {{companyName}}!',
        styles: { fontSize: '32px', color: '#333333', textAlign: 'center' }
      });
      addBlock({
        id: `block-${Date.now() + 1}`,
        type: 'paragraph',
        content: 'Hello {{firstName}}, thank you for signing up!',
        styles: { fontSize: '16px', color: '#555555', textAlign: 'left' }
      });
      setEditorMode('block');

      // Extract variables from the new blocks
      extractVariables('Welcome to {{companyName}}! Hello {{firstName}}, thank you for signing up!');
    } else {
      // Set editor mode BEFORE setting HTML so block→source re-generation doesn't overwrite it
      setEditorMode('source');

      const sampleHtml = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>Welcome to {{companyName}}!</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<style type="text/css">
body { margin: 0; padding: 0; background-color: #f4f4f4; }
table { border-collapse: collapse; }
@media only screen and (max-width: 620px) {
  .responsive { width: 100% !important; }
}
</style>
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4;">
<table role="presentation" width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td align="center" style="padding: 20px 0;">
      <table role="presentation" border="0" cellspacing="0" cellpadding="0" width="600" style="background-color: #ffffff;" class="responsive">
        <tr>
          <td style="padding: 40px;">
            <h1 style="margin: 0; font-family: Arial, sans-serif; font-size: 32px; color: #333333; text-align: center;">
              Welcome to {{companyName}}!
            </h1>
            <p style="margin: 20px 0 0 0; font-family: Arial, sans-serif; font-size: 16px; line-height: 1.6; color: #555555;">
              Hello {{firstName}}, thank you for signing up! We're excited to have you on board.
            </p>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
</body>
</html>`;
      setSourceHtml(sampleHtml);
      extractVariables(sampleHtml);
    }

    setSettings({
      subjectLine: 'Welcome to {{companyName}}!',
      templateName: 'Welcome Template'
    });
    setTemplateId('');
    setTemplateEvent(''); // Clear event for new blank templates
    setViewMode('edit');
    setTemplateNameEditable(true);
    setIsDirty(false);
  };

  const loadTemplate = async (item: TemplateItem) => {
    try {
      // Use HTML template directly from API response
      const html = item.html_template || '';

      if (!html.trim()) {
        alert('Template HTML content is missing.');
        return;
      }

      // Set editor mode BEFORE setting HTML so block→source re-generation doesn't overwrite it
      setEditorMode('source');

      setSourceHtml(html);
      extractVariables(html);

      // Use subject from API, fallback to extracting from HTML if not available
      const subject = item.subject || extractSubjectFromHtml(html);
      const templateName = item.templateDisplayName || item.templateName || 'Untitled Template';

      setSettings({
        templateName: templateName,
        subjectLine: subject || '',
      });

      setTemplateId(String(item.templateid || ''));
      setTemplateEvent(item.templateName || ''); // Store original event name
      setViewMode('edit');
      setTemplateNameEditable(false);
      setIsDirty(false);
    } catch (error) {
      console.error('Failed to load template:', error);
      alert('Template loading failed. Use the Import button to paste/upload your template instead.');
    }
  };

  return (
    <div className={styles.templateLibrary}>
      <div className={styles.quickStart}>
        <h3>Quick Start</h3>
        <div className={styles.quickStartButtons}>
          <button
            className={styles.quickStartBtn}
            onClick={() => createBlankTemplate('block')}
          >
            Start with Block Mode
            <span>Visual drag-and-drop editor</span>
          </button>
          <button
            className={`${styles.quickStartBtn} ${styles.sourceMode}`}
            onClick={() => createBlankTemplate('source')}
          >
            Start with Source Mode
            <span>HTML code editor</span>
          </button>
        </div>
      </div>

      <div className={styles.existingTemplates}>
        <h3>Existing Templates ({templates.length})</h3>

        <div className={styles.templateSearch}>
          <input
            type="text"
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        {templates.length > 0 ? (
          <div className={styles.templateList}>
            {templates
              .filter(item => {
                const displayName = (item.templateDisplayName || '').toLowerCase();
                const templateName = (item.templateName || '').toLowerCase();
                const searchLower = searchTerm.toLowerCase();
                return displayName.includes(searchLower) || templateName.includes(searchLower);
              })
              .map((item) => {
                const displayName = item.templateDisplayName || item.templateName || 'Untitled Template';
                return (
                  <button
                    key={String(item.templateid || Math.random())}
                    className={styles.templateItem}
                    onClick={() => loadTemplate(item)}
                    title={displayName}
                  >
                    {displayName}
                  </button>
                );
              })}
          </div>
        ) : (
          <p>No data</p>
        )}
      </div>
    </div>
  );
}

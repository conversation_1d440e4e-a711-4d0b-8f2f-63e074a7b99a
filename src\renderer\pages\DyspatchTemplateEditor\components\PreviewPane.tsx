import SmartphoneIcon from '@mui/icons-material/Smartphone';
import DesktopWindowsIcon from '@mui/icons-material/DesktopWindows';
import { useTemplateStore } from '../stores/templateStore';
import { generateEmailHtml } from '../utils/emailGenerator';
import styles from './PreviewPane.module.scss';

export function PreviewPane() {
  const { blocks, sourceHtml, settings, variables, collections, editorMode, deviceSize, setDeviceSize } = useTemplateStore();

  const getPreviewHtml = () => {
    const html = editorMode === 'block'
      ? generateEmailHtml(blocks, settings, variables, collections, true)
      : sourceHtml;

    // For source mode, manually process variables and collections for preview
    if (editorMode === 'source') {
      return generateEmailHtml(
        [{ id: 'temp', type: 'html', content: html }],
        settings,
        variables,
        collections,
        true
      );
    }

    return html;
  };

  return (
    <div className={styles.previewPane}>
      <div className={styles.previewToolbar}>
        <div className={styles.deviceToggle}>
          <button
            className={`${styles.deviceBtn} ${deviceSize === 'desktop' ? styles.active : ''}`}
            onClick={() => setDeviceSize('desktop')}
            title="Desktop view"
          >
            <DesktopWindowsIcon sx={{ fontSize: 18 }} />
          </button>
          <button
            className={`${styles.deviceBtn} ${deviceSize === 'mobile' ? styles.active : ''}`}
            onClick={() => setDeviceSize('mobile')}
            title="Mobile view"
          >
            <SmartphoneIcon sx={{ fontSize: 18 }} />
          </button>
        </div>
      </div>

      <div className={styles.previewContainer}>
        {deviceSize === 'mobile' ? (
          <div className={styles.phoneWrapper}>
            <div className={styles.deviceFrame}>
              <div className={styles.deviceNotch} />
              <iframe
                className={styles.previewIframe}
                srcDoc={getPreviewHtml()}
                title="Email Preview"
                style={{ backgroundColor: settings.emailBackgroundColor }}
              />
            </div>
          </div>
        ) : (
          <iframe
            className={styles.previewIframe}
            srcDoc={getPreviewHtml()}
            title="Email Preview"
            style={{ backgroundColor: settings.emailBackgroundColor }}
          />
        )}
      </div>
    </div>
  );
}

import { useMutation } from "@tanstack/react-query";
import axios from "axios";

export const useDeleteHotspot = () => {
  return useMutation(async ({ hotspotId }: { hotspotId: string }) => {
    try {
      const response = await axios.delete(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/hotspots/${hotspotId}`);

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message ?? "");
    }
  });
};

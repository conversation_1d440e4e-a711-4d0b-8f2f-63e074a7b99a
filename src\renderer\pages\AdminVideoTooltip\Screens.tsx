import { <PERSON>ert, Box, Button, CircularProgress, Paper, Typography, Chip, Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import { Add as AddIcon, Info as InfoIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';
import { useState } from "react";
import ScreensList from "./_components/screens/ScreensList";
import ScreenUploadModal from "./_components/screens/ScreenUploadModal";
import useGetScreens from "../../hooks/admin-video-tooltip/useGetScreens";


export default function Screens() {
  const { data: screens, isLoading: loading, error, refetch } = useGetScreens();
  const [uploadModalOpen, setUploadModalOpen] = useState(false);

  const handleUploadSuccess = () => {
    setUploadModalOpen(false);
    refetch();
  };

  return (
    <Box sx={{ minHeight: '100%', display: 'flex', flexDirection: 'column', pb: 2 }}>
    {/* Header */}
    <Paper
      elevation={0}
      sx={{
        p: 2,
        backgroundColor: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <Typography
        variant="h4"
        sx={{
          fontWeight: 'bold',
          color: '#0d1b2a',
          fontSize: '24px',
        }}
      >
        Screens
      </Typography>
      <Button
        variant="contained"
        startIcon={<AddIcon />}
        onClick={() => setUploadModalOpen(true)}
      >
        Upload Screen
      </Button>
      
    </Paper>
    
    {/* Quick Guide */}
    <Accordion 
      defaultExpanded
      sx={{
        mt: 2,
        mb: 3,
        boxShadow: 2,
        '&:before': {
          display: 'none',
        },
        '&.Mui-expanded': {
          margin: '16px 0 24px 0',
        },
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon sx={{ color: '#2196f3' }} />}
        sx={{
          backgroundColor: '#e3f2fd',
          borderLeft: '4px solid #2196f3',
          '&:hover': {
            backgroundColor: '#bbdefb',
          },
          '&.Mui-expanded': {
            backgroundColor: '#e3f2fd',
            minHeight: 48,
          },
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, width: '100%' }}>
          <InfoIcon sx={{ color: '#2196f3', fontSize: 24 }} />
          <Typography
            variant="h6"
            sx={{
              fontWeight: 'bold',
              color: '#1565c0',
              fontSize: '16px',
            }}
          >
            Quick Guide: Video Tooltips
          </Typography>
        </Box>
      </AccordionSummary>
      <AccordionDetails
        sx={{
          backgroundColor: '#f5f5f5',
          p: 2.5,
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
            <Chip 
              label="1" 
              size="small" 
              sx={{ 
                backgroundColor: '#2196f3', 
                color: 'white', 
                fontWeight: 'bold',
                minWidth: '24px',
                height: '24px',
              }} 
            />
            <Typography variant="body2" sx={{ color: '#0d1b2a', lineHeight: 1.6 }}>
              <strong>Upload Screen</strong> → Click "Upload Screen", add name and image (PNG/JPEG, max 10MB)
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
            <Chip 
              label="2" 
              size="small" 
              sx={{ 
                backgroundColor: '#2196f3', 
                color: 'white', 
                fontWeight: 'bold',
                minWidth: '24px',
                height: '24px',
              }} 
            />
            <Typography variant="body2" sx={{ color: '#0d1b2a', lineHeight: 1.6 }}>
              <strong>Add Hotspots</strong> → Click "Hotspots" on screen card → Use Rectangle tool to draw areas → Enter unique Element ID for each → Save
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
            <Chip 
              label="3" 
              size="small" 
              sx={{ 
                backgroundColor: '#2196f3', 
                color: 'white', 
                fontWeight: 'bold',
                minWidth: '24px',
                height: '24px',
              }} 
            />
            <Typography variant="body2" sx={{ color: '#0d1b2a', lineHeight: 1.6 }}>
              <strong>Map Videos</strong> → Click "Video-Mappings" on screen card → Select hotspot → Upload video file → Add title/description (optional) → Save
            </Typography>
          </Box>
          <Box sx={{ mt: 1, pt: 1.5, borderTop: '1px solid rgba(33, 150, 243, 0.2)' }}>
            <Typography variant="caption" sx={{ color: '#1565c0', fontStyle: 'italic', display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <InfoIcon sx={{ fontSize: 14 }} />
              <strong>Note:</strong> Inform engineering team whenever you add new screens or hotspots. 
            </Typography>
          </Box>
        </Box>
      </AccordionDetails>
    </Accordion>

    {/* Content */}
    <Box sx={{ flex: 1, }}>
      {error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error instanceof Error ? error.message : 'Failed to load screens. Please try again.'}
        </Alert>
      ) : null}

      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '200px',
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <ScreensList
          screens={screens || []}
          onScreensChange={refetch}
          onUploadClick={() => setUploadModalOpen(true)}
        />
      )}
    </Box>

    {/* Upload Modal */}
    <ScreenUploadModal
      open={uploadModalOpen}
      onClose={() => setUploadModalOpen(false)}
      onSuccess={handleUploadSuccess}
    />
  </Box>
  );
}

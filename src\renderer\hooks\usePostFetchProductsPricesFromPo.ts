import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";
import { commonUiBackendDecryption } from "@bryzos/giss-ui-library";

const usePostFetchProductsPricesFromPo = (isEncrypted: boolean) => {
  const queryClient = useQueryClient();

  return useMutation(async (data: any) => {
    try {
      let response = await axios.post(
        `${import.meta.env.VITE_API_PRICING_SERVICE}/api/v1/pricing/calculate/po`,
        { data }
      );

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          if (isEncrypted) {
            response = JSON.parse(await commonUiBackendDecryption(response.data.data, import.meta.env.VITE_DECRYPTION_KEY));
          } else {
            response = response.data.data;
          }
          return response;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostFetchProductsPricesFromPo;

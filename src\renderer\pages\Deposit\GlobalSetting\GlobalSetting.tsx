import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import useGetGlobalDepositHistory from "../../../hooks/useGetGlobalDepositHistory";
import { MenuItem, Select } from "@mui/material";
import { useEffect, useState } from "react";
import { filterArrray } from "../../../utils/helper";
import { useImmer } from "use-immer";
import ReactPaginate from "react-paginate";
import usePostGlobalDepositSetting from "../../../hooks/usePostGlobalDepositSetting";
import Loader from "../../../components/common/Loader";
import InputField from "../../../components/common/InputField";
import styles from "./GlobalSetting.module.scss";
import useDialogStore from "../../../components/common/DialogPopup/DialogStore";
import { confirmationPopupKeys} from "../../../utils/constant";
import SearchBar from "../../../components/common/SearchBox/SearchBox";

const GlobalSettingFormSchema = yup.object().shape({
  deposit: yup.number().required("Required").typeError("Enter valid input"),
  reason: yup.string().required("Required"),
});

type GlobalSettingFormSchemaType = yup.InferType<
  typeof GlobalSettingFormSchema
>;

const GlobalSetting = () => {
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  const [filteredGlobalDepositHistory, setFilteredGlobalDepositHistory] =
    useImmer<any>([]);

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(
    filteredGlobalDepositHistory.length / itemsPerPage
  );

  const {
    data: globalDepositHistoryData,
    isLoading: isGlobalDepositHistoryDataLoading,
  } = useGetGlobalDepositHistory();

  const {
    mutate: saveGlobalDepositHistory,
    data: sendGlobalDepositHistoryData,
    isLoading: isSendGlobalDepositHistoryDataLoading,
  } = usePostGlobalDepositSetting();

  const {
    control,
    reset,
    formState: { errors, isValid },
    handleSubmit,
  } = useForm<GlobalSettingFormSchemaType>({
    defaultValues: {},
    resolver: yupResolver(GlobalSettingFormSchema),
  });


  useEffect(() => {
    if (isSendGlobalDepositHistoryDataLoading) {
      setFilteredGlobalDepositHistory([]);
    }
  }, [isSendGlobalDepositHistoryDataLoading]);

  useEffect(() => {
    if(globalDepositHistoryData?.length > 0 && inputSearchValue.length !== 0){
      search(inputSearchValue)
    } else if (globalDepositHistoryData) {
      setFilteredGlobalDepositHistory(globalDepositHistoryData);
    } else {
      setFilteredGlobalDepositHistory([]);
    }
  }, [globalDepositHistoryData, inputSearchValue]);

  const globalSettingFormSubmitHandler = (
    data: GlobalSettingFormSchemaType
  ) => {
    reset();
    saveGlobalDepositHistory({
      data: { deposit_percentage: data.deposit, note: data.reason },
    });
  };

  const search = (searchValue: string) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(searchValue);
    if (searchValue) {
      const _filterArrray = filterArrray(
        globalDepositHistoryData,
        searchValue.trim(),
        [
          "deposit_percentage",
          "applied",
          "company_name",
          "created_date",
          "note",
        ]
      );
      if (_filterArrray?.length) {
        setFilteredGlobalDepositHistory(_filterArrray.slice(0, itemsPerPage));
      } else {
        setFilteredGlobalDepositHistory([]);
      }
    } else {
      setFilteredGlobalDepositHistory(
        globalDepositHistoryData ? globalDepositHistoryData : []
      );
    }
  };

  const handlePageClick = (event: any) => {
    const newOffset =
      (event.selected * itemsPerPage) % filteredGlobalDepositHistory.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  const showGlobalSettingPopup = () => {
    showCommonDialog(null, confirmationPopupKeys.confirmationContent, null, resetDialogStore, 
      [{name: confirmationPopupKeys.confirmation.yes, action: ()=>{showGlobalPopup()}},{name: confirmationPopupKeys.confirmation.no, action: resetDialogStore}])
   }

  const showGlobalPopup = () => {
    handleSubmit(globalSettingFormSubmitHandler)();
    resetDialogStore()
  };

  return (
    <div className="contentMain">
      {isGlobalDepositHistoryDataLoading ||
      isSendGlobalDepositHistoryDataLoading ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.globalPanel}>
              <p className={styles.globalDeposittext}>Global Deposit Setting</p>
              <div>
                <div className={styles.depositPanel}>
                  <label className={styles.depositText}>Deposit</label>
                  <InputField className={styles.depositGlobal} control={control} fieldName="deposit" type="tel" />
                </div>
                <div className={styles.depositPanel}>
                  <label className={styles.depositText}>Reason For Deposit Change</label>
                  <InputField className={styles.depositGlobal} control={control} fieldName="reason" /></div>
                <button className={styles.submitBtn} onClick={showGlobalSettingPopup} type="submit" disabled={!isValid}>Submit</button>
              </div>
          </div>

          <div>
            <div className={styles.searchBox}>
              <Select
                className={styles.showdropdwn}
                value={itemsPerPage}
                onChange={(event) => {
                  setItemsPerPage(+event.target.value);
                }}
              >
                {perPageEntriesOptions.map((item, index) => (
                  <MenuItem key={index} value={item}>
                    <span>{item}</span>
                  </MenuItem>
                ))}
              </Select>
              <SearchBar
                  value={inputSearchValue}
                  placeholder={'Search'}
                  onChange={(event)=>search(event.target.value)}
                  onClear={() => setInputSearchValue('')}
              />
            </div>
              <div className={styles.tblscroll}>
                <table>
                  <thead>
                    <tr>
                      <th>Deposit Percentage</th>
                      <th>Applied</th>
                      <th>Created Date</th>
                      <th>Note</th>
                    </tr>
                  </thead>
                  <tbody>
                  {filteredGlobalDepositHistory?.length ? (
                    filteredGlobalDepositHistory
                      .slice(itemOffset, endOffset)
                      .map(
                        (history: any) =>
                          history.is_active && (
                            <tr key={history.id}>
                              <td>{history.deposit_percentage}</td>
                              <td>{history.applied}</td>
                              <td>{history.created_date}</td>
                              <td>{history.note}</td>
                            </tr>
                          )
                      )
                      ) : (
                        <tr>
                          <td colSpan={4} className={"noDataFoundTd"}>No data found</td>
                        </tr>
                      )}
                  </tbody>
                </table>
              </div>
            <div className={"PaginationNumber"}>
              <ReactPaginate
                breakLabel="..."
                nextLabel=">"
                onPageChange={handlePageClick}
                pageRangeDisplayed={5}
                pageCount={pageCount}
                previousLabel="<"
                renderOnZeroPageCount={(props) =>
                  props.pageCount > 0 ? undefined : null
                }
                forcePage={pageCount > 0 ? currentPage : -1}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlobalSetting;

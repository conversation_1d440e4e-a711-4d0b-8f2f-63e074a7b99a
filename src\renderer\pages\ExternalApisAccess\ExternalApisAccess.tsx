import { AgGridReact } from 'ag-grid-react'
import styles from './ExternalApisAccess.module.scss'
import clsx from 'clsx'
import React, { useContext, useEffect, useState } from 'react'
import ReactPaginate from 'react-paginate'
import Loader from '../../components/common/Loader'
import { Accordion, AccordionDetails, AccordionSummary, MenuItem, Select, Tooltip } from '@mui/material'
import { useDebouncedValue } from '@mantine/hooks'
import { ReactComponent as ClearIcon } from '../../../assests/images/Close.svg';
import MatPopup from '../../components/common/MatPopup'
import useGetAdminReferenceData from '../../hooks/useGetAdminReferenceData'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import useGetExternalApiKeys from '../../hooks/useGetExternalApiKeys'
import usePostEditExternalApiKeyMapping from '../../hooks/usePostEditExternalApiKeyMapping'
import { CommonCtx } from '../AppContainer'
import { API_KEY_ENVIRONMENTS } from '../../utils/constant'

const ExternalApisAccess = () => {
  const showPopupFormAnyComponent = useContext(CommonCtx);
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [debouncedInputSearchValue] = useDebouncedValue(inputSearchValue, 1000);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [showConfirmationPopup, setShowConfirmationPopup] = useState(false);
  const [keyEndpointMapData, setKeyEndpointMappingData] = useState<any>({});
  const [adminReferenceDataEndpointMap, setAdminReferenceDataEndpointMap] = useState<any>({});
  const [payloadEndpoint, setPayloadEndpoint] = useState<any>({});
  const [meta, setMeta] = useState<any>(null);
  const [rowData, setRowData] = useState([]);
  const [isEnabledSubmitBtn, setIsEnabledSubmitBtn] = useState(false);
  const [apiKeyEnvironment, setApiKeyEnvironment] = useState(API_KEY_ENVIRONMENTS.sandbox);
  const [maximumSeatsAllocated,setMaximumSeatsAllocated] = useState<any>(0)

  const colDefs = [
    {
      field: "edit_api_key",
      headerName: ``,
      sortable: false,
      minWidth: 120,
      headerClass: 'poLineMatch',
      cellClass: 'poLineMatchCol',
      cellRenderer: (props: any) => {
        return (
          <button className={styles.editBtn} onClick={() => { handleEditApiKeyEndpointMapping(props?.data) }}>Edit</button>
        );
      },
    },
    {
      field: "api_key_name",
      headerName: `Name`,
      sortable: false,
      minWidth: 200,
      flex: 1,
      headerClass: 'poLineMatch',
      cellClass: 'poLineMatchCol'
    },
    {
      field: "company_name",
      headerName: 'Company Name',
      sortable: false,
      minWidth: 250,
      flex: 1,
    },
    {
      field: "api_key",
      headerName: "Key",
      minWidth: 500,
      flex: 1
    },
    {
      field: "api_key_purpose",
      headerName: "Purpose",
      minWidth: 200,
      flex: 1,
      cellClass: clsx(styles.truncateText),
      cellRenderer: (props: any) => {
        return (
          <Tooltip title={props?.data?.api_key_purpose}>
            {props?.data?.api_key_purpose}
          </Tooltip>
        );
      },
    },
    {
      field: "created_date",
      headerName: "Created Date",
      minWidth: 150,
      flex: 1,
      cellClass: clsx(styles.truncateText),
      cellRenderer: (props: any) => {
        return (
          <Tooltip title={props?.data?.created_date}>
            {props?.data?.created_date}
          </Tooltip>
        );
      },
    }
  ];

  const {
    data: getExternalApiKeysData,
    isLoading: isGetExternalApiKeysDataLoading,
  } = useGetExternalApiKeys(
    itemsPerPage,
    currentPage,
    debouncedInputSearchValue, 
    apiKeyEnvironment);

  const { data: adminReferenceData } = useGetAdminReferenceData();

  const {
    mutateAsync: editExternalApiKeyMapping,
    data: editExternalApiKeyMappingData,
    isLoading: isEditExternalApiKeyMappingLoading,
  } = usePostEditExternalApiKeyMapping(apiKeyEnvironment);



  useEffect(() => {
    if (isGetExternalApiKeysDataLoading) {
      return;
    }
    if (getExternalApiKeysData?.meta) {
      setMeta(getExternalApiKeysData.meta);
    }

    if (getExternalApiKeysData?.items?.length) {
      setRowData(getExternalApiKeysData.items);
    } else {
      setRowData([])
    }
  }, [getExternalApiKeysData, isGetExternalApiKeysDataLoading]);

  useEffect(() => {
    if (adminReferenceData?.external_api_endpoint) {
      setAdminReferenceDataEndpointMap(adminReferenceData?.external_api_endpoint);
    }
  }, [adminReferenceData]);

  useEffect(() => {
    if (keyEndpointMapData?.api_key && adminReferenceDataEndpointMap) {
      const payloadEndpointObj: any = {};
      for (const key in adminReferenceDataEndpointMap) {
        payloadEndpointObj[key] = {};
        const endpointList = adminReferenceDataEndpointMap[key];
        keyEndpointMapData.endpoint.forEach((item: string | number) => {
          if (endpointList.some((endpoint: { value: string | number }) => endpoint.value === item)) {
            payloadEndpointObj[key][item] = true;
          }
        });
        }
      setMaximumSeatsAllocated(keyEndpointMapData.user_slots ? keyEndpointMapData.user_slots: 0)
      setPayloadEndpoint(payloadEndpointObj);
      setShowConfirmationPopup(true);

    }
  }, [keyEndpointMapData, adminReferenceDataEndpointMap])

  useEffect(() => {
    if (editExternalApiKeyMappingData) {
      showPopupFormAnyComponent(editExternalApiKeyMappingData);
    }
  }, [editExternalApiKeyMappingData])

  const defaultColDef = {
    lockVisible: true,
    cellStyle: { flex: 1 },
  };

  const handlePageClick = (event: any) => {
    setCurrentPage(event.selected + 1);
  };

  const search = (searchValue: string) => {
    setCurrentPage(1);
    setInputSearchValue(searchValue);
  };

  const clearInput = () => {
    setInputSearchValue('');
  };

  const handleEditApiKeyEndpointMapping = (apiKeyEndpointMappingData) => {
    setKeyEndpointMappingData(apiKeyEndpointMappingData);
  }

  const editPopupClose = () => {
    setShowConfirmationPopup(false);
    setKeyEndpointMappingData({});
    setPayloadEndpoint({});
    setIsEnabledSubmitBtn(false);
  }

  const editPopupSubmit = () => {
    let endPoints: any[] = [];
    Object.keys(payloadEndpoint).forEach((endpointType) => {
      endPoints = [...endPoints, ...Object.keys(payloadEndpoint[endpointType])];
    })
    const payload = {
      "data": {
        "api_key_id": keyEndpointMapData.api_key_id,
        "endpoint": endPoints,
        "user_slots":(maximumSeatsAllocated === "")  ?  0 : Number(maximumSeatsAllocated),
      }
    }
    editExternalApiKeyMapping(payload);
    editPopupClose()
  }

  const handleKeyDown = (e) => {
    if (e.key === '.' || e.key === ',') {
      e.preventDefault();
    }
  };

  return (
    <div>
      <div className={styles.buttonContainer}>
        <button onClick={() => setApiKeyEnvironment(API_KEY_ENVIRONMENTS.sandbox)} className={clsx(apiKeyEnvironment === API_KEY_ENVIRONMENTS.sandbox && styles.activeTab)}>SANDBOX</button>
        <button onClick={() => setApiKeyEnvironment(API_KEY_ENVIRONMENTS.prod)} className={clsx(apiKeyEnvironment === API_KEY_ENVIRONMENTS.prod && styles.activeTab)}>PROD</button>
      </div>
      <div className={styles.searchBox}>
        <div className={styles.sortDataSection}>
          <Select
            className="editLinesDropdown emailAttachDropdown"
            MenuProps={{
              classes: {
                paper: styles.Dropdownpaper,
                list: styles.muiMenuList,
              },
            }}
            value={itemsPerPage}
            onChange={(event) => {
              setItemsPerPage(+event.target.value);
            }}
          >
            {perPageEntriesOptions.map((item, index) => (
              <MenuItem key={index} value={item}>
                <span>{item}</span>
              </MenuItem>
            ))}
          </Select>
        </div>
        <div className={styles.SortRightSection}>
          <div className={styles.searchContainer}>
            <input
              className={styles.searchInput}
              type="text"
              onChange={(e) => search(e.target.value)}
              placeholder="Search"
              value={inputSearchValue}
            />
            {inputSearchValue && (
              <button className={styles.clearInputIcon} onClick={clearInput}>
                <ClearIcon />
              </button>
            )}
          </div>
        </div>
      </div>
      {isGetExternalApiKeysDataLoading ||
        isEditExternalApiKeyMappingLoading ? (
        <div className={styles.noDataFound}>
          <Loader />
        </div>
      ) : (
        <div
          className={clsx(styles.ag_theme_quartz, styles.agGridAdmin)}
          style={{
            height: "calc(100vh - 300px)",
            width: "100%",
            minHeight: "400px",
          }}
        >
          <AgGridReact
            rowData={rowData}
            columnDefs={colDefs}
            sideBar={true}
            suppressCellFocus={true}
            rowHeight={70}
            headerHeight={32}
            enableCellTextSelection={true}
            defaultColDef={defaultColDef}
          />
        </div>
      )}
      <div className={"PaginationNumber"}>
        {meta && (
          <ReactPaginate
            breakLabel="..."
            nextLabel=">"
            onPageChange={handlePageClick}
            pageRangeDisplayed={5}
            pageCount={meta.totalPages}
            previousLabel="<"
            renderOnZeroPageCount={(props) =>
              props.pageCount > 0 ? undefined : null
            }
            forcePage={meta.currentPage > 0 ? meta.currentPage - 1 : undefined}
          />
        )}
      </div>
      <MatPopup
        classes={{
          paper: styles.externalApiPopup
        }}
        open={showConfirmationPopup}
      >
        <div className={styles.tblscrollPop}>
          <span className={styles.popupTitle}>Edit External API Key</span>
          <div className={styles.continuePopup}>
            <span className={styles.apiKeyName}>API Key : {keyEndpointMapData.api_key}</span>
            <div className={styles.inputDiv}>
              <label>Maximum Allowed Seats: </label>
              <input type="number" className={styles.companyNameInput} min={0} value={maximumSeatsAllocated} onChange={(e) => {  setMaximumSeatsAllocated(e.target.value); setIsEnabledSubmitBtn(true) }}   onKeyDown={handleKeyDown} />
            </div>
            <div className={styles.endPointMapping}>
              <span className={styles.popupTitle}>Key Endpoint Mapping</span>
              <div className={styles.overFlowForPop}>
                <span>
                  {(!!Object.keys(adminReferenceDataEndpointMap).length) &&
                    Object.keys(adminReferenceDataEndpointMap).map((endpointDataKey: any, index: number) => (
                      <ApiKeyEndpointAccordion key={index} endpointType={endpointDataKey} index={index} adminReferenceDataEndpointMap={adminReferenceDataEndpointMap} payloadEndpoint={payloadEndpoint} setPayloadEndpoint={setPayloadEndpoint} setIsEnabledSubmitBtn={setIsEnabledSubmitBtn} />
                    ))}
                </span>
            </div>
            </div>
          
            <div className={styles.yesAndnoBtn}>
              <button className={styles.okBtn} onClick={editPopupSubmit} disabled={!isEnabledSubmitBtn} >
                Submit
              </button>
              <button
                className={styles.okBtn}
                onClick={editPopupClose}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>

      </MatPopup>
    </div>
  )
}

export default ExternalApisAccess

export const ApiKeyEndpointAccordion = ({ endpointType, index, adminReferenceDataEndpointMap, payloadEndpoint, setPayloadEndpoint, setIsEnabledSubmitBtn }) => {
  const [isAllCheckedEndpoint, setIsAllCheckedEndpoint] = useState<boolean>(false);

  useEffect(() => {
    if (payloadEndpoint[endpointType] && Object.keys(payloadEndpoint[endpointType])) {
      setIsAllCheckedEndpoint(!!(Object.keys(payloadEndpoint[endpointType])?.length === adminReferenceDataEndpointMap[endpointType].length));
    }
  }, [adminReferenceDataEndpointMap, payloadEndpoint, endpointType])

  const handleEndpointCheckBox = (e: { target: { checked: boolean } }, endpointDataKey: any, type: string) => {
    const payloadEndpointObj: any = { ...payloadEndpoint };
    if (e.target.checked) {
      payloadEndpointObj[type][endpointDataKey] = e.target.checked;
    } else {
      delete payloadEndpointObj[type][endpointDataKey]
    }
    setPayloadEndpoint(payloadEndpointObj);
    setIsEnabledSubmitBtn(true)
  }

  const handleHeaderCheckbox = (e: { target: { checked: boolean | ((prevState: boolean) => boolean) } }) => {
    let allEndpointObjValueChange = { ...payloadEndpoint };
    adminReferenceDataEndpointMap[endpointType].forEach(endpointName => {
      if (e.target.checked) allEndpointObjValueChange[endpointType][endpointName.value] = true;
      else delete allEndpointObjValueChange[endpointType][endpointName.value];
    });
    setPayloadEndpoint(allEndpointObjValueChange)
    setIsEnabledSubmitBtn(true)
  }


  return (
    <Accordion
      defaultExpanded={index===0}
      className={styles.BOMaccordion}
      classes={{
        root: styles.accordionRoot,
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls={`${index}-content`}
        id={`${index}-header`}
        className={styles.AccordionSummary}
        classes={{
          content: styles.accordionTitle
        }}
      >
        <div className={styles.fileNameContainer}>
          {endpointType}
        </div>
        <span className={styles.btnSection}>
          <div>
            <label className={styles.containerChk}>
              <input type="checkbox" checked={isAllCheckedEndpoint} onChange={handleHeaderCheckbox} />
              <span className={styles.checkmark} />
            </label>
          </div>
        </span>

      </AccordionSummary>
      <AccordionDetails className={styles.AccordionDetails}>
        <div className={styles.innerContent}>
          {adminReferenceDataEndpointMap[endpointType].map((endpointData, i) => {
            return (
              <div key={i} className={styles.chkMain}>
                <div>
                  <label className={styles.containerChk}>
                    <input type="checkbox" checked={!!payloadEndpoint?.[endpointType]?.[endpointData.value]} onChange={(e) => handleEndpointCheckBox(e, endpointData.value, endpointType)}
                    />
                    <span className={styles.checkmark} />
                  </label>
                </div>
                <div>{endpointData.endpoint}</div>
              </div>
            )
          })}
        </div>
      </AccordionDetails>
    </Accordion>)
}
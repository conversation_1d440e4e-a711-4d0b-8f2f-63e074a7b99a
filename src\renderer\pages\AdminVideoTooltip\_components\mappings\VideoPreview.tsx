import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  VolumeUp as VolumeIcon,
  VolumeOff as VolumeOffIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { useLoadSubtitle } from '../../../../hooks/admin-video-tooltip/useLoadSubtitle';

interface VideoPreviewProps {
  videoUrl: string;
  captionUrl?: string;
  isCaptionEnabled?: boolean;
}

const VideoPreview = ({ videoUrl, captionUrl, isCaptionEnabled = false }: VideoPreviewProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [subtitleUrl, setSubtitleUrl] = useState<string | undefined>();
  const videoRef = useRef<HTMLVideoElement>(null);

  const { data: subtitle, isLoading: isSubtitleLoading } = useLoadSubtitle(
    captionUrl && isCaptionEnabled ? captionUrl : ''
  );

  // Handle play/pause
  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  // Handle mute/unmute
  const handleMuteToggle = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  // Handle video load
  const handleVideoLoad = () => {
    console.log('✅ Video loaded successfully:', {
      url: videoUrl,
      duration: videoRef.current?.duration,
      videoWidth: videoRef.current?.videoWidth,
      videoHeight: videoRef.current?.videoHeight
    });
    setIsLoading(false);
    setHasError(false);
    
    if (videoRef.current && !isEmbedVideo && subtitleUrl && isCaptionEnabled) {
      const video = videoRef.current;
      const tracks = video.textTracks;
      if (tracks && tracks.length > 0) {
        for (let i = 0; i < tracks.length; i++) {
          tracks[i].mode = 'showing';
        }
      }
    }
  };

  // Handle video error
  const handleVideoError = (e) => {
    console.log('❌ Video load error:', e);
    console.log('❌ Error details:', {
      url: videoUrl,
      error: e.target?.error,
      networkState: e.target?.networkState,
      readyState: e.target?.readyState,
      currentSrc: e.target?.currentSrc
    });
    setIsLoading(false);
    setHasError(true);
    setIsPlaying(false);
  };

  // Handle video ended
  const handleVideoEnded = () => {
    setIsPlaying(false);
  };

  // Convert YouTube URLs to embed format
  const getEmbedUrl = (url) => {
    // YouTube
    if (url.includes('youtube.com/watch?v=')) {
      const videoId = url.split('v=')[1].split('&')[0];
      return `https://www.youtube.com/embed/${videoId}`;
    }
    if (url.includes('youtu.be/')) {
      const videoId = url.split('youtu.be/')[1].split('?')[0];
      return `https://www.youtube.com/embed/${videoId}`;
    }
    
    // Vimeo
    if (url.includes('vimeo.com/')) {
      const videoId = url.split('vimeo.com/')[1].split('?')[0];
      return `https://player.vimeo.com/video/${videoId}`;
    }
    
    // Return original URL for direct video files
    return url;
  };

  const embedUrl = getEmbedUrl(videoUrl);
  const isEmbedVideo = embedUrl !== videoUrl;

  useEffect(() => {
    let objectUrl: string | undefined;
    if (!isSubtitleLoading && subtitle && captionUrl && isCaptionEnabled) {
      const subtitleBlob = new Blob([subtitle as BlobPart], {
        type: 'text/vtt',
      });
      objectUrl = URL.createObjectURL(subtitleBlob);
      setSubtitleUrl(objectUrl);
    } else {
      setSubtitleUrl(undefined);
    }
    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [isSubtitleLoading, subtitle, captionUrl, isCaptionEnabled]);

  useEffect(() => {
    if (videoRef.current && !isEmbedVideo) {
      const video = videoRef.current;
      const tracks = video.textTracks;
      
      if (tracks && tracks.length > 0) {
        for (let i = 0; i < tracks.length; i++) {
          if (subtitleUrl && isCaptionEnabled) {
            tracks[i].mode = 'showing';
          } else {
            tracks[i].mode = 'hidden';
          }
        }
      }
    }
  }, [subtitleUrl, isCaptionEnabled, isEmbedVideo]);

  if (hasError) {
    return (
      <Alert 
        severity="warning" 
        icon={<ErrorIcon />}
        sx={{ 
          backgroundColor: 'rgba(255, 152, 0, 0.1)',
          border: '1px solid rgba(255, 152, 0, 0.3)',
        }}
      >
        <Typography variant="body2">
          Unable to load video preview. The video URL may be invalid or the video may not be accessible.
        </Typography>
        <Typography variant="caption" sx={{ mt: 1, display: 'block', color: '#666' }}>
          URL: {videoUrl}
        </Typography>
      </Alert>
    );
  }

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        backgroundColor: '#000',
        borderRadius: 1,
        overflow: 'hidden',
        aspectRatio: '16/9',
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            zIndex: 2,
          }}
        >
          <CircularProgress size={32} sx={{ color: 'white' }} />
        </Box>
      )}

      {isEmbedVideo ? (
        // Embedded video (YouTube, Vimeo, etc.)
        <iframe
          src={embedUrl}
          width="100%"
          height="100%"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          onLoad={handleVideoLoad}
          onError={handleVideoError}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
          }}
        />
      ) : (
        // Direct video file
        <>
          <video
            ref={videoRef}
            src={videoUrl}
            onLoadedData={handleVideoLoad}
            onCanPlay={() => console.log('🎬 Video can play')}
            onLoadStart={() => console.log('🔄 Video load started')}
            onError={handleVideoError}
            onEnded={handleVideoEnded}
            muted={isMuted}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
            }}
          >
            {subtitleUrl && isCaptionEnabled && (
              <track
                kind="subtitles"
                src={subtitleUrl}
                srcLang="en"
                label="English"
                default
              />
            )}
          </video>
          
          {/* Video Controls */}
          {!isLoading && (
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                p: 1,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <IconButton
                onClick={handlePlayPause}
                size="small"
                sx={{ color: 'white' }}
              >
                {isPlaying ? <PauseIcon /> : <PlayIcon />}
              </IconButton>
              
              <IconButton
                onClick={handleMuteToggle}
                size="small"
                sx={{ color: 'white' }}
              >
                {isMuted ? <VolumeOffIcon /> : <VolumeIcon />}
              </IconButton>
              
              {/* <Typography variant="caption" sx={{ color: 'white', ml: 'auto' }}>
                Preview
              </Typography> */}
            </Box>
          )}
        </>
      )}
      
      {/* Fallback message for embed videos */}
      {isEmbedVideo && !isLoading && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 8,
            right: 8,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            px: 1,
            py: 0.5,
            borderRadius: 0.5,
            fontSize: '0.75rem',
          }}
        >
          External Video
        </Box>
      )}
    </Box>
  );
};

export default VideoPreview;

import React, { memo} from "react";
import { Dialog } from "@mui/material";
import styles from './DialogPopup.module.scss'
import { ReactComponent as CloseIcon } from '../../../../assests/images/Close.svg';
import useDialogStore, { DialogAction } from "./DialogStore";

const DialogBox = memo(() => {

    const { openDialog, header, content, type, onClose, actions } = useDialogStore();
    return (
        <Dialog
            open={openDialog}
            onClose={onClose}
            transitionDuration={200}
            classes={{
                root: styles.ErrorDialog,
                paper: styles.dialogContent
            }}
        >
            {type === 'success' && 
            <button className={styles.closeIcon} onClick={onClose}>
                <CloseIcon />
            </button>
            }
            {header && <>
            <p className={styles.successPopupTitle}>{header}</p>
            </>
            }
            {content && <>
              <p className={styles.content}>{content}</p>
            </>}
            <div className={styles.actionBtnSection}>
            {
                actions?.map((action: DialogAction, index: number)=>{
                    return <button key={index} className={styles.submitBtn} onClick={action.action}>{action.name}</button>
                })
            }
            </div>
          
        </Dialog>
    );
});


export default DialogBox;
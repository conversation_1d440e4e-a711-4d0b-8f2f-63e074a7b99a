.blockPalette {
  width: 180px;
  background: white;
  border-right: 1px solid #e0e0e0;
  padding: 12px;
  overflow-y: auto;

  h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.paletteItems {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.paletteItem {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: move;
  transition: all 0.2s;

  &:hover {
    background: #f0f0f0;
    border-color: #007bff;
  }

  &.dragging {
    opacity: 0.5;
  }

  span {
    font-size: 14px;
    color: #333;
  }
}

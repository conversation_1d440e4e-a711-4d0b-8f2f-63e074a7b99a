import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQuery<PERSON>eys } from "../utils/constant";

const useGetSalesTaxNexusData = () => {
  return useQuery(
    [reactQueryKeys.getSalesTaxNexusData],
    async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/sales-tax-nexus-data`
        );
        if (response.data?.data) {
          // Check if response contains error_message
          if (
            typeof response.data.data === "object" &&
            !Array.isArray(response.data.data) &&
            "error_message" in response.data.data
          ) {
            throw new Error(response.data.data.error_message);
          }
          // Check if data is an array (successful response)
          if (Array.isArray(response.data.data)) {
            return response.data.data;
          }
          // If data exists but is not an array and has no error_message, return it
          return response.data.data;
        } else {
          return null;
        }
      } catch (error: any) {
        throw new Error(error?.message);
      }
    },
    {
      staleTime: 0,
      cacheTime: 0,
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      retry: false,
    }
  );
};

export default useGetSalesTaxNexusData;

import { Controller, useForm } from "react-hook-form";
import Loader from "../../components/common/Loader";
import useRemoveSalesTax from "../../hooks/useRemoveSalesTax";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useContext, useEffect } from "react";
import useGetOpenPoNumberData from "../../hooks/useGetOpenPoNumberData";
import { Autocomplete, TextField, Tooltip } from "@mui/material";
import { useImmer } from "use-immer";
import styles from "./RemoveSalesTax.module.scss";
import { CommonCtx } from "../AppContainer";
import { confirmationPopupKeys} from "../../utils/constant";
import useDialogStore from "../../components/common/DialogPopup/DialogStore";

const schema = yup.object({
  poNumber: yup.string().required("PO number is required"),
});

type FormData = {
  poNumber: string;
};

const RemoveSalesTax = () => {
  const [poLists, setPoLists] = useImmer<any[]>([]);
  const {showCommonDialog, resetDialogStore } = useDialogStore();

  const showPopupFormAnyComponent = useContext(CommonCtx);

  const { data: openPoNumberData, isLoading: isOpenPoNumberDataLoading } =
    useGetOpenPoNumberData();

  const {
    mutate: removeSalesTax,
    error: removeSalesTaxError,
    data: removeSalesTaxData,
    isLoading: isRemoveSalesTaxLoading,
  } = useRemoveSalesTax();

  const { register, control, handleSubmit } = useForm<FormData>({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    if (!isOpenPoNumberDataLoading && openPoNumberData) {
      const _setPoLists: any[] = [];
      openPoNumberData.forEach((data: any, index: number) => {
        if (Number(data.sales_tax) > 0) {
          _setPoLists.push({
            id: index,
            title: data.buyer_po_number,
            value: data.buyer_po_number,
          });
        }
      });

      setPoLists(_setPoLists);
    }
  }, [isOpenPoNumberDataLoading, openPoNumberData]);

  useEffect(() => {
    if (
      !isRemoveSalesTaxLoading &&
      removeSalesTaxData &&
      !removeSalesTaxError
    ) {
      showPopupFormAnyComponent(removeSalesTaxData);
    }
  }, [removeSalesTaxData, isRemoveSalesTaxLoading]);

  const formSubmitHandler = (data: FormData) => {
    removeSalesTax({ data: { po_number: data.poNumber } });
  };

  const showRemoveSalesTaxPopup = () => {
    showCommonDialog(null, confirmationPopupKeys.confirmationContent, null, resetDialogStore, 
      [{name: confirmationPopupKeys.confirmation.yes, action: ()=>{removeSalesTaxPopup()}},{name: confirmationPopupKeys.confirmation.no, action: resetDialogStore}])
   }

  const removeSalesTaxPopup = () => {
    handleSubmit(formSubmitHandler)();
    resetDialogStore()
  };


  return (
    <div>
      {isRemoveSalesTaxLoading || isOpenPoNumberDataLoading ? (
        <Loader />
      ) : (
        <div className={styles.sendNotificationMain}>
          <div className={styles.title}> Remove sales tax </div>
            <label>
              <Controller
                name={register("poNumber").name}
                control={control}
                render={({
                  field: { onChange, value },
                  fieldState: { error },
                }) => (
                  <Tooltip
                    title={error?.message}
                    placement="top-end"
                    classes={{
                      popper: styles.errorStyle,
                      tooltip: styles.tooltip,
                    }}
                  >
                    <Autocomplete
                      className={styles.selectDropdown}
                      options={poLists}
                      value={
                        poLists.find((obj: any) => obj.value === value) ?? null
                      }
                      getOptionLabel={(option: any) => option.title ?? ""}
                      renderInput={(params) => (
                        <TextField {...params} label="Choose PO#" />
                      )}
                      onChange={(event, data: any) => {
                        onChange(data ? data.value : null);
                      }}
                      classes={{
                        root: styles.autoCompleteDesc,
                        popper: styles.autocompleteDescPanel,
                        paper: styles.autocompleteDescInnerPanel,
                        listbox: styles.listAutoComletePanel,
                      }}
                    />
                  </Tooltip>
                )}
              />
            </label>
            <div className={styles.btnSendNotif}>
              <button onClick={showRemoveSalesTaxPopup}>Submit</button>
            </div>
        </div>
      )}
    </div>
  );
};

export default RemoveSalesTax;

import { useImmer } from "use-immer";
import Loader from "../../../components/common/Loader";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import MatSelect from "../../../components/common/MatSelect";
import { format2DecimalPlaces } from "../../../utils/helper";
import clsx from "clsx";
import InputField from "../../../components/common/InputField";
import { yupResolver } from "@hookform/resolvers/yup";
import { makeApaymentSchema } from "../../../models/MakeApayment.model";
import styles from "./MakeApayment.module.scss";
import { CommonCtx } from "../../AppContainer";
import LoginPopup from "../../../components/LoginPopup";
import { Autocomplete, TextField, Tooltip } from "@mui/material";
import { logoNameList, makeApaymentErrorMessage } from "../../../utils/constant";
import useNodeGetCassTransactionData from "../../../hooks/useNodeGetCassTransactionData";
import useNodeGetSellerPaymentSetup from "../../../hooks/useNodeGetSellerPaymentSetup";
import useNodePostCassTransactionData from "../../../hooks/useNodePostCassTransactionData";
import AdHocPayment from "./AdHocPayment/AdHocPayment";
import useAdhocCassTransaction from "../../../hooks/useAdhocCassTransaction";
import useGetAdhocCassSellerSetup from "../../../hooks/useGetAdhocCassSellerSetup";
import useGetReferenceData from "../../../hooks/useGetReferenceData";
import useSaveAdhocSellerSetup from "../../../hooks/useSaveAdhocSellerSetup";
import { update } from "lodash-es";

const defaultValues: any = {
  selectedPoObj: {},
  selectedPoNumber: null,
  selectedPaymentMethod: null,
  sellerPayoutAmount: null,
  sellerPayoutStatementDescriptor: null,
  sellerPayoutInternalNote: null,
  sellerPayoutEmailId: null,
  bryzosHoldingsAmount: null,
  bryzosHoldingsStatementDescriptor: null,
  bryzosHoldingsInternalNote: null,
  salesTaxAmount: null,
  salesTaxStatementDescriptor: "",
  salesTaxInternalNote: null,
  bnplAmount: null,
  bnplStatementDescriptor: null,
  bnplInternalNote: null,
  createNewAccount: false,
  adHocAccountNumber: null,
  adHocRoutingNumber: null,
  adHocAmount: null,
  adHocStatementDescriptor: null,
  adHocInternalNote: null,
  adHocBryzosBuyer: false,
  sellerId: null,
  sellerCompany: null,
  pgpmMappingId: null,
  selectedSeller: null,
  cassDisbursementRoutingNumber: null,
  cassDisbursementAccountNumber: null,
  sellerInvoiceNumber:'',
  sellerInvoiceNumberBH:null,
  sellerInvoiceNumberST:'',
  sellerInvoiceNumberAH:'',
  sellerInvoiceNumberBNPL:''
};

const MakeApayment = () => {
  const [poLists, setPoLists] = useImmer([]);
  const [isUserReAuthenticated, setIsUserReAuthenticated] = useImmer(false);
  const [adhocCassSellerSetup, setAdhocCassSellerSetup] = useImmer<any>([]);
  const [showLoader, setShowLoader] = useImmer(false);
  const [newlyCreatedUser, setNewlyCreatedUser] = useImmer(null);

  const {
    data: cassTransactionData,
    isLoading: isCassTransactionLoading,
    isFetching: isCassTransactionFetching,
  } = useNodeGetCassTransactionData();

  const {
    mutate: getSellerPaymentSetup,
    data: sellerPaymentSetupData,
    isLoading: isSellerPaymentSetupLoading,
  } = useNodeGetSellerPaymentSetup();

  const {
    mutateAsync: makeCassTransaction,
    data: postCassTransactionData,
    isLoading: isPostCassTransactionDataLoading,
    error: postCassTransactionError,
  } = useNodePostCassTransactionData();

  const {
    data: adhocCassSellerSetupData,
    isLoading: isAdhocCassSellerSetupDataLoading,
  } = useGetAdhocCassSellerSetup();

  const {
    mutateAsync: adhocCassTransaction,
    data: adhocCassTransactionData,
    isLoading: isAdhocCassTransactionLoading,
  } = useAdhocCassTransaction();

  const {
    mutate: saveAdhocSellerSetup,
    data: saveAdhocSellerSetupData,
    isLoading: isSaveAdhocSellerSetupLoading,
  } = useSaveAdhocSellerSetup();

  const { data: referenceData, isLoading: isGetReferenceDataLoading } = useGetReferenceData();

  const {
    register,
    control,
    watch,
    getValues,
    setValue,
    reset,
    setError,
    clearErrors,
    formState: { isValid, errors },
  } = useForm({
    defaultValues,
    resolver: yupResolver(makeApaymentSchema),
  });
  const showPopupFormAnyComponent = useContext(CommonCtx);

  const paymentTypeSellerPayout = "seller_payout";
  const paymentTypeBnpl = "bnpl";
  const paymentTypeSalesTax = "sales_tax";
  const paymentTypeBryzosHoldings = "bryzos_holdings";

  const selectedPoNumber = watch("selectedPoNumber");
  const selectedPoObj: any = watch("selectedPoObj");

  const sellerPayoutAmount = watch("sellerPayoutAmount");
  const bryzosHoldingsAmount = watch("bryzosHoldingsAmount");
  const salesTaxAmount = watch("salesTaxAmount");
  const bnplAmount = watch("bnplAmount");

  const sellerInvoiceNumber = watch("sellerInvoiceNumber");
  const sellerInvoiceNumberBH = watch("sellerInvoiceNumberBH");
  const sellerInvoiceNumberST = watch("sellerInvoiceNumberST");
  const sellerInvoiceNumberAH = watch("sellerInvoiceNumberAH");
  const sellerInvoiceNumberBNPL = watch("sellerInvoiceNumberBNPL");

  useEffect(() => {
    if (!isCassTransactionLoading && cassTransactionData) {
      const _setPoLists = cassTransactionData.map(
        (data: any, index: number) => ({
          id: data.PO_NUMBER,
          title: data.PO_NUMBER,
          value: data.PO_NUMBER,
        })
      );

      setPoLists(_setPoLists);
      setSelectedObj();
    }
  }, [isCassTransactionLoading, cassTransactionData]);

  useEffect(() => {
    if (!isSellerPaymentSetupLoading && sellerPaymentSetupData) {
      let paymentMethod = null;

      if (
        sellerPaymentSetupData?.length &&
        sellerPaymentSetupData[0].payment_method
      ) {
        paymentMethod = sellerPaymentSetupData[0].payment_method;
      }

      setValue("selectedPaymentMethod", paymentMethod);
    }
  }, [isSellerPaymentSetupLoading, sellerPaymentSetupData]);

  useEffect(() => {
    if (
      !isPostCassTransactionDataLoading &&
      !isCassTransactionLoading &&
      !isCassTransactionFetching
    ) {
      if (postCassTransactionData) {
        showPopupFormAnyComponent(postCassTransactionData);
      }
      clearData();
      setDescriptorValue();
    }
  }, [
    isPostCassTransactionDataLoading,
    isCassTransactionLoading,
    isCassTransactionFetching,
    postCassTransactionData,
    postCassTransactionError,
  ]);

  useEffect(() => {
    if (!isAdhocCassTransactionLoading && adhocCassTransactionData) {
      showPopupFormAnyComponent(adhocCassTransactionData);
      clearData();
    }
  }, [adhocCassTransactionData, isAdhocCassTransactionLoading]);

  useEffect(() => {
    if (!isAdhocCassSellerSetupDataLoading && !isSellerPaymentSetupLoading) {
      if (adhocCassSellerSetupData?.length) {
        const adhocSellerData = adhocCassSellerSetupData[0].adhoc_seller.sort((a: any, b: any) => a.company_name.localeCompare(b.company_name));
        const bryzosSellerData = adhocCassSellerSetupData[0].bryzos_seller.sort((a: any, b: any) => a.company_name.localeCompare(b.company_name));
        let allSellers: any[] = [];
        if(sellerPaymentSetupData?.length){
          const currentSeller = { ...sellerPaymentSetupData[0], is_adhoc_seller: true };
          allSellers = [currentSeller, ...adhocSellerData, ...bryzosSellerData];
        }else{
          allSellers = [...adhocSellerData, ...bryzosSellerData];
        }

        setAdhocCassSellerSetup(allSellers);
      } else {
        setAdhocCassSellerSetup([]);
      }
      if (newlyCreatedUser) {
        setValue("selectedSeller", newlyCreatedUser);
        setNewlyCreatedUser(null);
      }
    }
  }, [adhocCassSellerSetupData, isAdhocCassSellerSetupDataLoading, sellerPaymentSetupData, isSellerPaymentSetupLoading]);
  
  useEffect(() => {
    if (saveAdhocSellerSetupData && !isSaveAdhocSellerSetupLoading) {
      showPopupFormAnyComponent(saveAdhocSellerSetupData);
    }
  }, [saveAdhocSellerSetupData, isSaveAdhocSellerSetupLoading]);

  const poChangeHandler = (poNumber: string) => {
    setValue("selectedPoNumber", poNumber);
    setSelectedObj();
    if(poNumber){
      getSellerPaymentSetup(poNumber);
    }
    clearData();
    setDescriptorValue();
  };

  const convertToNumber = (obj: any) => {
    for (const key in obj) {
      if(!obj[key]){
        continue;
      }
      if (!isNaN(obj[key])) {
        obj[key] = +obj[key];
      }
    }

    return obj;
  };

  const formatAmount = (value: number) => {
    if (value !== undefined && value !== null && !isNaN(+value)) {
      const _value = +value;
      return +_value.toFixed(2);
    } else {
      return 0;
    }
  };

  const setSelectedObj = () => {
    const poNumber = getValues("selectedPoNumber");

    const _selectedPoObj = cassTransactionData?.find(
      (item: any) => item.PO_NUMBER === poNumber
    );

    if (_selectedPoObj) {
      setValue("selectedPoObj", convertToNumber(_selectedPoObj));
    } else {
      setValue("selectedPoObj", null);
    }
  };

  const clearData = () => {
    setValue("sellerPayoutAmount", null);
    setValue("bryzosHoldingsAmount", null);
    setValue("salesTaxAmount", null);
    setValue("bnplAmount", null);
    setValue("sellerPayoutInternalNote", null);
    setValue("sellerPayoutEmailId", null);
    setValue("bryzosHoldingsInternalNote", null);
    setValue("salesTaxInternalNote", null);
    setValue("bnplInternalNote", null);
    setValue("sellerPayoutStatementDescriptor", null);
    setValue("bryzosHoldingsStatementDescriptor", null);
    setValue("salesTaxStatementDescriptor", "");
    setValue("bnplStatementDescriptor", null);
    setValue("adHocStatementDescriptor", null);
    setValue("adHocAccountNumber", null);
    setValue("adHocRoutingNumber", null);
    setValue("adHocAmount", null);
    setValue("adHocBryzosBuyer", false);
    setValue("selectedSeller", null);
    setValue("adHocInternalNote", null);
    setValue("sellerInvoiceNumber", '');
    setValue("sellerInvoiceNumberBH", '');
    setValue("sellerInvoiceNumberST", '');
    setValue("sellerInvoiceNumberBNPL", '');
    setValue("sellerInvoiceNumberAH", '');
  };

  const [canUpdateSellerDescriptor, setCanUpdateSellerDescriptor] = useState<boolean>(true);

  const updateSellerAdhocBankDescriptor = (valueStr:string, invNumber:string|null|undefined = null)=>{
    const _selectedPoObj: any = getValues("selectedPoObj");
    invNumber = invNumber?.trim();
    if (_selectedPoObj) {
      if(invNumber){
        setValue(
          valueStr,
          `Inv-${invNumber} ${_selectedPoObj.SELLER_STATEMENT_DESCRIPTOR}`
        );
      }else{
        setValue(
          valueStr,
          `${_selectedPoObj.SELLER_STATEMENT_DESCRIPTOR}`
        );
      }
    }
  }



  const setDescriptorValue = () => {
    const _selectedPoObj: any = getValues("selectedPoObj");

    if (_selectedPoObj) {
      setCanUpdateSellerDescriptor(true);
      updateSellerAdhocBankDescriptor("sellerPayoutStatementDescriptor");
      setValue(
        "bryzosHoldingsStatementDescriptor",
        _selectedPoObj.BRYZOS_HOLDING_STATEMENT_DESCRIPTOR
      );

      const sellerPayoutEmailId = _selectedPoObj.SELLER_AR_EMAIL === 0 ? null : _selectedPoObj.SELLER_AR_EMAIL;
      setValue(
        "sellerPayoutEmailId",
        sellerPayoutEmailId
      );
      if (_selectedPoObj?.BUYER_METHOD_OF_PAYMENT === "BNPL") {
        setValue(
          "bnplStatementDescriptor",
          _selectedPoObj.BNPL_STATEMENT_DESCRIPTOR
        );
      }
      setValue(
        "salesTaxStatementDescriptor",
        `${_selectedPoObj.PO_NUMBER} - Sales Tax - ${_selectedPoObj.SELLER_MAIN_COMPANY_NAME}`
      );
      updateSellerAdhocBankDescriptor("adHocStatementDescriptor");
      setValue("adHocStatementEmailId", null);
    }
  };

  const yesButtonClick = (uiType: string) => {
    if (selectedPoNumber && cassTransactionData?.length) {
      const i = cassTransactionData.findIndex(
        (item: any) => item.PO_NUMBER === selectedPoNumber
      );
      if (i > -1 && cassTransactionData[i][uiType] !== undefined) {
        cassTransactionData[i][uiType] = 0;
        getSellerPaymentSetup(selectedPoNumber);
      }
    }
  };

  const submitPayment = async (paymentType: string) => {
    const obj: any = {
      po_number: getValues("selectedPoNumber"),
      amount: null,
      payment_method: null,
      internal_note: null,
      statement_descriptor: null,
      seller_payment_method: getValues("selectedPaymentMethod") ?? null,
    };

    if(getValues("sellerPayoutEmailId")){
      const emails = getValues("sellerPayoutEmailId").split(';');
      const isValidData = (getValues("sellerPayoutEmailId").length === 0) ? true : emails.every((email: string) => email.trim() && isEmail(email.trim()));
      if(paymentType === paymentTypeSellerPayout && !isValidData){
          setError('sellerPayoutEmailId', {message:'Enter valid email'}, { shouldFocus: true })
          return;
      }
    }

    switch (paymentType) {
      case paymentTypeSellerPayout:
        obj.amount = getValues("sellerPayoutAmount");
        obj.payment_method = paymentTypeSellerPayout;
        obj.statement_descriptor = getValues("sellerPayoutStatementDescriptor");
        obj.internal_note = getValues("sellerPayoutInternalNote");
        obj.to_email = (!getValues('sellerPayoutEmailId') || getValues("sellerPayoutEmailId").length === 0) ? null : getValues("sellerPayoutEmailId");
        obj.seller_invoice_number = getValues("sellerInvoiceNumber")
        if (getValues("sellerPayoutAmount")) {
          console.log(obj);
          await makeCassTransaction(obj);
          getSellerPaymentSetup(getValues("selectedPoNumber"));
        }
        break;
      case paymentTypeBryzosHoldings:
        obj.amount = getValues("bryzosHoldingsAmount");
        obj.payment_method = paymentTypeBryzosHoldings;
        obj.statement_descriptor = getValues(
          "bryzosHoldingsStatementDescriptor"
        );
        
        obj.seller_invoice_number = getValues("sellerInvoiceNumberBH")
        obj.internal_note = getValues("bryzosHoldingsInternalNote");
        if (getValues("bryzosHoldingsAmount")) {
          makeCassTransaction(obj);
        }
        break;
      case paymentTypeSalesTax:
        obj.amount = getValues("salesTaxAmount");
        obj.payment_method = paymentTypeSalesTax;
        obj.statement_descriptor = getValues("salesTaxStatementDescriptor");
        obj.seller_invoice_number = getValues("sellerInvoiceNumberST")
        obj.internal_note = getValues("salesTaxInternalNote");
        if (getValues("salesTaxAmount")) {
          makeCassTransaction(obj);
        }
        break;
      case paymentTypeBnpl:
        obj.amount = getValues("bnplAmount");
        obj.payment_method = paymentTypeBnpl;
        obj.internal_note = getValues("bnplInternalNote");
        obj.seller_invoice_number = getValues("sellerInvoiceNumberBNPL")
        obj.statement_descriptor = getValues("bnplStatementDescriptor");
        if (getValues("bnplAmount")) {
          makeCassTransaction(obj);
        }
        break;
    }
  };

  
  const isEmail = (email: string) => {
    const emailPattern = new RegExp('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$');
    return emailPattern.test(email);
}

  return (
    <div className="contentMain">
      {isCassTransactionLoading || showLoader || isSaveAdhocSellerSetupLoading ||
        isSellerPaymentSetupLoading ||
        isCassTransactionFetching ||
        isAdhocCassTransactionLoading ||
        isAdhocCassSellerSetupDataLoading ||
        isGetReferenceDataLoading ||
        isAdhocCassSellerSetupDataLoading ||
        isPostCassTransactionDataLoading ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : isUserReAuthenticated ? (
        <div>
          <Controller
            name="selectedPoNumber"
            control={control}
            render={({
              field: { onChange, onBlur, value, name, ref },
              fieldState: { error },
            }) => (
              <Autocomplete
                className={styles.selectDropdown}
                options={poLists}
                value={poLists.find((obj: any) => obj.value === value) ?? null}
                getOptionLabel={(option: any) => option.title ?? ""}
                renderInput={(params) => (
                  <TextField {...params} label="Choose PO#" />
                )}
                onChange={(event, data: any) => {
                  poChangeHandler(data ? data.value : null);
                  onChange(data ? data.value : null);
                }}
                classes={{
                  root: styles.autoCompleteDesc,
                  popper: styles.autocompleteDescPanel,
                  paper: styles.autocompleteDescInnerPanel,
                  listbox: styles.listAutoComletePanel,
                }}
              />
            )}
          />
          {selectedPoNumber && selectedPoObj && (
            <div className={styles.headingErrors}>
              {selectedPoObj.RCDC_PENDING == 1 && (
                <p>{makeApaymentErrorMessage.rcdcPending}</p>
              )}
              {!selectedPoObj.SELLER_MAIN_COMPANY_NAME ? (
                <p>{makeApaymentErrorMessage.orderIsNotClaimed}</p>
              ) 
              :selectedPoObj.SELLER_SETUP == 0 && (
                <p>{makeApaymentErrorMessage.notSellerSetup}</p>
              )}
            </div>
          )}

          <p>Method of Payment: {selectedPoObj?.BUYER_METHOD_OF_PAYMENT}</p>

          {!!selectedPoObj?.SELLER_OVERLAY && (
            <div>
              <p>{selectedPoObj?.OVERLAY_MESSAGE}</p>
              <button onClick={() => yesButtonClick("SELLER_OVERLAY")}>
                Yes
              </button>
            </div>
          )}
          <div className={styles.cassRow}>

            <div
              className={clsx(
                styles.cassCollg3,
                styles.cassColmd6,
                styles.cassColsm12,
                styles.colum,
                styles.colum1,
                styles.cassMainTable
              )}
            >
              <div
                className={clsx(styles.paymentHeader, styles.paymentHeader1)}
              >
                <label>
                  Seller&nbsp;
                  {!!(selectedPoNumber && selectedPoObj?.SELLER_MAIN_COMPANY_NAME)  && (
                    <span>- ({selectedPoObj.SELLER_MAIN_COMPANY_NAME}) </span>
                  )}
                  Payment
                </label>
              </div>
              <table>
                <tbody>
                  <tr>
                    <td>Final Gross Sale</td>
                    <td
                      className={clsx(
                        formatAmount(selectedPoObj?.EXTENDED) < 0 &&
                        styles.negValue
                      )}
                    >
                      {selectedPoNumber && (
                        <span>
                          $ {format2DecimalPlaces(selectedPoObj?.EXTENDED)}
                        </span>
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Sales Tax</td>
                    <td
                      className={clsx(
                        formatAmount(selectedPoObj?.TOTAL_SELLER_SALES_TAX) <
                        0 && styles.negValue
                      )}
                    >
                      {selectedPoNumber && (
                        <span>
                          ${" "}
                          {format2DecimalPlaces(
                            selectedPoObj?.TOTAL_SELLER_SALES_TAX
                          )}
                        </span>
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Rake</td>
                    <td
                      className={clsx(
                        formatAmount(selectedPoObj?.RAKE) < 0 && styles.negValue
                      )}
                    >
                      {selectedPoNumber && (
                        <span>
                          $ {format2DecimalPlaces(selectedPoObj?.RAKE)}
                        </span>
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Total to Seller</td>
                    <td
                      className={clsx(
                        formatAmount(selectedPoObj?.TOTAL_TO_SELLER) < 0 &&
                        styles.negValue
                      )}
                    >
                      {selectedPoNumber && (
                        <span>
                          ${" "}
                          {format2DecimalPlaces(selectedPoObj?.TOTAL_TO_SELLER)}
                        </span>
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Total Paid to Seller</td>
                    <td
                      className={clsx(
                        formatAmount(selectedPoObj?.TOTAL_PAID_TO_SELLER) < 0 &&
                        styles.negValue
                      )}
                    >
                      {selectedPoNumber && (
                        <span>
                          ${" "}
                          {format2DecimalPlaces(
                            selectedPoObj?.TOTAL_PAID_TO_SELLER
                          )}
                        </span>
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Balance due Seller</td>
                    <td
                      className={clsx(
                        formatAmount(selectedPoObj?.BALANCE_DUE_SELLER) < 0 &&
                        styles.negValue
                      )}
                    >
                      {selectedPoNumber && (
                        <span>
                          ${" "}
                          {format2DecimalPlaces(
                            selectedPoObj?.BALANCE_DUE_SELLER
                          )}
                        </span>
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Enter Payment Amount:</td>
                    <td>
                      <span className={clsx(styles.dFlex, styles.aligncenter)}>
                        <span>$</span>
                        <input
                          className={styles.paymentAmountInput}
                          type="number"
                          onWheel={ event => event.currentTarget.blur() }
                          min={0}
                          step="0.0001"
                          disabled={selectedPoObj?.SELLER_SETUP !== 1}
                          {...register("sellerPayoutAmount", {
                            valueAsNumber: true,
                          })}
                        />
                        <span>USD</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Seller Invoice Number:</td>
                    <td>
                      <span className={clsx(styles.dFlex, styles.aligncenter)}>
                        <input
                          className={styles.paymentAmountInput}
                          disabled={selectedPoObj?.SELLER_SETUP !== 1}
                          {...register("sellerInvoiceNumber")}
                          onChange={(e)=>{
                            register("sellerInvoiceNumber").onChange(e);
                            canUpdateSellerDescriptor && updateSellerAdhocBankDescriptor("sellerPayoutStatementDescriptor",e.target.value)
                          }}
                        />
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Routing Number</td>
                    <td>
                      {selectedPoNumber && (
                        <span>
                          {sellerPaymentSetupData?.length &&
                            sellerPaymentSetupData[0].routing_number
                            ? sellerPaymentSetupData[0].routing_number
                            : "-"}
                        </span>
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Account Number</td>
                    <td>
                      {selectedPoNumber && (
                        <span>
                          {sellerPaymentSetupData?.length &&
                            sellerPaymentSetupData[0].account_number
                            ? sellerPaymentSetupData[0].account_number
                            : "-"}
                        </span>
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={2}>
                      <input
                        placeholder="Bank Statement Descriptor"
                        disabled={
                          !selectedPoNumber || selectedPoObj?.SELLER_SETUP !== 1
                        }
                        {...register("sellerPayoutStatementDescriptor")}
                        onChange={(e)=>{register("sellerPayoutStatementDescriptor").onChange(e);setCanUpdateSellerDescriptor(false)}}
                      />
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={2}>
                      <textarea
                        placeholder="Enter Internal Note (optional)"
                        disabled={
                          !selectedPoNumber || selectedPoObj?.SELLER_SETUP !== 1
                        }
                        {...register("sellerPayoutInternalNote")}
                      ></textarea>
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={2}>
                      <Tooltip
                        title={errors?.sellerPayoutEmailId?.message ?? ''}
                        placement="top-end"
                        classes={{
                          popper: styles.errorStyle,
                          tooltip: styles.tooltip,
                        }}
                      >
                        <input
                          type="text"
                          placeholder="Email id (multiple separate with a semicolon)"
                          disabled={
                            !selectedPoNumber || selectedPoObj?.SELLER_SETUP !== 1
                          }
                          {...register("sellerPayoutEmailId")}
                          onChange={(e)=>{
                              register("sellerPayoutEmailId").onChange(e);
                              clearErrors('sellerPayoutEmailId')
                          }}
                        />
                      </Tooltip>
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={2} align="center">
                      <button
                        className={styles.submitBtn}
                        onClick={() => submitPayment(paymentTypeSellerPayout)}
                        disabled={
                          isNaN(sellerPayoutAmount) ||
                          0 >= sellerPayoutAmount ||
                          selectedPoObj?.SELLER_SETUP !== 1
                        }
                      >
                        Submit Payment
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div
              className={clsx(
                styles.cassCollg3,
                styles.cassColmd6,
                styles.cassColsm12,
                styles.colum,
                styles.colum2,
                styles.cassMainTable
              )}
            >
              {!!selectedPoObj?.BRYZOS_HOLDINGS_OVERLAY && (
                <div>
                  <p>{selectedPoObj?.OVERLAY_MESSAGE}</p>
                  <button
                    onClick={() => yesButtonClick("BRYZOS_HOLDINGS_OVERLAY")}
                  >
                    Yes
                  </button>
                </div>
              )}
              <div>
                <div
                  className={clsx(styles.paymentHeader, styles.paymentHeader2)}
                >
                  Bryzos Holdings
                </div>
                <table>
                  <tbody>
                    <tr>
                      <td>Final Gross Value</td>
                      <td
                        className={clsx(
                          formatAmount(selectedPoObj?.CHECKOUT_PRICE) < 0 &&
                          styles.negValue
                        )}
                      >
                        {selectedPoNumber && (
                          <span>
                            {selectedPoObj &&
                              format2DecimalPlaces(
                                selectedPoObj?.CHECKOUT_PRICE
                              )}
                          </span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>Gross Rake</td>
                      <td
                        className={clsx(
                          formatAmount(selectedPoObj?.RAKE) < 0 &&
                          styles.negValue
                        )}
                      >
                        {selectedPoNumber && (
                          <span>
                            {format2DecimalPlaces(selectedPoObj?.RAKE)}
                          </span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      {selectedPoObj?.BUYER_METHOD_OF_PAYMENT ==
                        "ACH (credit)" ? (
                        <>
                          <td>
                            <span> Cass Fee</span>
                          </td>
                          <td
                            className={clsx(
                              formatAmount(selectedPoObj?.CASS_FEE) < 0 &&
                              styles.negValue
                            )}
                          >
                            <span>
                              {format2DecimalPlaces(selectedPoObj?.CASS_FEE)}
                            </span>
                          </td>
                        </>
                      ) : (
                        <>
                          <td>
                            <span> BNPL Fee</span>
                          </td>
                          <td
                            className={clsx(
                              formatAmount(selectedPoObj?.BNPL_FEE) < 0 &&
                              styles.negValue
                            )}
                          >
                            {selectedPoNumber && (
                              <span>
                                {format2DecimalPlaces(selectedPoObj?.BNPL_FEE)}
                              </span>
                            )}
                          </td>
                        </>
                      )}
                    </tr>
                    <tr>
                      <td>Total to Bryzos</td>
                      <td
                        className={clsx(
                          formatAmount(selectedPoObj?.TOTAL_TO_BRYZOS) < 0 &&
                          styles.negValue
                        )}
                      >
                        {selectedPoNumber && (
                          <span>
                            {format2DecimalPlaces(
                              selectedPoObj?.TOTAL_TO_BRYZOS
                            )}
                          </span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>Total Paid to {import.meta.env.VITE_CLIENT_NAME}</td>
                      <td
                        className={clsx(
                          formatAmount(selectedPoObj?.TOTAL_PAID_TO_BRYZOS) <
                          0 && styles.negValue
                        )}
                      >
                        {selectedPoNumber && (
                          <span>
                            {format2DecimalPlaces(
                              selectedPoObj?.TOTAL_PAID_TO_BRYZOS
                            )}
                          </span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>Balance due {import.meta.env.VITE_CLIENT_NAME}</td>
                      <td
                        className={clsx(
                          formatAmount(selectedPoObj?.BALANCE_DUE_BRYZOS) < 0 &&
                          styles.negValue
                        )}
                      >
                        {selectedPoNumber && (
                          <span>
                            {format2DecimalPlaces(
                              selectedPoObj?.BALANCE_DUE_BRYZOS
                            )}
                          </span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>Enter Payment Amount:</td>
                      <td>
                        <span
                          className={clsx(styles.dFlex, styles.aligncenter)}
                        >
                          <span>$</span>
                          <input
                            className={clsx(styles.paymentAmountInput)}
                            type="number"
                            onWheel={ event => event.currentTarget.blur() }
                            min={0}
                            step="0.0001"
                            disabled={selectedPoObj?.SELLER_SETUP !== 1}
                          {...register("bryzosHoldingsAmount", {
                            valueAsNumber: true,
                          })}
                          />
                          <span>USD</span>
                        </span>
                      </td>
                    </tr>
                    <tr>
                    <td>Seller Invoice Number:</td>
                    <td>
                      <span className={clsx(styles.dFlex, styles.aligncenter)}>
                        <input
                         disabled={selectedPoObj?.SELLER_SETUP !== 1}
                          className={styles.paymentAmountInput}
                          {...register("sellerInvoiceNumberBH")}
                        />
                      </span>
                    </td>
                  </tr>
                    <tr>
                      <td></td>
                    </tr>
                    <tr>
                      <td></td>
                    </tr>
                    <tr>
                      <td colSpan={2}>
                        <input
                          type="text"
                          placeholder="Bank Statement Descriptor"
                          disabled={selectedPoObj?.SELLER_SETUP !== 1}
                          {...register("bryzosHoldingsStatementDescriptor")}
                        />
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={2}>
                        <textarea
                          placeholder="Enter Internal Note (optional)"
                          {...register("bryzosHoldingsInternalNote")}
                          disabled={selectedPoObj?.SELLER_SETUP !== 1}
                        ></textarea>
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={2} align="center">
                        <button
                          className={styles.submitBtn}
                          onClick={() =>
                            submitPayment(paymentTypeBryzosHoldings)
                          }
                          disabled={
                            isNaN(bryzosHoldingsAmount) ||
                            0 >= bryzosHoldingsAmount
                          }
                        >
                          Submit Payment
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div
              className={clsx(
                styles.cassCollg3,
                styles.cassColmd6,
                styles.cassColsm12,
                styles.colum,
                styles.colum3,
                styles.cassMainTable
              )}
            >
              {!!selectedPoObj?.SALES_TAX_OVERLAY && (
                <div>
                  <p>{selectedPoObj?.OVERLAY_MESSAGE}</p>
                  <button onClick={() => yesButtonClick("SALES_TAX_OVERLAY")}>
                    Yes
                  </button>
                </div>
              )}
              <div>
                <div
                  className={clsx(styles.paymentHeader, styles.paymentHeader3)}
                >
                  Sales Tax
                </div>
                <table>
                  <img
                    className={styles.bgImg}
                    src="https://prod-bryzos-assets.imgix.net/img/uncle-tax-sam.png?q=10&auto=format&flip=h"
                    alt="uncle-sam"
                  />
                  <tbody>
                    <tr>
                      <td>Material Sale</td>
                      <td
                        className={clsx(
                          formatAmount(selectedPoObj?.MATERIAL_SALE) < 0 &&
                          styles.negValue
                        )}
                      >
                        {selectedPoNumber && (
                          <span>
                            ${" "}
                            {format2DecimalPlaces(selectedPoObj?.MATERIAL_SALE)}
                          </span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>Total Sales Tax</td>
                      <td
                        className={clsx(
                          formatAmount(selectedPoObj?.TOTAL_SALES_TAX) < 0 &&
                          styles.negValue
                        )}
                      >
                        {selectedPoNumber && (
                          <span>
                            ${" "}
                            {format2DecimalPlaces(
                              selectedPoObj?.TOTAL_SALES_TAX
                            )}
                          </span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>Total Sales Tax Paid</td>
                      <td
                        className={clsx(
                          formatAmount(selectedPoObj?.TOTAL_SALES_TAX_PAID) <
                          0 && styles.negValue
                        )}
                      >
                        {selectedPoNumber && (
                          <span>
                            ${" "}
                            {format2DecimalPlaces(
                              selectedPoObj?.TOTAL_SALES_TAX_PAID
                            )}
                          </span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>Balance due Sales Tax</td>
                      <td
                        className={clsx(
                          formatAmount(selectedPoObj?.BALANCE_DUE_SALES_TAX) <
                          0 && styles.negValue
                        )}
                      >
                        {selectedPoNumber && (
                          <span>
                            ${" "}
                            {format2DecimalPlaces(
                              selectedPoObj?.BALANCE_DUE_SALES_TAX
                            )}
                          </span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={2} className={styles.noTaxesDueTitle}>
                        {selectedPoObj?.BALANCE_DUE_SALES_TAX === 0 && (
                          <span>No Taxes Due</span>
                        )}
                      </td>
                    </tr>
                    <tr>
                    <td>Seller Invoice Number:</td>
                    <td>
                      <span className={clsx(styles.dFlex, styles.aligncenter)}>
                        <input
                          className={styles.paymentAmountInput}
                          onWheel={ event => event.currentTarget.blur() }
                          disabled={!selectedPoNumber}
                          {...register("sellerInvoiceNumberST")}
                        />
                      </span>
                    </td>
                  </tr>
                  
                    <tr>
                      <td>Enter Payment Amount:</td>
                      <td>
                        <span
                          className={clsx(styles.dFlex, styles.aligncenter)}
                        >
                          <span>$</span>
                          <input
                            className={styles.paymentAmountInput}
                            type="number"
                            onWheel={ event => event.currentTarget.blur() }
                            min={0}
                            step="0.0001"
                            disabled={!selectedPoNumber}
                            {...register("salesTaxAmount", {
                              valueAsNumber: true,
                            })}
                          />
                          <span>USD</span>
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td></td>
                    </tr>
                    <tr>
                      <td></td>
                    </tr>
                    <tr>
                      <td colSpan={2}>
                        <input
                          placeholder="Bank Statement Descriptor"
                          disabled={!selectedPoNumber}
                          {...register("salesTaxStatementDescriptor")}
                        />
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={2}>
                        <textarea
                          placeholder="Enter Internal Note (optional)"
                          {...register("salesTaxInternalNote")}
                          disabled={!selectedPoNumber}
                        ></textarea>
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={2} align="center">
                        <button
                          className={styles.submitBtn}
                          onClick={() => submitPayment(paymentTypeSalesTax)}
                          disabled={
                            isNaN(salesTaxAmount) || 0 >= salesTaxAmount
                          }
                        >
                          Submit Payment
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            { (!selectedPoObj || selectedPoObj.IS_BNPL === 1) &&
              <div
                className={clsx(
                  styles.cassCollg3,
                  styles.cassColmd6,
                  styles.cassColsm12,
                  styles.colum,
                  styles.colum4,
                  styles.cassMainTable
                )}
              >
                {!!selectedPoObj?.BNPL_OVERLAY && (
                  <div>
                    <p>{selectedPoObj?.OVERLAY_MESSAGE}</p>
                    <button onClick={() => yesButtonClick("BNPL_OVERLAY")}>
                      Yes
                    </button>
                  </div>
                )}
                <div className={styles.vLine}></div>
                <div>
                  <div
                    className={clsx(styles.paymentHeader, styles.paymentHeader4)}
                  >
                    BNPL
                  </div>
                  <table>
                    <tbody>
                      <tr>
                        <td>Material Sale</td>
                        <td>
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT === "BNPL" && (
                              <span
                                className={clsx(
                                  formatAmount(selectedPoObj?.MATERIAL_SALE) <
                                  0 && styles.negValue
                                )}
                              >
                                ${" "}
                                {format2DecimalPlaces(
                                  selectedPoObj?.MATERIAL_SALE
                                )}
                              </span>
                            )}
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT !== "BNPL" && (
                              <span>-</span>
                            )}
                        </td>
                      </tr>
                      <tr>
                        <td>Sales Tax</td>
                        <td>
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT === "BNPL" && (
                              <span
                                className={clsx(
                                  formatAmount(selectedPoObj?.TOTAL_SALES_TAX) <
                                  0 && styles.negValue
                                )}
                              >
                                $
                                {format2DecimalPlaces(
                                  selectedPoObj?.TOTAL_SALES_TAX
                                )}
                              </span>
                            )}
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT !== "BNPL" && (
                              <span>-</span>
                            )}
                        </td>
                      </tr>
                      <tr>
                        <td></td>
                        <td></td>
                      </tr>
                      <tr>
                        <td>Total to BNPL</td>
                        <td>
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT === "BNPL" && (
                              <span
                                className={clsx(
                                  formatAmount(selectedPoObj?.CHECKOUT_PRICE) <
                                  0 && styles.negValue
                                )}
                              >
                                $
                                {format2DecimalPlaces(
                                  selectedPoObj?.CHECKOUT_PRICE
                                )}
                              </span>
                            )}
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT !== "BNPL" && (
                              <span>-</span>
                            )}
                        </td>
                      </tr>
                      <tr>
                        <td>Total Paid to BNPL</td>
                        <td>
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT === "BNPL" && (
                              <span
                                className={clsx(
                                  formatAmount(
                                    selectedPoObj?.TOTAL_PAID_TO_BNPL
                                  ) < 0 && styles.negValue
                                )}
                              >
                                $
                                {format2DecimalPlaces(
                                  selectedPoObj?.TOTAL_PAID_TO_BNPL
                                )}
                              </span>
                            )}
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT !== "BNPL" && (
                              <span>-</span>
                            )}
                        </td>
                      </tr>
                      <tr>
                        <td>Balance due BNPL</td>
                        <td>
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT === "BNPL" && (
                              <span
                                className={clsx(
                                  formatAmount(selectedPoObj?.BALANCE_DUE_BNPL) <
                                  0 && styles.negValue
                                )}
                              >
                                $
                                {format2DecimalPlaces(
                                  selectedPoObj?.BALANCE_DUE_BNPL
                                )}
                              </span>
                            )}
                          {selectedPoNumber &&
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT !== "BNPL" && (
                              <span>-</span>
                            )}
                        </td>
                      </tr>
                      <tr>
                    <td>Seller Invoice Number:</td>
                    <td>
                      <span className={clsx(styles.dFlex, styles.aligncenter)}>
                        <input
                          className={styles.paymentAmountInput}
                          onWheel={ event => event.currentTarget.blur() }
                          disabled={
                            !selectedPoNumber ||
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT !== "BNPL"
                          }
                          {...register("sellerInvoiceNumberBNPL")}
                        />
                      </span>
                    </td>
                  </tr>
                    <tr>
                      <td>Enter Payment Amount:</td>
                      <td>
                        <span
                          className={clsx(styles.dFlex, styles.aligncenter)}
                        >
                          <span>$</span>
                          <input
                            className={styles.paymentAmountInput}
                            type="number"
                            onWheel={ event => event.currentTarget.blur() }
                            min={0}
                            step="0.0001"
                            disabled={
                              !selectedPoNumber ||
                              selectedPoObj?.BUYER_METHOD_OF_PAYMENT !== "BNPL"
                            }
                            {...register("bnplAmount", { valueAsNumber: true })}
                          />
                          <span>USD</span>
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td></td>
                    </tr>
                    <tr>
                      <td></td>
                    </tr>
                    <tr>
                      <td colSpan={2}>
                        <InputField
                          control={control}
                          fieldName="bnplStatementDescriptor"
                          placeholder="Bank Statement Descriptor"
                          disabled={
                            !selectedPoNumber ||
                            selectedPoObj?.BUYER_METHOD_OF_PAYMENT !== "BNPL"
                          }
                        />
                        </td>
                      </tr>
                      <tr>
                        <td colSpan={2}>
                          <textarea
                            placeholder="Enter Internal Note (optional)"
                            {...register("bnplInternalNote")}
                            disabled={
                              !selectedPoNumber ||
                              selectedPoObj?.BUYER_METHOD_OF_PAYMENT !== "BNPL"
                            }
                          ></textarea>
                        </td>
                      </tr>
                      <tr>
                        {
                          <td colSpan={2} align="center">
                            <button
                              className={styles.submitBtn}
                              onClick={() => submitPayment(paymentTypeBnpl)}
                              disabled={isNaN(bnplAmount) || 0 >= bnplAmount}
                            >
                              Submit Payment
                            </button>
                          </td>
                        }
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            }
            <AdHocPayment
                {...{
                  control, watch, selectedPoNumber, selectedPoObj, formatAmount, register, getValues, setValue, isValid, errors, setNewlyCreatedUser,
                  adhocCassTransaction, adhocCassSellerSetup, referenceData, setShowLoader, saveAdhocSellerSetup, setError, clearErrors, isEmail,
                  getSellerPaymentSetup, updateSellerAdhocBankDescriptor
                }}
              />
             
          </div>
        </div>
      ) : (
        <LoginPopup
          isUserReAuthenticated={isUserReAuthenticated}
          setIsUserReAuthenticated={setIsUserReAuthenticated}
          logoName={logoNameList.bryzosPayLogo}
        />
      )}
    </div>
  );
};

export default MakeApayment;

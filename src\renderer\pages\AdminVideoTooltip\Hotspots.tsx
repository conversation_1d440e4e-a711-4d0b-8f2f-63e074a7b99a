import  { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  CircularProgress,
  Link,
  Grid,
  Chip,
  Tooltip,
} from '@mui/material';
import { ArrowBack as ArrowBackIcon, Save as SaveIcon } from '@mui/icons-material';
import { useNotification } from '../../contexts/NotificationContext';
import { useSafeNavigation } from '../../hooks/useNavigationWarning';
import useGetScreen from '../../hooks/admin-video-tooltip/useGetScreen';
import useGetScreens from '../../hooks/admin-video-tooltip/useGetScreens';
import { useSaveHotspots } from '../../hooks/admin-video-tooltip/useSaveHotspots';
import { useDeleteHotspot } from '../../hooks/admin-video-tooltip/useDeleteHotspot';
import HotspotToolbar from './_components/hotspots/HotspotToolbar';
import HotspotCanvas from './_components/hotspots/HotspotCanvas';
import HotspotProperties from './_components/hotspots/HotspotProperties';
import HotspotsList from './_components/hotspots/HotspotsList';
import { routePaths } from '../../utils/constant';
import NavigationWarningDialog from '../../components/common/NavigationWarningDialog/NavigationWarningDialog';

const HotspotsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { showNotification } = useNotification() as any;
  const stageRef = useRef<any>();

  const { data: screenData, isLoading: loading, error: screenError, refetch } = useGetScreen(id || '');
  const { data: allScreensData } = useGetScreens();
  const saveHotspotsMutation = useSaveHotspots();
  const deleteHotspotMutation = useDeleteHotspot();

  // State
  const [screen, setScreen] = useState<any>(null);
  const [hotspots, setHotspots] = useState<any[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Map<string, string>>(new Map());
  const [currentInputValues, setCurrentInputValues] = useState<Map<string, string>>(new Map());

  // Tool state
  const [selectedTool, setSelectedTool] = useState('select');
  const [selectedHotspotId, setSelectedHotspotId] = useState(null);

  // Transform hotspots from API (pixel coords) to frontend format (normalized coords)
  const transformHotspotsFromAPI = (hotspots: any[], imageWidth: number, imageHeight: number) => {
    if (!hotspots || !imageWidth || !imageHeight) return [];

    return hotspots.map((hotspot: any) => {
      const hasVideoMapping = hotspot.has_video_mapping === 1 || hotspot.has_video_mapping === true;
      
      const videoMapping = hasVideoMapping && hotspot.video_mapping_id
        ? {
            id: hotspot.video_mapping_id,
            video_url: hotspot.video_url || '',
            is_enabled: true,
            priority: hotspot.mapping_priority || 10,
            title: hotspot.title || undefined,
            description: hotspot.description || undefined,
          }
        : null;

      return {
        ...hotspot,
        id: hotspot.id || `hotspot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        coords_json: {
          x: hotspot.coords_json.x / imageWidth,
          y: hotspot.coords_json.y / imageHeight,
          width: hotspot.coords_json.width / imageWidth,
          height: hotspot.coords_json.height / imageHeight
        },
        has_video_mapping: hasVideoMapping,
        video_mapping: videoMapping
      };
    });
  };

  const validateAllHotspots = useCallback((): Map<string, string> => {
    const errors = new Map<string, string>();
    
    hotspots.forEach((h) => {
      const hasCurrentInput = currentInputValues.has(h.id);
      const elementId = hasCurrentInput 
        ? currentInputValues.get(h.id)?.trim() || ''
        : h.element_id?.trim() || '';
      
      if (!elementId) {
        errors.set(h.id, 'Element ID is required');
        return;
      }
      
      if (!/^[a-zA-Z0-9_-]+$/.test(elementId)) {
        errors.set(h.id, 'Element ID can only contain letters, numbers, hyphens, and underscores');
        return;
      }
    });

    const elementIdToHotspotId = new Map<string, string[]>();
    hotspots.forEach((h) => {
      const hasCurrentInput = currentInputValues.has(h.id);
      const elementId = hasCurrentInput 
        ? currentInputValues.get(h.id)?.trim() || ''
        : h.element_id?.trim() || '';
      if (elementId) {
        if (!elementIdToHotspotId.has(elementId)) {
          elementIdToHotspotId.set(elementId, []);
        }
        elementIdToHotspotId.get(elementId)!.push(h.id);
      }
    });

    elementIdToHotspotId.forEach((hotspotIds, elementId) => {
      if (hotspotIds.length > 1) {
        hotspotIds.forEach((hotspotId) => {
          errors.set(hotspotId, 'Element ID must be unique within this screen');
        });
      }
    });

    if (allScreensData && Array.isArray(allScreensData)) {
      const otherScreensElementIds = new Set<string>();
      
      allScreensData.forEach((screen: any) => {
        if (screen.id !== id && screen.hotspots) {
          screen.hotspots.forEach((h: any) => {
            if (h.element_id && h.element_id.trim()) {
              otherScreensElementIds.add(h.element_id.trim());
            }
          });
        }
      });
      
      hotspots.forEach((h) => {
        const hasCurrentInput = currentInputValues.has(h.id);
        const elementId = hasCurrentInput 
          ? currentInputValues.get(h.id)?.trim() || ''
          : h.element_id?.trim() || '';
        if (elementId && otherScreensElementIds.has(elementId)) {
          errors.set(h.id, 'Element ID must be unique across all screens');
        }
      });
    }

    return errors;
  }, [hotspots, allScreensData, id, currentInputValues]);

  useEffect(() => {
    if (screenData) {
      console.log('🔍 Screen data from API:', screenData);
      console.log('🔍 Raw hotspots from API:', screenData.hotspots);
      console.log('🔍 Image dimensions:', {
        width: screenData.natural_width,
        height: screenData.natural_height
      });

      setScreen(screenData);

      const transformedHotspots = transformHotspotsFromAPI(
        screenData.hotspots || [],
        screenData.natural_width || 1920,
        screenData.natural_height || 1080
      );

      console.log('🔄 Transformed hotspots for frontend:', transformedHotspots);

      setHotspots(transformedHotspots);
      setHasUnsavedChanges(false);
    }
  }, [screenData]);

  useEffect(() => {
    if (hotspots.length > 0 || (allScreensData && Array.isArray(allScreensData))) {
      const errors = validateAllHotspots();
      setValidationErrors(errors);
    } else if (hotspots.length === 0) {
      setValidationErrors(new Map());
    }
  }, [validateAllHotspots, hotspots, allScreensData, currentInputValues]);

  const handleInputValueChange = (hotspotId: string, value: string) => {
    setCurrentInputValues(prev => {
      const newMap = new Map(prev);
      newMap.set(hotspotId, value);
      return newMap;
    });
  };

  const handleHotspotsChange = (newHotspots: any[]) => {
    setHotspots(newHotspots);
    setHasUnsavedChanges(true);
    setSaveError(null);
  };

  // Handle hotspot selection
  const handleHotspotSelect = (hotspotId: any) => {
    if (selectedHotspotId && selectedHotspotId !== hotspotId) {
      setCurrentInputValues(prev => {
        const newMap = new Map(prev);
        newMap.delete(selectedHotspotId);
        return newMap;
      });
    }
    setSelectedHotspotId(hotspotId);
    setSelectedTool('select');
  };

  // Handle hotspot update
  const handleHotspotUpdate = (hotspotId: any, updates: any) => {
    const processedUpdates = { ...updates };
    if (processedUpdates.element_id !== undefined) {
      processedUpdates.element_id = processedUpdates.element_id.trim();
    }
    
    const updatedHotspots = hotspots.map(h =>
      h.id === hotspotId ? { ...h, ...processedUpdates } : h
    );
    setHotspots(updatedHotspots);
    setHasUnsavedChanges(true);
    setSaveError(null);
  };

  // Handle hotspot delete
  const handleHotspotDelete = async (hotspotId: string) => {
    try {
      // Check if this is a frontend-only hotspot (no backend ID)
      const hotspot = hotspots.find(h => h.id === hotspotId);
      const isFrontendOnly = !hotspot?.id || hotspot.id.startsWith('hotspot_');
      
      if (!isFrontendOnly) {
        // Delete from backend first
        await deleteHotspotMutation.mutateAsync({ hotspotId });
      }
      
      const updatedHotspots = hotspots.filter(h => h.id !== hotspotId);
      setHotspots(updatedHotspots);
      setHasUnsavedChanges(true);
      setSaveError(null);
      
      setCurrentInputValues(prev => {
        const newMap = new Map(prev);
        newMap.delete(hotspotId);
        return newMap;
      });
      
      if (selectedHotspotId === hotspotId) {
        setSelectedHotspotId(null);
      }
      
      showNotification('Hotspot deleted', 'success');
    } catch (error) {
      console.error('Failed to delete hotspot:', error);
      showNotification('Failed to delete hotspot. Please try again.', 'error');
    }
  };

  // Transform hotspots for backend API
  const transformHotspotsForAPI = (hotspots: any[], imageWidth: number, imageHeight: number) => {
    return hotspots.map((hotspot: any) => {
      // Convert normalized coordinates to pixel coordinates
      const pixelCoords = {
        x: Math.round(hotspot.coords_json.x * imageWidth),
        y: Math.round(hotspot.coords_json.y * imageHeight),
        width: Math.round(hotspot.coords_json.width * imageWidth),
        height: Math.round(hotspot.coords_json.height * imageHeight)
      };

      const elementId = hotspot.element_id ? hotspot.element_id.trim() : "";

      // Return only the fields the backend expects
      const hasBackendId = hotspot.id && !hotspot.id.startsWith("hotspot_");

      const payload: any = {
        element_id: elementId,
        shape_type: hotspot.shape_type,
        coords_json: pixelCoords,
        is_enabled: hotspot.is_enabled ? 1 : 0,
      };

      if (hasBackendId) {
        payload.id = hotspot.id;
      }

      return payload;
    });
  };

  const validateHotspotsBeforeSave = () => {
    const errors = validateAllHotspots();
    
    if (errors.size > 0) {
      const hotspotsWithoutElementId = hotspots.filter(
        h => !h.element_id || !h.element_id.trim()
      );

      if (hotspotsWithoutElementId.length > 0) {
        const count = hotspotsWithoutElementId.length;
        return `Cannot save: ${count} hotspot${count > 1 ? 's' : ''} ${count > 1 ? 'are' : 'is'} missing an Element ID. Please add Element IDs to all hotspots before saving.`;
      }

      // Check for duplicate error messages
      const duplicateErrors = Array.from(errors.values()).filter(
        msg => msg.includes('unique')
      );
      
      if (duplicateErrors.length > 0) {
        return `Duplicate Element IDs found. Each hotspot must have a unique Element ID.`;
      }

      return 'Cannot save: Please fix all validation errors before saving.';
    }

    return null;
  };

  const handleSave = async () => {
    setSaveError(null);

    const errors = validateAllHotspots();
    if (errors.size > 0) {
      setValidationErrors(errors);
      const validationError = validateHotspotsBeforeSave();
      setSaveError(validationError || 'Please fix all validation errors before saving.');
      showNotification(validationError || 'Please fix all validation errors before saving.', 'error');
      return;
    }

    // Double-check with validateHotspotsBeforeSave
    const validationError = validateHotspotsBeforeSave();
    if (validationError) {
      setSaveError(validationError);
      showNotification(validationError, 'error');
      return;
    }

    try {
      const transformedHotspots = transformHotspotsForAPI(
        hotspots,
        screen?.natural_width || 1920,
        screen?.natural_height || 1080
      );

      await saveHotspotsMutation.mutateAsync({
        screenId: id!,
        hotspots: transformedHotspots
      });
      await refetch();
      setHasUnsavedChanges(false);
      setSaveError(null);
      setValidationErrors(new Map());
      setCurrentInputValues(new Map());
      showNotification('Hotspots saved successfully!', 'success');
    } catch (err) {
      console.log('Failed to save hotspots:', err);
      const errorMessage = 'Failed to save hotspots. Please try again.';
      setSaveError(errorMessage);
      showNotification(errorMessage, 'error');
    }
  };

  // Zoom controls
  const handleZoomIn = () => {
    if (stageRef.current) {
      const stage = stageRef.current;
      const oldScale = stage.scaleX();
      const newScale = Math.min(oldScale * 1.2, 3);
      stage.scale({ x: newScale, y: newScale });
      stage.batchDraw();
    }
  };

  const handleZoomOut = () => {
    if (stageRef.current) {
      const stage = stageRef.current;
      const oldScale = stage.scaleX();
      const newScale = Math.max(oldScale / 1.2, 0.1);
      stage.scale({ x: newScale, y: newScale });
      stage.batchDraw();
    }
  };

  const handleResetView = () => {
    if (stageRef.current) {
      const stage = stageRef.current;
      stage.scale({ x: 1, y: 1 });
      stage.position({ x: 0, y: 0 });
      stage.batchDraw();
    }
  };

  const selectedHotspot = hotspots.find(h => h.id === selectedHotspotId);

  // Navigation warning for unsaved changes
  const { handleNavigation: handleSafeNavigation, dialogOpen, message, handleConfirm, handleCancel } = useSafeNavigation(
    hasUnsavedChanges,
    () => navigate(`/${routePaths.adminVideoTooltipScreens}`),
    'You have unsaved hotspot changes. Are you sure you want to leave without saving?'
  );

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (screenError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">Failed to load screen. Please try again.</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          backgroundColor: "white",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: "bold",
              color: "#0d1b2a",
              fontSize: "24px",
            }}
          >
            Hotspots — {screen?.name}
          </Typography>
          {hasUnsavedChanges && (
            <Typography
              variant="caption"
              sx={{
                color: "#ff9800",
                backgroundColor: "rgba(255, 152, 0, 0.1)",
                px: 1,
                py: 0.5,
                borderRadius: 1,
              }}
            >
              Unsaved changes
            </Typography>
          )}
        </Box>
        <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
          <Link
            component="button"
            variant="body2"
            onClick={handleSafeNavigation}
            sx={{
              color: "#415a77",
              textDecoration: "none",
              display: "inline-flex",
              alignItems: "center",
              "&:hover": {
                textDecoration: "underline",
              },
            }}
          >
            <ArrowBackIcon sx={{ fontSize: 16, mr: 1 }} />
            Back to Screens
          </Link>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={
              saveHotspotsMutation.isLoading ||
              deleteHotspotMutation.isLoading ||
              !hasUnsavedChanges ||
              validationErrors.size > 0 ||
              hotspots.length === 0 ||
              hotspots.some((h) => {
                const hasCurrentInput = currentInputValues.has(h.id);
                const elementId = hasCurrentInput
                  ? currentInputValues.get(h.id)?.trim() || ""
                  : h.element_id?.trim() || "";
                return !elementId;
              })
            }
          >
            {saveHotspotsMutation.isLoading ? "Saving..." : "Save"}
          </Button>
        </Box>
      </Paper>

      {/* Content */}
      <Box sx={{ flex: 1, mt: 2 }}>
        {/* Toolbar */}
        <HotspotToolbar
          selectedTool={selectedTool}
          onToolChange={setSelectedTool}
          onZoomIn={handleZoomIn}
          onZoomOut={handleZoomOut}
          onResetView={handleResetView}
          onDeleteSelected={() =>
            selectedHotspotId && handleHotspotDelete(selectedHotspotId)
          }
          hasSelection={!!selectedHotspotId}
          disabled={
            loading ||
            saveHotspotsMutation.isLoading ||
            deleteHotspotMutation.isLoading
          }
        />

        {/* Main Content Grid */}
        <Grid container spacing={3}>
          {/* Canvas Area */}
          <Grid item xs={12} md={8}>
             <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
              }}
            >
              <HotspotCanvas
                screen={screen}
                hotspots={hotspots}
                onHotspotsChange={handleHotspotsChange}
                selectedTool={selectedTool}
                selectedHotspotId={selectedHotspotId}
                onHotspotSelect={handleHotspotSelect}
                stageRef={stageRef}
              />
              <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                <Tooltip
                  title={`${
                    hotspots.filter((h) => h.element_id).length
                  } hotspot${hotspots.filter((h) => h.element_id).length !== 1 ? 's' : ''} ${hotspots.filter((h) => h.element_id).length !== 1 ? 'are' : 'is'} configured`}
                >
                  <Chip
                    label={`${
                      hotspots.filter((h) => h.element_id).length
                    } configured`}
                    size="small"
                    color="success"
                    variant="outlined"
                  />
                </Tooltip>
                <Tooltip
                  title={`${
                    hotspots.filter((h) => !h.element_id).length
                  } hotspot${hotspots.filter((h) => !h.element_id).length !== 1 ? 's' : ''} need${hotspots.filter((h) => !h.element_id).length !== 1 ? '' : 's'} Element ID`}
                >
                  <Chip
                    label={`${
                      hotspots.filter((h) => !h.element_id).length
                    } need ID`}
                    size="small"
                    color="warning"
                    variant="outlined"
                  />
                </Tooltip>
                <Tooltip
                  title={`${
                    hotspots.filter((h) => h.has_video_mapping).length
                  } hotspot${hotspots.filter((h) => h.has_video_mapping).length !== 1 ? 's' : ''} ${hotspots.filter((h) => h.has_video_mapping).length !== 1 ? 'have' : 'has'} video mapping${hotspots.filter((h) => h.has_video_mapping).length !== 1 ? 's' : ''}`}
                >
                  <Chip
                    label={`${
                      hotspots.filter((h) => h.has_video_mapping).length
                    } have videos`}
                    size="small"
                    color="info"
                    variant="outlined"
                  />
                </Tooltip>
              </Box>
            </Box>
          </Grid>

          {/* Properties Panel */}
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
              <HotspotProperties
                selectedHotspot={selectedHotspot}
                hotspots={hotspots}
                allScreens={allScreensData || []}
                currentScreenId={id || ""}
                onHotspotUpdate={handleHotspotUpdate}
                onHotspotDelete={handleHotspotDelete}
                disabled={
                  loading ||
                  saveHotspotsMutation.isLoading ||
                  deleteHotspotMutation.isLoading
                }
                saveError={saveError}
                validationError={
                  selectedHotspot
                    ? validationErrors.get(selectedHotspot.id) || undefined
                    : undefined
                }
                onInputValueChange={handleInputValueChange}
              />

              <HotspotsList
                hotspots={hotspots}
                selectedHotspotId={selectedHotspotId}
                onHotspotSelect={handleHotspotSelect}
                onHotspotDelete={handleHotspotDelete}
                disabled={
                  loading ||
                  saveHotspotsMutation.isLoading ||
                  deleteHotspotMutation.isLoading
                }
              />
            </Box>
          </Grid>
        </Grid>
      </Box>
      <NavigationWarningDialog
        open={dialogOpen}
        message={message}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
    </Box>
  );
};

export default HotspotsPage;

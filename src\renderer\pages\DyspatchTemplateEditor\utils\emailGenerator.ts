import type { BlockContent, EmailSettings, Variable } from '../types';

export function generateEmailHtml(
  blocks: BlockContent[],
  settings: EmailSettings,
  variables: Variable[],
  collections: Record<string, any[]> = {},
  forPreview: boolean = false
): string {
  if (blocks.length === 1 && blocks[0].type === 'html') {
    const fullHtmlContent = blocks[0].content;

    if (fullHtmlContent.includes('<!DOCTYPE') || fullHtmlContent.includes('<html')) {
      return processVariables(fullHtmlContent, variables, collections, forPreview);
    }

    return processVariables(
      wrapInEmailTemplate(fullHtmlContent, settings),
      variables,
      collections,
      forPreview
    );
  }

  const processedBlocks = blocks.map(block => generateBlockHtml(block, settings)).join('\n');

  const processedHtml = processVariables(
    wrapInEmailTemplate(processedBlocks, settings),
    variables,
    collections,
    forPreview
  );

  return processedHtml;
}

export function generateBlockHtml(block: BlockContent, settings: EmailSettings): string {
  const styles = block.styles || {};

  switch (block.type) {
    case 'heading1':
      return `
        <tr>
          <td align="${styles.textAlign || 'left'}" style="padding: ${styles.padding || '10px 0'};">
            <h1 style="margin: 0; font-family: ${settings.fontFamily}, sans-serif; font-size: ${styles.fontSize || '32px'}; color: ${styles.color || '#333333'}; font-weight: 700;">
              ${block.content}
            </h1>
          </td>
        </tr>`;

    case 'heading2':
      return `
        <tr>
          <td align="${styles.textAlign || 'left'}" style="padding: ${styles.padding || '10px 0'};">
            <h2 style="margin: 0; font-family: ${settings.fontFamily}, sans-serif; font-size: ${styles.fontSize || '24px'}; color: ${styles.color || '#333333'}; font-weight: 600;">
              ${block.content}
            </h2>
          </td>
        </tr>`;

    case 'heading3':
      return `
        <tr>
          <td align="${styles.textAlign || 'left'}" style="padding: ${styles.padding || '10px 0'};">
            <h3 style="margin: 0; font-family: ${settings.fontFamily}, sans-serif; font-size: ${styles.fontSize || '18px'}; color: ${styles.color || '#333333'}; font-weight: 600;">
              ${block.content}
            </h3>
          </td>
        </tr>`;

    case 'paragraph':
      return `
        <tr>
          <td align="${styles.textAlign || 'left'}" style="padding: ${styles.padding || '10px 0'};">
            <p style="margin: 0; font-family: ${settings.fontFamily}, sans-serif; font-size: ${styles.fontSize || '14px'}; line-height: 1.6; color: ${styles.color || '#555555'};">
              ${block.content}
            </p>
          </td>
        </tr>`;

    case 'button':
      return generateButtonHtml(block, settings);

    case 'image':
      return `
        <tr>
          <td align="${styles.textAlign || 'center'}" style="padding: ${styles.padding || '10px 0'};">
            <img src="${block.content}" alt="${block.attributes?.alt || ''}" style="display: block; max-width: 100%; height: auto; ${styles.width ? `width: ${styles.width};` : ''}" />
          </td>
        </tr>`;

    case 'spacer':
      return `
        <tr>
          <td height="${styles.height || '20'}" style="font-size: 0; line-height: 0;">
            &nbsp;
          </td>
        </tr>`;

    case 'divider':
      return `
        <tr>
          <td style="padding: ${styles.padding || '20px 0'};">
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td style="border-top: ${styles.border || '1px solid #e0e0e0'};"></td>
              </tr>
            </table>
          </td>
        </tr>`;

    case 'html':
      return `
        <tr>
          <td>
            ${block.content}
          </td>
        </tr>`;

    default:
      return '';
  }
}

function generateButtonHtml(block: BlockContent, settings: EmailSettings): string {
  const styles = block.styles || {};
  const href = block.attributes?.href || '#';
  const backgroundColor = styles.backgroundColor || '#007bff';
  const color = styles.color || '#ffffff';
  const padding = styles.padding || '12px 30px';
  const borderRadius = styles.borderRadius || '4px';

  return `
    <tr>
      <td align="${styles.textAlign || 'center'}" style="padding: ${styles.margin || '20px 0'};">
        <!--[if mso]>
        <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${href}" style="height:40px;v-text-anchor:middle;width:200px;" arcsize="${parseInt(borderRadius)}%" stroke="f" fillcolor="${backgroundColor}">
        <w:anchorlock/>
        <center>
        <![endif]-->
        <a href="${href}" style="background-color: ${backgroundColor}; border-radius: ${borderRadius}; color: ${color}; display: inline-block; font-family: ${settings.fontFamily}, sans-serif; font-size: ${styles.fontSize || '16px'}; font-weight: bold; padding: ${padding}; text-decoration: none; text-transform: none;">
          ${block.content}
        </a>
        <!--[if mso]>
        </center>
        </v:roundrect>
        <![endif]-->
      </td>
    </tr>`;
}

export function wrapInEmailTemplate(content: string, settings: EmailSettings): string {
  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
<title>${settings.subjectLine || settings.templateName}</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="x-apple-disable-message-reformatting" />
{% if subject %}{% assign subject = "${settings.subjectLine}" %}{% endif %}
<!--[if !mso]><!-->
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<!--<![endif]-->
<style type="text/css">
  body { margin: 0; padding: 0; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
  table, td { border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
  img { border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; }
  p { display: block; margin: 13px 0; }
  a { color: #007bff; text-decoration: underline; }

  @media only screen and (max-width: 620px) {
    table.responsive { width: 100% !important; }
    td.responsive { width: auto !important; }
    img.responsive { width: 100% !important; max-width: 100% !important; height: auto !important; }
    .mobile-hide { display: none !important; }
    .mobile-center { text-align: center !important; }
  }
</style>
<!--[if mso]>
<xml>
<o:OfficeDocumentSettings>
<o:AllowPNG/>
<o:PixelsPerInch>96</o:PixelsPerInch>
</o:OfficeDocumentSettings>
</xml>
<![endif]-->
</head>
<body style="margin: 0; padding: 0; background-color: ${settings.emailBackgroundColor};">
<table role="presentation" width="100%" border="0" cellspacing="0" cellpadding="0" style="background-color: ${settings.emailBackgroundColor};">
  <tr>
    <td align="center" style="padding: 20px 0;">
      <!--[if mso]>
      <table role="presentation" align="center" border="0" cellspacing="0" cellpadding="0" width="${settings.maxWidth}">
      <tr>
      <td>
      <![endif]-->
      <table role="presentation" border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: ${settings.maxWidth}px; background-color: ${settings.contentBackgroundColor};" class="responsive">
        <tr>
          <td style="padding: 20px;">
            <table role="presentation" width="100%" border="0" cellspacing="0" cellpadding="0">
              ${content}
            </table>
          </td>
        </tr>
      </table>
      <!--[if mso]>
      </td>
      </tr>
      </table>
      <![endif]-->
    </td>
  </tr>
</table>
</body>
</html>`;
}

export function processVariables(
  html: string,
  variables: Variable[],
  collections: Record<string, any[]> = {},
  forPreview: boolean
): string {
  let processedHtml = html;

  if (forPreview) {
    const loopRegex = /\{% for (\w+) in (\w+) %\}([\s\S]*?)\{% endfor %\}/g;
    processedHtml = processedHtml.replace(loopRegex, (_match, itemVar, collectionName, loopContent) => {
      const collection = collections[collectionName] || [];

      return collection.map((item, index) => {
        let itemContent = loopContent;

        Object.keys(item).forEach(key => {
          const itemVarRegex = new RegExp(`\\{\\{\\s*${itemVar}\\.${key}\\s*\\}\\}`, 'g');
          itemContent = itemContent.replace(itemVarRegex, item[key]);
        });

        itemContent = itemContent.replace(/\{\{\s*forloop\.index\s*\}\}/g, (index + 1).toString());
        itemContent = itemContent.replace(/\{\{\s*forloop\.index0\s*\}\}/g, index.toString());
        itemContent = itemContent.replace(/\{\{\s*forloop\.first\s*\}\}/g, index === 0 ? 'true' : 'false');
        itemContent = itemContent.replace(/\{\{\s*forloop\.last\s*\}\}/g, index === collection.length - 1 ? 'true' : 'false');

        return itemContent;
      }).join('');
    });

    // Create a map of variable names to their values for quick lookup
    const variableMap: Record<string, string> = {};
    variables.forEach(variable => {
      variableMap[variable.name] = variable.defaultValue || `[${variable.name}]`;
    });

    // First, replace {{variableName}} with their values
    variables.forEach(variable => {
      const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g');
      processedHtml = processedHtml.replace(regex, variableMap[variable.name]);
    });

    // Helper function to evaluate conditionals with nested support
    const evaluateConditionals = (html: string, variableMap: Record<string, string>): string => {
      // Pattern to match {% if ... %} tags (both with and without braces)
      const ifTagRegex = /\{%\s*if\s*(?:\{(\w+)\}|(\w+))\s*==\s*"([^"]+)"\s*%\}/g;
      const endifTag = '{% endif %}';
      
      let result = html;
      let maxIterations = 100; // Prevent infinite loops
      let iteration = 0;
      
      // Process conditionals iteratively from innermost to outermost
      while (iteration < maxIterations) {
        iteration++;
        let foundAny = false;
        
        // Find all {% if %} tags
        const ifMatches: Array<{ index: number; length: number; varName: string; expectedValue: string }> = [];
        let match;
        
        // Reset regex lastIndex
        ifTagRegex.lastIndex = 0;
        
        while ((match = ifTagRegex.exec(result)) !== null) {
          const varName = match[1] || match[2]; // Handle both {var} and var syntax
          const expectedValue = match[3];
          ifMatches.push({
            index: match.index,
            length: match[0].length,
            varName,
            expectedValue
          });
        }
        
        if (ifMatches.length === 0) {
          // No more conditionals to process
          break;
        }
        
        // Process from rightmost (innermost) to leftmost
        for (let i = ifMatches.length - 1; i >= 0; i--) {
          const ifMatch = ifMatches[i];
          const ifStart = ifMatch.index;
          const ifEnd = ifStart + ifMatch.length;
          
          // Find the matching {% endif %} by tracking nesting depth
          let depth = 1;
          let endifIndex = -1;
          let searchPos = ifEnd;
          
          while (searchPos < result.length && depth > 0) {
            const nextIf = result.indexOf('{% if', searchPos);
            const nextEndif = result.indexOf(endifTag, searchPos);
            
            if (nextEndif === -1) {
              // No matching endif found
              break;
            }
            
            if (nextIf !== -1 && nextIf < nextEndif) {
              // Found nested if, increase depth
              depth++;
              searchPos = nextIf + 5;
            } else {
              // Found endif, decrease depth
              depth--;
              if (depth === 0) {
                endifIndex = nextEndif;
                break;
              }
              searchPos = nextEndif + endifTag.length;
            }
          }
          
          if (endifIndex === -1) {
            // No matching endif, skip this conditional
            continue;
          }
          
          // Extract content between {% if %} and {% endif %}
          const content = result.substring(ifEnd, endifIndex);
          const endifEnd = endifIndex + endifTag.length;
          
          // Evaluate the condition
          const actualValue = variableMap[ifMatch.varName] || '';
          const fallbackValue = `[${ifMatch.varName}]`;
          
          let replacement = '';
          if (actualValue === fallbackValue || actualValue === '') {
            // No value set, show the content (Option 1)
            replacement = content;
          } else if (actualValue.trim() === ifMatch.expectedValue.trim()) {
            // Condition is true, keep the content
            replacement = content;
          } else {
            // Condition is false, remove the content
            replacement = '';
          }
          
          // Replace the entire conditional block
          result = result.substring(0, ifStart) + replacement + result.substring(endifEnd);
          foundAny = true;
          
          // Break to restart search (positions have changed)
          break;
        }
        
        if (!foundAny) {
          // No more conditionals could be processed
          break;
        }
      }
      
      return result;
    };

    // Evaluate conditionals with nested support
    processedHtml = evaluateConditionals(processedHtml, variableMap);

    // Remove any remaining template tags that weren't processed
    processedHtml = processedHtml.replace(/\{%[^%]*%\}/g, '');
  }

  return processedHtml;
}

/**
 * Processes variables in a string (like subject line) by replacing {{variableName}} with their values
 * This is used for processing subject lines and other text fields when sending emails
 */
export function processVariablesInString(
  text: string,
  variables: Variable[],
  forPreview: boolean = false
): string {
  if (!forPreview || !text) {
    return text;
  }

  // Create a map of variable names to their values
  const variableMap: Record<string, string> = {};
  variables.forEach(variable => {
    variableMap[variable.name] = variable.defaultValue || `[${variable.name}]`;
  });

  // Replace {{variableName}} with their values
  let processedText = text;
  variables.forEach(variable => {
    const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g');
    processedText = processedText.replace(regex, variableMap[variable.name]);
  });

  return processedText;
}

export function extractSubjectFromHtml(html: string): string {
  const titleMatch = html.match(/<title>([^<]*)<\/title>/i);
  return titleMatch ? titleMatch[1] : '';
}

export function parseHtmlToBlocks(html: string): BlockContent[] {
  const blocks: BlockContent[] = [];

  if (html.length > 5000 || html.includes('mso') || html.includes('outlook') ||
      html.includes('table') && html.split('table').length > 10) {
    blocks.push({
      id: `block-${Date.now()}-html`,
      type: 'html',
      content: extractEmailBodyContent(html),
      styles: {}
    });
    return blocks;
  }

  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  const bodyContent = extractMainContentElements(doc);

  bodyContent.forEach((element, index) => {
    const id = `block-${Date.now()}-${index}`;

    if (element.tagName === 'H1') {
      blocks.push({
        id,
        type: 'heading1',
        content: element.textContent || '',
        styles: extractStyles(element, 'heading1')
      });
    } else if (element.tagName === 'H2') {
      blocks.push({
        id,
        type: 'heading2',
        content: element.textContent || '',
        styles: extractStyles(element, 'heading2')
      });
    } else if (element.tagName === 'H3') {
      blocks.push({
        id,
        type: 'heading3',
        content: element.textContent || '',
        styles: extractStyles(element, 'heading3')
      });
    } else if (element.tagName === 'P') {
      const content = element.textContent || '';
      if (content.trim()) {
        blocks.push({
          id,
          type: 'paragraph',
          content,
          styles: extractStyles(element, 'paragraph')
        });
      }
    } else if (element.tagName === 'A' && element.getAttribute('style')?.includes('background')) {
      blocks.push({
        id,
        type: 'button',
        content: element.textContent || '',
        styles: extractStyles(element, 'button'),
        attributes: { href: element.getAttribute('href') || '#' }
      });
    } else if (element.tagName === 'IMG') {
      blocks.push({
        id,
        type: 'image',
        content: element.getAttribute('src') || '',
        styles: extractStyles(element, 'image'),
        attributes: { alt: element.getAttribute('alt') || '' }
      });
    }
  });

  if (blocks.length === 0) {
    const textContent = doc.body.textContent?.trim() || '';
    if (textContent) {
      blocks.push({
        id: `block-${Date.now()}-text`,
        type: 'paragraph',
        content: textContent.substring(0, 500),
        styles: { fontSize: '14px', color: '#555555', textAlign: 'left' }
      });
    }
  }

  return blocks;
}

function extractEmailBodyContent(html: string): string {
  if (html.includes('<!DOCTYPE') || html.includes('<html')) {
    return html;
  }

  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  const tables = doc.querySelectorAll('table[role="presentation"]');
  let contentTable = null;

  for (let i = tables.length - 1; i >= 0; i--) {
    const table = tables[i];
    const hasContent = table.querySelector('h1, h2, h3, p, img, a');
    if (hasContent) {
      contentTable = table;
      break;
    }
  }

  if (contentTable) {
    return contentTable.innerHTML;
  }

  return doc.body.innerHTML || html;
}

function extractMainContentElements(doc: Document): Element[] {
  const elements: Element[] = [];

  const contentSelectors = [
    'table[role="presentation"] td h1, table[role="presentation"] td h2, table[role="presentation"] td h3',
    'table[role="presentation"] td p',
    'table[role="presentation"] td img',
    'table[role="presentation"] td a[style*="background"]'
  ];

  contentSelectors.forEach(selector => {
    const found = doc.querySelectorAll(selector);
    found.forEach(el => {
      if (!elements.includes(el)) {
        elements.push(el);
      }
    });
  });

  if (elements.length === 0) {
    const directElements = doc.querySelectorAll('h1, h2, h3, p, img, a[style*="background"]');
    directElements.forEach(el => elements.push(el));
  }

  return elements;
}

function extractStyles(element: Element, blockType: string): any {
  const styles: any = {};
  const computedStyle = element.getAttribute('style') || '';

  const styleEntries = computedStyle.split(';').filter(s => s.trim());
  styleEntries.forEach(entry => {
    const [property, value] = entry.split(':').map(s => s.trim());
    if (property && value) {
      switch (property) {
        case 'color':
          styles.color = value;
          break;
        case 'font-size':
          styles.fontSize = value;
          break;
        case 'text-align':
          styles.textAlign = value;
          break;
        case 'background-color':
          styles.backgroundColor = value;
          break;
        case 'padding':
          styles.padding = value;
          break;
        case 'margin':
          styles.margin = value;
          break;
        case 'border-radius':
          styles.borderRadius = value;
          break;
        case 'width':
          styles.width = value;
          break;
        case 'height':
          styles.height = value;
          break;
      }
    }
  });

  if (Object.keys(styles).length === 0) {
    switch (blockType) {
      case 'heading1':
        return { fontSize: '32px', color: '#333333', textAlign: 'left' };
      case 'heading2':
        return { fontSize: '24px', color: '#333333', textAlign: 'left' };
      case 'heading3':
        return { fontSize: '18px', color: '#333333', textAlign: 'left' };
      case 'paragraph':
        return { fontSize: '14px', color: '#555555', textAlign: 'left' };
      case 'button':
        return { backgroundColor: '#007bff', color: '#ffffff', textAlign: 'center', padding: '12px 30px', borderRadius: '4px' };
      default:
        return {};
    }
  }

  return styles;
}

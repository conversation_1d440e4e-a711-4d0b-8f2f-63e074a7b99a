import { useEffect, useState } from "react";
import useApproveRejectResaleCertficate from "../../../hooks/useApproveRejectResaleCertficate";
import useGetAllCertificates from "../../../hooks/useGetAllCertificates";
import { v4 as uuidv4 } from "uuid";
import MatPopup from "../../../components/common/MatPopup";
import Loader from "../../../components/common/Loader/Loader";
import styles from "./ApproveReject.module.scss";
import { useImmer } from "use-immer";
import { filterArrray } from "../../../utils/helper";
import { Select, MenuItem, Tooltip } from "@mui/material";
import ReactPaginate from "react-paginate";
import useDialogStore from "../../../components/common/DialogPopup/DialogStore";
import { confirmationPopupKeys} from "../../../utils/constant";
import SearchBar from "../../../components/common/SearchBox/SearchBox";
import clsx from "clsx";

const ApproveReject = () => {
  const [filteredaCertificates, setFilteredaCertificates] = useImmer([]);

  const [apiResponseMessage, setApiResponseMessage] = useState("");
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const [showApproveRejectPopup, setShowApproveRejectPopup] = useState(false);
  const [isApproveToCompanyLevel, setIsApproveToCompanyLevel] = useState(false);
  const [selectedCertificate, setSelectedCertificate] = useState(null);
  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredaCertificates.length / itemsPerPage);

  const {
    data: allCertificates,
    isLoading: isAllCertificatesLoading,
    isFetching: isAllCertificatesFetching,
  } = useGetAllCertificates();
  const {
    mutate: approveRejectResaleCertficate,
    data: resaleCertficateData,
    isLoading: isApproveRejectResaleCertficateLoading,
    error: approveRejectResaleCertficateError,
  } = useApproveRejectResaleCertficate();

  useEffect(() => {
    if(allCertificates?.length > 0 && inputSearchValue.length !== 0){
      search(inputSearchValue)
    } else if (allCertificates) {
      setFilteredaCertificates(allCertificates);
    } else {
      setFilteredaCertificates([]);
    }
  }, [allCertificates, inputSearchValue]);

  useEffect(() => {
    if (resaleCertficateData && !isApproveRejectResaleCertficateLoading) {
      if (approveRejectResaleCertficateError) {
        setApiResponseMessage(
          (approveRejectResaleCertficateError as Error).message
        );
      } else {
        setApiResponseMessage("Success");
      }
    }
  }, [
    resaleCertficateData,
    isApproveRejectResaleCertficateLoading,
    approveRejectResaleCertficateError
  ]);
  
  useEffect(() => {
    setCurrentPage(0);
    setItemOffset(0);
  }, [itemsPerPage]);

  const search = (searchString: string) => {
    setCurrentPage(0);   
    setItemOffset(0);
    setInputSearchValue(searchString);
    if (searchString) {
      const _filterArrray = filterArrray(allCertificates,searchString.trim(), [
        "buyer_name",
        "approval_status",
        "certificate_status",
        "expiry_limit",
        "state",
        "added_by",
        "added_date",
      ]);
      if (_filterArrray?.length) {
        setFilteredaCertificates(_filterArrray);
      } else {
        setFilteredaCertificates([]);
      }
    } else {
      setFilteredaCertificates(allCertificates ? allCertificates : []);
    }
  };

  const certificateDownloadHandler = (s3CerificateUrl: string) => {
    const name = s3CerificateUrl.substring(s3CerificateUrl.lastIndexOf("/"));
    const a = document.createElement("a");
    a.href = s3CerificateUrl;
    a.download = name;
    a.click();
  };

  const onClickApproveHandler = (certificate: any) => {
    approveRejectResaleCertficate({
      id: certificate.resales_cert_id,
      approve: true,
      is_applied_to_all: isApproveToCompanyLevel,
    });
  };

  const onClickRejectHandler = (certificate: any) => {
    approveRejectResaleCertficate({
      id: certificate.resales_cert_id,
      approve: false,
    });
  };

  const handlePageClick = (event: any) => {
    const newOffset =
      (event.selected * itemsPerPage) % filteredaCertificates.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  const formatStateDisplay = (state: any) => {
    if (!state) return "";
    
    // Handle both array and string cases
    const stateArray = Array.isArray(state) ? state : [state];
    const stateString = stateArray.filter(Boolean).join(", ");
    
    return stateString;
  };
  const showResaleCertApprovePopup = (certificate: any) => {
    setShowApproveRejectPopup(true)
    setSelectedCertificate(certificate)
   }

  const handleResaleCertApprovePopupOpen = () => {
    onClickApproveHandler(selectedCertificate)
    closeResaleCertApprovePopup()
  };

  const showResaleCertRejectPopup = (certificate: any) => {
    showCommonDialog(null, confirmationPopupKeys.confirmationContent, null, resetDialogStore, 
      [{name: confirmationPopupKeys.confirmation.yes, action: ()=>{resaleCertRejectPopupOpen(certificate)}},{name: confirmationPopupKeys.confirmation.no, action: resetDialogStore}])
   }

  const resaleCertRejectPopupOpen = (certificate: any) => {
    onClickRejectHandler(certificate);
    resetDialogStore()
  }
  
  const closeResaleCertApprovePopup = () => {
    setShowApproveRejectPopup(false);
    setSelectedCertificate(null);
    setIsApproveToCompanyLevel(false);
  }

  return (
    <div>
      {isAllCertificatesLoading ||
      isAllCertificatesFetching ||
      isApproveRejectResaleCertficateLoading ? (
        <div className={styles.loader}>
          <div className={styles.loaderImg}>
            <Loader />
          </div>
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>

              <SearchBar
                value={inputSearchValue}
                placeholder={'Search'}
                onChange={(event)=>search(event.target.value)}
                onClear={()=> {setInputSearchValue('')}}
              />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>Buyer Name</th>
                  <th>Certificate Status</th>
                  <th>Expiry Limit</th>
                  <th>State</th>
                  <th>Added By</th>
                  <th>Added Date</th>
                  <th colSpan={3}></th>
                </tr>
              </thead>
              <tbody>
                {filteredaCertificates?.length ? (
                  filteredaCertificates
                    .slice(itemOffset, endOffset)
                    .map((certificate: any) => (
                      <tr key={certificate.resales_cert_id}>
                        <td>{certificate.buyer_name}</td>
                        <td>{certificate.certificate_status}</td>
                        <td>{certificate.expiry_limit}</td>
                        <td>
                          <Tooltip 
                            title={formatStateDisplay(certificate?.state)}
                            componentsProps={{
                              tooltip: {
                                sx: {
                                  fontSize: '14px',
                                  padding: '8px 12px',                                  
                                  color: '#fff',
                                  borderRadius: '4px',
                                  maxWidth: '300px',
                                  wordWrap: 'break-word',
                                  whiteSpace: 'pre-wrap'
                                }
                              }
                            }}
                          >
                            <div className={styles.stateText}>
                              {formatStateDisplay(certificate?.state)}
                            </div>
                          </Tooltip>
                        </td>
                        <td>{certificate.added_by}</td>
                        <td>{certificate.added_date}</td>
                        <td>
                          {(certificate.certificate_status === "Pending" ||
                            certificate.certificate_status === "Rejected") && (
                            <button
                              className={styles.approvalBtn}
                              onClick={() => showResaleCertApprovePopup(certificate)}
                            >
                              Approve
                            </button>
                          )}
                        </td>
                        <td>
                          {(certificate.certificate_status === "Pending" ||
                            certificate.certificate_status === "Approved") && (
                            <button
                              className={styles.rejectBtn}
                              onClick={() => showResaleCertRejectPopup(certificate)}
                            >
                              Reject
                            </button>
                          )}
                        </td>
                        <td colSpan={2}>
                          <button
                            className={styles.approvalBtn}
                            onClick={() =>
                              certificateDownloadHandler(
                                certificate.cerificate_url_s3
                              )
                            }
                          >
                            Download
                          </button>
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={7} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
        </div>
      )}
      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyPop}>
          <div
            className={styles.successfullytext}
            dangerouslySetInnerHTML={{ __html: apiResponseMessage }}
          ></div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        classes={
          {
            paper: styles.approveRejectPopupPaper
          }
        }
        open={showApproveRejectPopup}
      >
        <div className={styles.successfullyPop}>
          <div className={styles.title}>{confirmationPopupKeys.confirmationContent}</div>
          <div
            className={styles.successfullytext}
          >
            

            <div className={styles.checkboxMain}>
              <label className={clsx(styles.containerChk, "containerChk")}>
                <input
                  type="checkbox"
                  checked={isApproveToCompanyLevel}
                  onChange={(e) => {
                    setIsApproveToCompanyLevel(e.target.checked)
                  }}
                />
                <span className={clsx(styles.checkmark, "checkmark")} />
              </label>
              Apply to all users in the company
            </div>
          </div>
          <div className={styles.actionBtnSection}>
            <button
              className={styles.submitBtn}
              onClick={() => handleResaleCertApprovePopupOpen()}
            >
              {confirmationPopupKeys.confirmation.yes}
            </button>
            <button
              className={styles.submitBtn}
              onClick={() => closeResaleCertApprovePopup()}
            >
              {confirmationPopupKeys.confirmation.no}
            </button>
          </div>
        </div>
      </MatPopup>
    </div>
  );
};

export default ApproveReject;
